package com.optima.security.repository;

import com.optima.security.model.JwtToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface JwtTokenRepository extends JpaRepository<JwtToken, String> {

    @Modifying
    @Query("delete from JwtToken token where token.expirationTime < current_timestamp ")
    void deleteExpiredTokens();

    List<JwtToken> findAllByClientId(Long clientId);
}
