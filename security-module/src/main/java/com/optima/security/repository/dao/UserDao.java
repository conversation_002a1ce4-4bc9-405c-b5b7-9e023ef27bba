package com.optima.security.repository.dao;

import com.optima.security.model.UserEntity;

import java.util.List;


public interface UserDao {

    UserEntity findByUserName(String username);

    UserEntity findByEmail(String email);

    List<UserEntity> findByToken(String token);

    UserEntity findByUserNameToken(String username, String token);

    List<UserEntity> findAllUsersByEmail(String email);

    void updateUser(String username, String psw, String token, String oldPassword);

    void updateToken(String username, String token);

    void insertToken(String token, Long id);

    void insertDate(Long id);

    void updateEmail(String username, String email);

    void updateDeleted(String username, <PERSON><PERSON><PERSON> deleted);
}
