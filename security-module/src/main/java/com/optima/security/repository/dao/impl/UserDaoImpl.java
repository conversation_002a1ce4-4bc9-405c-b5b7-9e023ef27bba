package com.optima.security.repository.dao.impl;


import com.optima.security.model.UserEntity;
import com.optima.security.repository.dao.UserDao;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.List;


@Repository
@Transactional
public class UserDaoImpl implements UserDao {
    @PersistenceContext
    EntityManager entityManager;

    @SuppressWarnings("unchecked")
    @Override
    public UserEntity findByUserName(String username) {
        Query q = entityManager.createQuery("SELECT u FROM UserEntity u WHERE u.username = :username", UserEntity.class);
        q.setParameter("username", username);
        UserEntity user;
        try {
            user = (UserEntity) q.getSingleResult();
        } catch (NoResultException e) {
            user = null;
        }
        return user;

    }

    @Override
    public UserEntity findByEmail(String email) {
        Query q = entityManager.createQuery("SELECT u FROM UserEntity u WHERE u.email = :email", UserEntity.class);
        q.setParameter("email", email);
        UserEntity user;
        try {
            user = (UserEntity) q.getSingleResult();
        } catch (NoResultException e) {
            user = null;
        }
        return user;
    }

    @Override
    public List<UserEntity> findByToken(String token) {
        Query q = entityManager.createQuery("SELECT u FROM UserEntity u WHERE u.token like :token", UserEntity.class);
        q.setParameter("token", token);
        List<UserEntity> user;
        try {
            user = q.getResultList();
        } catch (NoResultException e) {
            user = null;
        }
        return user;
    }

    @Override
    public UserEntity findByUserNameToken(String username, String token) {
        Query q = entityManager.createQuery("SELECT u FROM UserEntity u WHERE u.token like :token and u.username like :username", UserEntity.class);
        q.setParameter("token", token);
        q.setParameter("username", username);
        UserEntity user;
        try {
            user = (UserEntity) q.getSingleResult();
        } catch (NoResultException e) {
            user = null;
        }
        return user;
    }

    @Override
    public List<UserEntity> findAllUsersByEmail(String email) {
        Query q = entityManager.createQuery("SELECT u FROM UserEntity u WHERE u.email = :email", UserEntity.class);
        q.setParameter("email", email);
        List<UserEntity> user;
        try {
            user = q.getResultList();
        } catch (NoResultException e) {
            user = null;
        }
        return user;
    }

    @Override
    @Transactional
    public void updateUser(String username, String encodePsw, String token, String oldPassword) {
        Query q = entityManager.createQuery("UPDATE UserEntity u Set u.password = :password, u.oldPassword = :oldPassword, u.passwordModifiedDate = CURRENT_TIMESTAMP WHERE u.username LIKE :username AND u.token LIKE :token ");
        q.setParameter("password", encodePsw);
        q.setParameter("username", username);
        q.setParameter("token", token);
        q.setParameter("oldPassword", oldPassword);
        q.executeUpdate();
    }

    @Override
    @Transactional
    public void updateToken(String username, String token) {
        Query q = entityManager.createQuery("UPDATE UserEntity u Set u.token = NULL WHERE u.username LIKE :username AND u.token LIKE :token ");
        q.setParameter("username", username);
        q.setParameter("token", token);
        q.executeUpdate();
    }

    @Override
    @Transactional
    public void insertToken(String token, Long id) {
        Query q = entityManager.createQuery("UPDATE UserEntity u Set u.token = :token WHERE u.id = :id");
        q.setParameter("token", token);
        q.setParameter("id", id);
        q.executeUpdate();
    }

    @Override
    @Transactional
    public void insertDate(Long id) {
        Query q = entityManager.createQuery("UPDATE UserEntity u Set u.last_access = CURRENT_TIMESTAMP WHERE u.id = :id");
        q.setParameter("id", id);
        q.executeUpdate();
    }

    @Override
    @Transactional
    public void updateEmail(String username, String email) {
        Query query = entityManager.createQuery("UPDATE UserEntity u SET u.email =:email WHERE u.username = :username");
        query.setParameter("email", email);
        query.setParameter("username", username);
        query.executeUpdate();
    }

    @Override
    @Transactional
    public void updateDeleted(String username, Boolean deleted) {
        Query query = entityManager.createQuery("UPDATE UserEntity u SET u.deleted =:deleted WHERE u.username = :username");
        query.setParameter("deleted", deleted);
        query.setParameter("username", username);
        query.executeUpdate();
    }
}
