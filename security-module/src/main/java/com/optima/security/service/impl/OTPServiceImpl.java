package com.optima.security.service.impl;

import com.optima.common.models.wrappers.email.EmailMessage;
import com.optima.common.models.wrappers.sms.SMSEntity;
import com.optima.common.services.EmailService;
import com.optima.common.services.SMSService;
import com.optima.security.model.OTPRepositoryModel;
import com.optima.security.model.OTPResponse;
import com.optima.security.model.OTPStatus;
import com.optima.security.model.userData.UserData;
import com.optima.security.model.wrappers.OtpDTO;
import com.optima.security.repository.OTPRepository;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.OTPService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Optional;
import java.util.Random;

@Service
public class OTPServiceImpl implements OTPService {

    private static final Logger logger = LogManager.getLogger(OTPServiceImpl.class);

    @Value("${restdata.urls.userdata}")
    private String userDataUrl;

    @Value("${otp.expiration-time: #{5*60*1000}}")
    private Long passwordExpirationTime;

    private final OTPRepository otpRepository;

    private final SMSService smsService;

    private final EmailService emailService;

    private final AuthenticationService authenticationService;

    @Autowired
    private OTPService otpService;

    public OTPServiceImpl(OTPRepository otpRepository, SMSService smsService,
                          AuthenticationService authenticationService, EmailService emailService) {
        this.otpRepository = otpRepository;
        this.smsService = smsService;
        this.authenticationService = authenticationService;
        this.emailService = emailService;
    }

    @Override
    public String generateOTP(int len) {
        Random random = new Random();
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < len; i++) {
            otp.append(random.nextInt(9));
        }
        return otp.toString();
    }

    @Override
    @Transactional
    public OTPRepositoryModel addOtpForUser(Long userId, String otp, String obtainedBy, String ipAddress) {
        long creationTime = System.currentTimeMillis();
        long expirationTime = creationTime + passwordExpirationTime;
        Timestamp creationTimestamp = new Timestamp(creationTime);
        Timestamp expirationTimestamp = new Timestamp(expirationTime);

        OTPRepositoryModel otpRepositoryModel = new OTPRepositoryModel();
        otpRepositoryModel.setUid(userId);
        otpRepositoryModel.setOtp(otp);
        otpRepositoryModel.setOtpCreationTime(creationTimestamp);
        otpRepositoryModel.setExpirationTime(expirationTimestamp);
        otpRepositoryModel.setOtpObtainedBy(obtainedBy);
        otpRepositoryModel.setIpAddress(ipAddress);
        otpRepositoryModel.setStatus(OTPStatus.NEW);
        return otpRepository.save(otpRepositoryModel);
    }

    @Override
    public Boolean checkOtpForUser(Long userId) {
        Optional o = otpRepository.findById(userId);
        logger.info(o);
        return o.isPresent();
    }

    @Override
    public void removeOtpById(Long userId) {
        try {
            otpRepository.deleteById(userId);
        } catch (Exception e) {
            logger.info("Empty result while deleting otp by id" + userId);
        }
    }

    @Transactional
    public void deleteOTPWithExpiredTokens() {
        otpRepository.deleteExpiredTokens(new Date());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateExpiredPasswords() {
        otpRepository.updateExpiredPasswords(new Timestamp(System.currentTimeMillis()));
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public ResponseEntity<OtpDTO> loadByClientIdAndOtp(Long clientId, String otp) {
        otpService.updateExpiredPasswords();
        return otpRepository.findByUidAndStatus(clientId, OTPStatus.NEW)
                .map(item -> {
                    if (item.getOtp().equals(otp)) {
                        OtpDTO otpDTO = new OtpDTO().setClientId(item.getUid()).setOtp(otp)
                                .setExpirationTime(item.getExpirationTime());
                        item.setStatus(OTPStatus.USED);
                        item.setOtpUsageTime(new Timestamp(System.currentTimeMillis()));
                        otpRepository.save(item);
                        return ResponseEntity.ok(otpDTO);
                    }
                    return null;
                }).orElse(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build());
    }

    @Override
    @Transactional
    public ResponseEntity<OTPResponse> sendOTP(Long userId, String ipAddress) {
        otpService.updateExpiredPasswords();
        UserData userData = authenticationService.getUserData(userId.toString(), userDataUrl);
        String phoneNumber = !StringUtils.isEmpty(userData.getMobileNumber()) ? userData.getMobileNumber() : userData.getPhoneNumber();
        if ((StringUtils.isEmpty(phoneNumber)
                || !(phoneNumber.startsWith("3")
                || !phoneNumber.startsWith("00393"))) && StringUtils.isEmpty(userData.getEmail())) {
            return ResponseEntity.ok(OTPResponse.failed("Nessun numero di telefono e e-mail configurato."));
        }
        OTPResponse otpResponse = otpRepository.findByUidAndStatus(userId, OTPStatus.NEW).map(otp -> {
            long creationTime = System.currentTimeMillis();
            long expirationTime = creationTime + passwordExpirationTime;
            Timestamp creationTimestamp = new Timestamp(creationTime);
            Timestamp expirationTimestamp = new Timestamp(expirationTime);
            otp.setOtpCreationTime(creationTimestamp);
            otp.setExpirationTime(expirationTimestamp);
            otpRepository.save(otp);
            return OTPResponse.success("Expires in " + otp.getExpirationTime());
        }).orElseGet(() -> {
            String otp = generateOTP(4);
            OTPRepositoryModel otpRepositoryModel;
            if (StringUtils.isEmpty(phoneNumber) || !(phoneNumber.startsWith("3") || phoneNumber.startsWith("00393"))) {
                otpRepositoryModel = addOtpForUser(userId, otp, userData.getEmail(), ipAddress);
                sendOTPByEmail(userData.getEmail(), otp);
            } else {
                otpRepositoryModel = addOtpForUser(userId, otp, phoneNumber, ipAddress);
                sendOTPBySMS(phoneNumber, otp);
            }
            return OTPResponse.success("Expires in " + otpRepositoryModel.getExpirationTime());
        });
        return ResponseEntity.ok(otpResponse);
    }

    private Integer sendOTPBySMS(String mobileNumber, String otp) {
        return smsService.sendSMS(new SMSEntity("Gentile Cliente, utilizza il codice "
                + otp + " per confermare la tua operazione.", mobileNumber)).getTransmissionCode();
    }

    private Integer sendOTPByEmail(String email, String otp) {
        EmailMessage emailMessage = new EmailMessage();
        emailMessage.setSubject("Optima Italia - Il tuo codice di attivazione");
        emailMessage.setMessage("Gentile Cliente, utilizza il codice  " + otp + " per confermare la tua operazione.");
        return emailService.sendEmail(emailMessage, email).getStatusCodeValue();
    }
}
