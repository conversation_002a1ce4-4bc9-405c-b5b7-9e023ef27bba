package com.optima.security.service.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.factory.TokenServiceFactory;
import com.optima.security.model.UserEntity;
import com.optima.security.processors.TokenService;
import com.optima.security.service.JwtTokenService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;


public class JwtTokenServiceImpl implements JwtTokenService {


    @Value("${optima.security.token.secret}")
    private String tokenSecret;

    private final TokenServiceFactory tokenServiceFactory;

    public JwtTokenServiceImpl(TokenServiceFactory tokenServiceFactory) {
        this.tokenServiceFactory = tokenServiceFactory;
    }

    @Override
    public String buildProlongedToken(Authentication authentication) throws JwtTokenException {
        TokenService service = tokenServiceFactory.getService(TokenType.PROLONGED);
        if (service != null) {
            return service.buildToken(authentication);
        }
        throw new JwtTokenException("Prolonged token is not configured.");
    }

    @Override
    public String buildProlongedAdminToken(Authentication authentication, String userId) throws JwtTokenException {
        TokenService service = tokenServiceFactory.getService(TokenType.ADMINPROLONGED);
        if (service != null && userId != null) {
            return service.buildToken(authentication, userId);
        }
        throw new JwtTokenException("Prolonged token is not configured.");
    }

    @Override
    @Transactional
    public String buildDisposableToken(Long userId) throws JwtTokenException {
        TokenService service = tokenServiceFactory.getService(TokenType.DISPOSABLE);
        if (service != null) {
            return service.buildToken(userId);
        }
        throw new JwtTokenException("Disposable token is not configured.");
    }

    @Override
    public String buildResetPasswordToken(UserEntity user) throws JwtTokenException {
        TokenService service = tokenServiceFactory.getService(TokenType.RESET_PSW_TOKEN);
        if (service != null) {
            return service.buildToken(user);
        }
        throw new JwtTokenException("Disposable token is not configured.");
    }


    public AbstractAuthenticationToken validateToken(String token) {
        if (token != null) {
            try {
                Claims claims = Jwts.parser().setSigningKey(this.tokenSecret).parseClaimsJws(token).getBody();
                TokenType tokenType = TokenType.valueOf(claims.get("tokenType", String.class));
                TokenService service = tokenServiceFactory.getService(tokenType);
                if (service != null) {
                    return service.process(claims);
                }
            } catch (ExpiredJwtException | MalformedJwtException e){
                return null;
            }
        }
        return null;
    }
}
