package com.optima.security.service.impl;

import com.optima.security.model.UserAudit;
import com.optima.security.repository.UserAuditRepository;
import com.optima.security.service.UserAuditService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Transactional
public class UserAuditServiceImpl implements UserAuditService {

    private static final Logger logger = LogManager.getLogger(UserAuditServiceImpl.class);

    private final UserAuditRepository userAuditRepository;

    public UserAuditServiceImpl(UserAuditRepository userAuditRepository) {
        this.userAuditRepository = userAuditRepository;
    }

    @Override
    public void saveNewLogin(UserAudit userAudit) {
        logger.info("Creating new login for users_audit table with user id: " + userAudit.getClientId());
        userAuditRepository.save(userAudit);
    }
}
