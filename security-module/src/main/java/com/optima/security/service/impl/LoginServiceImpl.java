package com.optima.security.service.impl;

import com.optima.security.model.BriefUserData;
import com.optima.security.model.UserEntity;
import com.optima.security.model.authenticateUserObject.AuthenticateUserObject;
import com.optima.security.model.userData.UserData;
import com.optima.security.repository.dao.UserDao;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.LoginService;
import com.optima.security.util.OptimaSHAPasswd;
import com.optima.security.exceptions.DeletedUserException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LoginServiceImpl implements LoginService {
    private @Value("${restdata.urls.userdata}")
    String userDataUrl;
    private @Value("${restdata.urls.modalitaPagamento}")
    String modalitaUrl;
    private final UserDao userDao;
    private final OptimaSHAPasswd optimaSHAPasswd;
    private final AuthenticationService authenticationService;
    private static final Logger logger = LogManager.getLogger(LoginServiceImpl.class);

    @Autowired
    public LoginServiceImpl(UserDao userDao, OptimaSHAPasswd optimaSHAPasswd, AuthenticationService authenticationService) {
        this.userDao = userDao;
        this.optimaSHAPasswd = optimaSHAPasswd;
        this.authenticationService = authenticationService;
    }

    @Override
    public UserEntity ifExist(String username, String password, Boolean includeDeleted) {
        //Autenticazione tramite username
        logger.info("Searching for a user using a username or email");
        UserEntity user = userDao.findByUserName(username);
        if (user != null && optimaSHAPasswd.compare(user.getPassword(), password)) {
            logger.info("User: {} found successfully by username", username);
            // Check if a user is deleted
            if (user.getDeleted() != null && user.getDeleted() && Boolean.FALSE.equals(includeDeleted)) {
                logger.error("User: {} is deleted", username);
                throw new DeletedUserException("Account is deleted");
            }
            return user;
        } else {
            //Autenticazione tramite email
            user = userDao.findByEmail(username);
            if (user != null && optimaSHAPasswd.compare(user.getPassword(), password)) {
                logger.info("User: {} found successfully by email", username);
                // Check if a user is deleted
                if (user.getDeleted() != null && user.getDeleted() && Boolean.FALSE.equals(includeDeleted)) {
                    logger.error("User: {} is deleted", username);
                    throw new DeletedUserException("Account is deleted");
                }
                return user;
            } else {
                logger.error("User: {} didn't find", username);
                return null;
            }
        }
    }

    @Override
    public UserEntity findByUserName(String username) {
        return userDao.findByUserName(username);
    }

    @Override
    public Object getAllUsersWithTheSameEmail(String email, String password) {
        List<UserEntity> users = userDao.findAllUsersByEmail(email);
        List<BriefUserData> briefUserData = new ArrayList<>();
        if (!users.isEmpty()) {
            for (UserEntity user : users) {
                if (optimaSHAPasswd.compare(user.getPassword(), password)) {
                    for (UserEntity client : users) {
                        if (Boolean.FALSE.equals(client.getDeleted())) {
                            briefUserData.add(new BriefUserData(authenticationService.getUserData(client.getUsername(), userDataUrl).getNameInInvoice(), client.getUsername()));
                        }
                    }
                    return briefUserData;
                }
            }
            return new ArrayList<>();
        }
        return users;
    }

    public UserData getUserData(String clientId) {
        UserData finalUserData = authenticationService.getUserData(clientId, userDataUrl);
        // UserData addingUserData = authenticationService.getUserData(clientId, modalitaUrl);
        // finalUserData.setModalita(addingUserData.getModalita());
        // transform(finalUserData);
        return finalUserData;
    }

    @Override
    public AuthenticateUserObject getAuthenticateUserObject(String clientId, String passwordModifiedDate, Boolean deleted) {
        AuthenticateUserObject authenticateUserObject = new AuthenticateUserObject(getUserData(clientId), passwordModifiedDate);
        if (authenticateUserObject.getCustomerData() != null) {
            authenticateUserObject.getCustomerData().setDeleted(deleted);
        }
        return authenticateUserObject;
    }


    private void transform(UserData userData) {
        Map<String, String> map = new HashMap<>();
        map.put("SDD", "Addebito diretto");
        map.put("BB", "Bonifico bancario");
        map.put("BP", "Bollettino postale");
        map.put("Bollettino Postale", "Bollettino postale");
        String modalitaDiPagamento = userData.getModalita();
        map.forEach((String k, String v) -> {
            if (modalitaDiPagamento != null && modalitaDiPagamento.contains(k)) {
                userData.setModalita(v);
            }
        });
    }
}
