package com.optima.security.service.impl;

import com.optima.security.model.UserAudit;
import com.optima.security.model.UserEntity;
import com.optima.security.model.UserPrincipal;
import com.optima.security.model.authenticateUserObject.CustomerData;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.LoginService;
import com.optima.security.service.UserAuditService;
import com.optima.security.service.UserService;
import com.optima.security.exceptions.DeletedUserException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;

@Service
public class CustomUserDetailsService extends AbstractUserDetailsAuthenticationProvider {

    private static final Logger logger = LogManager.getLogger(CustomUserDetailsService.class);
    private final AuthenticationService authenticationService;
    private final UserService userService;
    private final LoginService loginService;
    private final UserAuditService userAuditService;

    @Autowired
    public CustomUserDetailsService(AuthenticationService authenticationService, UserAuditService userAuditService,
                                    LoginService longinService, UserService userService) {
        this.authenticationService = authenticationService;
        this.userAuditService = userAuditService;
        this.loginService = longinService;
        this.userService = userService;
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
                                                  UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken) throws AuthenticationException {
    }

    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        try {
            logger.info("Attempting to authenticate user: {}", username);
            if (isSSOAuthentication()) {
                return loadUserForSSO(username, authentication);
            } else {
                return loadUserForRegularLogin(username, authentication);
            }
        } catch (DeletedUserException ex) {
            throw new DeletedUserException("Account is deleted");
        }
        catch (Exception ex) {
            logger.error("Error authenticating user: {}", username, ex);
            throw new AuthenticationServiceException("Authentication error", ex);
        }
    }

    private boolean isSSOAuthentication() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return false;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        return "/api/validSSOToken".equals(request.getServletPath());
    }

    private UserDetails loadUserForSSO(String username, UsernamePasswordAuthenticationToken authentication) {
        logger.info("User {} authentication by SSO token", username);
        String token = authentication.getCredentials().toString();
        /*ResponseEntity<?> tokenInfo = authenticationService.checkSSOToken(token, username);
        if (tokenInfo.getStatusCode() == HttpStatus.OK) {*/
            UserEntity user = loginService.findByUserName(username);
            CustomerData authenticateUserObject = loginService.getAuthenticateUserObject(username,
                    user.getPasswordModifiedDate().toString(), user.getDeleted()).getCustomerData();
            logger.info("User {} has been authenticated by SSO token successfully", username);
            return UserPrincipal.create(authenticateUserObject);
/*        } else {
            throw new BadCredentialsException("Invalid X-Optima-Auth token");
        }*/
    }

    private UserDetails loadUserForRegularLogin(String username, UsernamePasswordAuthenticationToken authentication) {
        String password = authentication.getCredentials().toString();
        UserEntity user = loginService.ifExist(username, password, false);
        if (user == null) {
            logger.error("User {} with this username and password does not exist.", username);
            throw new AuthenticationServiceException("No user");
        } else {
            CustomerData authenticateUserObject = loginService.getAuthenticateUserObject(user.getIdCliente(),
                    user.getPasswordModifiedDate().toString(), user.getDeleted()).getCustomerData();
            UserDetails loadedUser = UserPrincipal.create(authenticateUserObject);
            logger.info("User {} has been authenticated.", username);
            userService.updateLastAccess(user.getId());
            userAuditService.saveNewLogin(new UserAudit(user.getIdCliente(), user.getEmail(),
                    new Timestamp(System.currentTimeMillis()), false, false, null));
            return loadedUser;
        }
    }
}
