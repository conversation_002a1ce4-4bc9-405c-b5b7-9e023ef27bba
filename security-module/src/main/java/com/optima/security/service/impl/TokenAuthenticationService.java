package com.optima.security.service.impl;


import com.optima.security.constants.SecurityConstants;
import com.optima.security.controller.SecurityController;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.service.JwtTokenService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Collection;

public class TokenAuthenticationService {


    private final JwtTokenService jwtTokenService;

    private static final Logger logger = LogManager.getLogger(TokenAuthenticationService.class);

    public TokenAuthenticationService(JwtTokenService jwtTokenService) {
        this.jwtTokenService = jwtTokenService;
    }


    public void addAuthentication(HttpServletRequest req, HttpServletResponse res, Authentication authentication) throws IOException, JwtTokenException {
        String jwt = jwtTokenService.buildProlongedToken(authentication);
        res.setContentType(MediaType.APPLICATION_JSON_VALUE);
        try (PrintWriter out = res.getWriter()) {
            if (StringUtils.hasLength(req.getParameter("client_id"))
                    && StringUtils.hasLength(req.getParameter("redirect_uri"))
                    && StringUtils.hasLength(req.getParameter("response_type"))
                    && StringUtils.hasLength(req.getParameter("state"))) {
                String redirectUrl = UriComponentsBuilder.fromHttpUrl(req.getParameter("redirect_uri"))
                        .fragment("state="+ req.getParameter("state"))
                        .queryParam("access_token", jwt)
                        .build().toString();
                if (redirectUrl.contains("oauth-redirect.googleusercontent.com")) {

                    String fragmentWithQueryParams = "access_token="+jwt;

                    logger.info("fragmentWithQueryParams -> "+fragmentWithQueryParams);

                    redirectUrl = UriComponentsBuilder.fromHttpUrl(req.getParameter("redirect_uri")).fragment(fragmentWithQueryParams).toUriString();

                    redirectUrl += "&token_type=bearer";
                    redirectUrl += "&state="+req.getParameter("state");

                    logger.info(redirectUrl);
                }
                out.println("{\"accessToken\": \"" + jwt + "\", \"redirectUri\": \"" + redirectUrl + "\"}");
            } else {
                out.println("{\"accessToken\": \"" + jwt + "\"}");
            }
            out.flush();
        }
    }

    public void addAuthentication(HttpServletResponse res, Authentication authentication, String userId) throws IOException, JwtTokenException {
        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
        boolean authorized = authorities.contains(new SimpleGrantedAuthority("ROLE_ADMIN"));
        String JWT = null;
        if (authorized) {
            JWT = jwtTokenService.buildProlongedAdminToken(authentication, userId);

        }
        res.setContentType(MediaType.APPLICATION_JSON_VALUE);
        try (PrintWriter out = res.getWriter()) {
            out.println("{\"accessToken\": \"" + JWT + "\"}");
            out.flush();
        }
    }

    public Authentication getAuthentication(HttpServletRequest request) {
        String token = null;
        if (request.getParameter(SecurityConstants.ACCESS_TOKEN) != null) {
            token = request.getParameter(SecurityConstants.ACCESS_TOKEN);
        }
        if (request.getHeader(SecurityConstants.HEADER_STRING) != null) {
            token = request.getHeader(SecurityConstants.HEADER_STRING);
        }
        return jwtTokenService.validateToken(token);
    }
}

