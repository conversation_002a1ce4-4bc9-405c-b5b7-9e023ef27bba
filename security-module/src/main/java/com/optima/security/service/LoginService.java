package com.optima.security.service;

import com.optima.security.model.UserEntity;
import com.optima.security.model.authenticateUserObject.AuthenticateUserObject;
import com.optima.security.model.userData.UserData;

public interface LoginService {
    UserEntity ifExist(String username, String password, Boolean includeDeleted);

    UserEntity findByUserName(String username);

    UserData getUserData(String clientId);

    AuthenticateUserObject getAuthenticateUserObject(String clientId, String passwordModifiedDate, Boolean deleted);

    Object getAllUsersWithTheSameEmail(String email, String password);
}
