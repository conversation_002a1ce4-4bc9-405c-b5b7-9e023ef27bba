package com.optima.security.controller;

import com.optima.security.model.Login;
import com.optima.security.model.UserAudit;
import com.optima.security.model.UserEntity;
import com.optima.security.model.authenticateUserObject.AuthenticateUserObject;
import com.optima.security.service.LoginService;
import com.optima.security.service.UserAuditService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@RestController
@RequestMapping("/internal")
public class LoginController {

    private final LoginService longinService;

    private final UserAuditService userAuditService;
    private static final Logger logger = LogManager.getLogger(LoginController.class);

    public LoginController(LoginService longinService, UserAuditService userAuditService) {
        this.longinService = longinService;
        this.userAuditService = userAuditService;
    }

    @PostMapping("/authenticateUser")
    public Object authenticateUser(@RequestBody Login request, @RequestHeader("x-api-consumer") String apiConsumer) {
        if (Stream.of(request.getUsername(), request.getPassword()).anyMatch(Objects::isNull)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        logger.info("Authenticating user with username: " + request.getUsername());
        Object users = longinService.getAllUsersWithTheSameEmail(request.getUsername(), request.getPassword());
        if (users == null) {
            return new AuthenticateUserObject(null, null);
        } else if (users instanceof List<?> && ((List<?>) users).size() > 1) {
            return users;
        }
        UserEntity user = longinService.ifExist(request.getUsername(), request.getPassword(), true);
        String outputDateString = null;
        if (user != null) {
            SimpleDateFormat outputFormat = new SimpleDateFormat("dd/MM/yyyy");
            // Convertire la stringa di Data di input in un oggetto Date
            outputDateString = outputFormat.format(user.getPasswordModifiedDate());
            userAuditService.saveNewLogin(new UserAudit(user.getIdCliente(), user.getEmail(), new Timestamp(System.currentTimeMillis()),
                    false, true, apiConsumer));
        }
        return user != null ? longinService.getAuthenticateUserObject(user.getIdCliente(), outputDateString, user.getDeleted()) :
                new AuthenticateUserObject(null, null);
    }

}
