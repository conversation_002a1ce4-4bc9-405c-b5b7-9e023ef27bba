package com.optima.security.controller;


import com.optima.security.model.ApplicationUser;
import com.optima.security.model.User;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.impl.LoginServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api")
public class SecurityController {

    private final Environment environment;

    private static final Logger logger = LogManager.getLogger(SecurityController.class);

    private final AuthenticationService authenticationService;

    private final LoginServiceImpl loginService;

    public SecurityController(Environment environment, AuthenticationService authenticationService, LoginServiceImpl loginService) {
        this.environment = environment;
        this.authenticationService = authenticationService;
        this.loginService = loginService;
    }


    @GetMapping(path = "/getCredentials")
    public ResponseEntity getCredentials(@RequestHeader("username") String username, @RequestHeader("password") String password, HttpServletRequest request) {

        User user = authenticationService.login(username, password);
        if (user != null) {
            logger.info("User authenticated. Client id is obtained. Client Id: {}", user.getId());
            request.getSession().setAttribute("clientId", user.getId());
            return new ResponseEntity<>(user, HttpStatus.OK);
        }
        logger.info("Authentication for user with username {} failed.", username);
        return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
    }

    @PostMapping("/checkIfUserAlreadyRestoredPassword")
    public ResponseEntity<Boolean> checkIfUserAlreadyRestoredPassword(@RequestBody ApplicationUser userCreds) {
        return authenticationService.checkIfUserAlreadyRestoredPassword(userCreds.getUsername(), userCreds.getPassword());
    }

    @GetMapping("/getAllUsersWithTheSameEmail")
    public Object getAllUsersWithTheSameEmail(@RequestHeader("username") String username, @RequestHeader("password") String password) {
        return loginService.getAllUsersWithTheSameEmail(username, password);
    }
}
