package com.optima.security.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class User {

    private Long id;

    private String name;

    private Boolean consumer;

    private String sName;

    private Email billingEmail;

    private String billingType;

    private String sottotipoCluster;

    private List<Long> condiminioUsers;

    private BillingAddress billingAddress;

    private BillingAddress sedeLegale;

    private List<ReferenceNumber> referenceNumber;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("idCliente")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("nome")
    public void setName(String name) {
        this.name = name;
    }

    public Boolean getConsumer() {
        return consumer;
    }

    public void setConsumer(Boolean consumer) {
        this.consumer = consumer;
    }

    @JsonProperty("sName")
    public String getsName() {
        return sName;
    }

    @JsonProperty("cognome")
    public void setsName(String sName) {
        this.sName = sName;
    }

    @JsonProperty("billingType")
    public String getBillingType() {
        return billingType;
    }

    @JsonProperty("tipoFatturazione")
    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    @JsonProperty("billingAddress")
    public BillingAddress getBillingAddress() {
        return billingAddress;
    }

    @JsonProperty("indirizzoFatturazione")
    public void setBillingAddress(BillingAddress billingAddress) {
        this.billingAddress = billingAddress;
    }

    @JsonProperty("billingEmail")
    public Email getBillingEmail() {
        return billingEmail;
    }

    @JsonProperty("indirizzoEmail")
    public void setBillingEmail(Email billingEmail) {
        this.billingEmail = billingEmail;
    }

    @JsonProperty("referenceNumbers")
    public List<ReferenceNumber> getReferenceNumber() {
        return referenceNumber;
    }

    @JsonProperty("numeriRiferimento")
    public void setReferenceNumber(List<ReferenceNumber> referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @JsonProperty("sedeLegale")
    public BillingAddress getSedeLegale() {
        return sedeLegale;
    }

    @JsonProperty("sedeLegale")
    public void setSedeLegale(BillingAddress sedeLegale) {
        this.sedeLegale = sedeLegale;
    }

    @JsonProperty("sottotipoCluster")
    public String getSottotipoCluster() {
        return sottotipoCluster;
    }

    public void setSottotipoCluster(String sottotipoCluster) {
        this.sottotipoCluster = sottotipoCluster;
    }

    public List<Long> getCondiminioUsers() {
        return condiminioUsers;
    }

    public void setCondiminioUsers(List<Long> condiminioUsers) {
        this.condiminioUsers = condiminioUsers;
    }
}

