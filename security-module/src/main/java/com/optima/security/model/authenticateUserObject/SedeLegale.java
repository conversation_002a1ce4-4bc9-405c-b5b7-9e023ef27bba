package com.optima.security.model.authenticateUserObject;

public class SedeLegale {
    private String id;
    private String descrizione;
    private Integer tipoIndirizzo =2;
    private String cap;
    private NumeriRiferimento comune = new NumeriRiferimento();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    public Integer getTipoIndirizzo() {
        return tipoIndirizzo;
    }

    public void setTipoIndirizzo(Integer tipoIndirizzo) {
        this.tipoIndirizzo = tipoIndirizzo;
    }

    public String getCap() {
        return cap;
    }

    public void setCap(String cap) {
        this.cap = cap;
    }

    public NumeriRiferimento getComune() {
        return comune;
    }

    public void setComune(NumeriRiferimento comune) {
        this.comune = comune;
    }
}
