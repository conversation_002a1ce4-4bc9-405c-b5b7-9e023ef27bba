package com.optima.security.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BillingAddress {

    private String postalCode;

    private String description;

    public BillingAddress(String postalCode, String description) {
        this.postalCode = postalCode;
        this.description = description;
    }

    @JsonProperty("code")
    public String getPostalCode() {
        return postalCode;
    }

    @JsonProperty("cap")
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("descrizione")
    public void setDescription(String description) {
        this.description = description;
    }
}
