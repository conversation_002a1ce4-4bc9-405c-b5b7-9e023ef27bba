package com.optima.security.model;

import com.optima.security.constants.TokenType;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "jwt_token")
public class JwtToken {

    @Id
    private String uid;

    @Column(nullable = false, length = 200)
    private String token;

    @Column(name = "expiration_time", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date expirationTime;

    @Column(name = "token_type")
    @Enumerated(EnumType.STRING)
    private TokenType tokenType;

    @Column(name = "client_id", nullable = false)
    private Long clientId;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public TokenType getTokenType() {
        return tokenType;
    }

    public void setTokenType(TokenType tokenType) {
        this.tokenType = tokenType;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JwtToken jwtToken = (JwtToken) o;
        return Objects.equals(uid, jwtToken.uid) &&
                Objects.equals(clientId, jwtToken.clientId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(uid, clientId);
    }

    @Override
    public String toString() {
        return "JwtToken{" +
                "uid='" + uid + '\'' +
                ", token='" + token + '\'' +
                ", expirationTime=" + expirationTime +
                ", tokenType=" + tokenType +
                ", clientId=" + clientId +
                '}';
    }
}
