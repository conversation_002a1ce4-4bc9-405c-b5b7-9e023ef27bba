package com.optima.security.model;

import lombok.NonNull;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "user_logged_in_before")
public class UserLoggedInBefore {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "client_id")
    @NonNull
    private Long clientId;

    @Column(name = "changed_password")
    private Boolean changedPassword;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Boolean getChangedPassword() {
        return changedPassword;
    }

    public void setChangedPassword(Boolean changedPassword) {
        this.changedPassword = changedPassword;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserLoggedInBefore userLoggedInBefore = (UserLoggedInBefore) o;
        return Objects.equals(id, userLoggedInBefore.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, clientId);
    }

}
