package com.optima.security.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OTPResponse {

    private Status status;

    private String description;

    public enum Status {
        OK, KO
    }

    public static OTPResponse success(String description) {
        OTPResponse otpResponse = new OTPResponse();
        otpResponse.status = Status.OK;
        otpResponse.description = description;
        return otpResponse;
    }

    public static OTPResponse failed(String description) {
        OTPResponse otpResponse = new OTPResponse();
        otpResponse.status = Status.KO;
        otpResponse.description = description;
        return otpResponse;
    }
}
