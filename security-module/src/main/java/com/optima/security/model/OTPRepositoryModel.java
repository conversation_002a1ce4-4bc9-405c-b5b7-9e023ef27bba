package com.optima.security.model;


import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "otp")
public class OTPRepositoryModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column
    private Long uid;

    @Column(nullable = false, length = 200, unique = true)
    private String otp;

    @Column(name = "ip_address", length = 200)
    private String ipAddress;

    @Column(name = "otp_obtained_by", length = 200)
    private String otpObtainedBy;

    @Column(name = "otp_creation_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date otpCreationTime;

    @Column(name = "otp_usage_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date otpUsageTime;

    @Column(name = "expiration_time", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date expirationTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private OTPStatus status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getOtpObtainedBy() {
        return otpObtainedBy;
    }

    public void setOtpObtainedBy(String otpObtainedBy) {
        this.otpObtainedBy = otpObtainedBy;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public Date getOtpCreationTime() {
        return otpCreationTime;
    }

    public void setOtpCreationTime(Date otpCreationTime) {
        this.otpCreationTime = otpCreationTime;
    }

    public Date getOtpUsageTime() {
        return otpUsageTime;
    }

    public void setOtpUsageTime(Date otpUsageTime) {
        this.otpUsageTime = otpUsageTime;
    }

    public OTPStatus getStatus() {
        return status;
    }

    public void setStatus(OTPStatus status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OTPRepositoryModel otpRepositoryModel = (OTPRepositoryModel) o;
        return Objects.equals(uid, otpRepositoryModel.uid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(uid, otp);
    }

    @Override
    public String toString() {
        return "OTP{" +
                "uid='" + uid + '\'' +
                ", otp='" + otp + '\'' +
                ", creationTime=" + expirationTime +
                '}';
    }
}
