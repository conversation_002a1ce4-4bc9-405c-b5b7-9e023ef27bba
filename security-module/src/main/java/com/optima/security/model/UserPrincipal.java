package com.optima.security.model;

import com.optima.security.model.authenticateUserObject.CustomerData;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

public class UserPrincipal implements UserDetails {

    private Long id;

    private String name;

    private Boolean consumer;

    private String sName;

    private Email billingEmail;

    private String billingType;

    private String sottotipoCluster;

    private BillingAddress billingAddress;

    private BillingAddress sedeLegale;

    private List<ReferenceNumber> referenceNumber;

    private Collection<? extends GrantedAuthority> authorities;

    private UserPrincipal(Long id, String name, Boolean consumer, String sName, Email billingEmail,
                          String billingType, String sottotipoCluster,
                          BillingAddress billingAddress, BillingAddress sedeLegale,
                          List<ReferenceNumber> referenceNumber,
                          Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.name = name;
        this.consumer = consumer;
        this.sName = sName;
        this.billingEmail = billingEmail;
        this.billingType = billingType;
        this.sottotipoCluster = sottotipoCluster;
        this.billingAddress = billingAddress;
        this.sedeLegale = sedeLegale;
        this.referenceNumber = referenceNumber;
        this.authorities = authorities;
    }

    private UserPrincipal(Long id, String name,
                          Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.name = name;
        this.authorities = authorities;
    }

    public UserPrincipal(String name, Collection<? extends GrantedAuthority> authorities) {
        this.name = name;
        this.authorities = authorities;
    }

    public static UserPrincipal create(User user) {
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        return new UserPrincipal(
                user.getId(),
                user.getName(),
                user.getConsumer(),
                user.getsName(),
                user.getBillingEmail(),
                user.getBillingType(),
                user.getSottotipoCluster(),
                user.getBillingAddress(),
                user.getSedeLegale(),
                user.getReferenceNumber(),
                authorities
        );
    }

    public static UserPrincipal create(CustomerData user) {
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        List<ReferenceNumber> referenceNumber = new ArrayList<>();
        if (user.getNumeriRiferimento() != null) {
            for (int i = 0; i < user.getNumeriRiferimento().size(); i++) {
                referenceNumber.add(new ReferenceNumber(user.getNumeriRiferimento().get(i).getValue()));
            }
        }
        return new UserPrincipal(
                user.getIdCliente(),
                user.getNome(),
                user.getConsumer(),
                user.getCognome(),
                new Email(user.getIndirizzoEmail().getValue()),
                user.getTipoFatturazione(),
                user.getSottotipoCluster(),
                new BillingAddress(user.getIndirizzoFatturazione().getCap(), user.getIndirizzoFatturazione().getDescrizione()),
                new BillingAddress(user.getSedeLegale().getCap(), user.getSedeLegale().getDescrizione()),
                referenceNumber,
                authorities);
    }

    public static UserPrincipal create(UserEntity user) {
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        return new UserPrincipal(
                Long.parseLong(user.getIdCliente()),
                user.getUsername(),
                authorities
        );
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public Boolean getConsumer() {
        return consumer;
    }

    public String getsName() {
        return sName;
    }

    public Email getBillingEmail() {
        return billingEmail;
    }

    public String getBillingType() {
        return billingType;
    }

    public String getSottotipoCluster() {
        return sottotipoCluster;
    }

    public BillingAddress getBillingAddress() {
        return billingAddress;
    }

    public BillingAddress getSedeLegale() {
        return sedeLegale;
    }

    public List<ReferenceNumber> getReferenceNumber() {
        return referenceNumber;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserPrincipal that = (UserPrincipal) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
