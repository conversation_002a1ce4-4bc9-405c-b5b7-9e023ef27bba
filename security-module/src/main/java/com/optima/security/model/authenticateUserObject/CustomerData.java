package com.optima.security.model.authenticateUserObject;

import java.util.List;

public class CustomerData {
    private Long idCliente;
    private String crmGuiId;
    private IndirizzoEmail indirizzoEmail;
    private List<NumeriRiferimento> numeriRiferimento;
    private Boolean consumer;
    private String nome;
    private String cognome;
    private String partitaIva;
    private String codiceFiscale;
    private SedeLegale sedeLegale;
    private IndirizzoFatturazione indirizzoFatturazione;
    private String tipoFatturazione;
    private String tipologiaContratto="";
    private String tipoCliente="";
    private String sottotipoCluster="";
    private Boolean deleted;

    public Long getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(Long idCliente) {
        this.idCliente = idCliente;
    }

    public String getCrmGuiId() {
        return crmGuiId;
    }

    public void setCrmGuiId(String crmGuiId) {
        this.crmGuiId = crmGuiId;
    }

    public IndirizzoEmail getIndirizzoEmail() {
        return indirizzoEmail;
    }

    public void setIndirizzoEmail(IndirizzoEmail indirizzoEmail) {
        this.indirizzoEmail = indirizzoEmail;
    }

    public List<NumeriRiferimento> getNumeriRiferimento() {
        return numeriRiferimento;
    }

    public void setNumeriRiferimento(List<NumeriRiferimento> numeriRiferimento) {
        this.numeriRiferimento = numeriRiferimento;
    }

    public Boolean getConsumer() {
        return consumer;
    }

    public void setConsumer(Boolean consumer) {
        this.consumer = consumer;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCognome() {
        return cognome;
    }

    public void setCognome(String cognome) {
        this.cognome = cognome;
    }

    public String getPartitaIva() {
        return partitaIva;
    }

    public void setPartitaIva(String partitaIva) {
        this.partitaIva = partitaIva;
    }

    public String getCodiceFiscale() {
        return codiceFiscale;
    }

    public void setCodiceFiscale(String codiceFiscale) {
        this.codiceFiscale = codiceFiscale;
    }

    public SedeLegale getSedeLegale() {
        return sedeLegale;
    }

    public void setSedeLegale(SedeLegale sedeLegale) {
        this.sedeLegale = sedeLegale;
    }

    public IndirizzoFatturazione getIndirizzoFatturazione() {
        return indirizzoFatturazione;
    }

    public void setIndirizzoFatturazione(IndirizzoFatturazione indirizzoFatturazione) {
        this.indirizzoFatturazione = indirizzoFatturazione;
    }

    public String getTipoFatturazione() {
        return tipoFatturazione;
    }

    public void setTipoFatturazione(String tipoFatturazione) {
        this.tipoFatturazione = tipoFatturazione;
    }

    public String getTipologiaContratto() {
        return tipologiaContratto;
    }

    public void setTipologiaContratto(String tipologiaContratto) {
        this.tipologiaContratto = tipologiaContratto;
    }

    public String getTipoCliente() {
        return tipoCliente;
    }

    public void setTipoCliente(String tipoCliente) {
        this.tipoCliente = tipoCliente;
    }

    public String getSottotipoCluster() {
        return sottotipoCluster;
    }

    public void setSottotipoCluster(String sottotipoCluster) {
        this.sottotipoCluster = sottotipoCluster;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
