package com.optima.security.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.optima.security.model.newUserService.RequestNewUserService;

import javax.persistence.*;
import java.sql.Date;

@Entity
@Table(name = "AC_USERS", schema = "dbo")
public class UserEntity {
    private Long id;
    private String idCliente;
    private String username;
    @JsonIgnore
    private String password;
    private Date creation_date;
    private String email;
    @JsonIgnore
    private String token;
    private String note;
    private Date last_access;
    private Date passwordModifiedDate;
    @JsonIgnore
    private String oldPassword;
    private Boolean deleted;

    public UserEntity(Long id, String idCliente, String username, String password, Date creation_date, String email, String token, String note, Date last_access, Date passwordModifiedDate) {
        this.id = id;
        this.idCliente = idCliente;
        this.username = username;
        this.password = password;
        this.creation_date = creation_date;
        this.email = email;
        this.token = token;
        this.note = note;
        this.last_access = last_access;
        this.passwordModifiedDate = passwordModifiedDate;
    }

    public UserEntity(RequestNewUserService user) {
        this.idCliente = String.valueOf(user.getOptimaUserId());
        this.username = String.valueOf(user.getOptimaUserId());
        this.creation_date = new Date(new java.util.Date().getTime());
        this.email = user.getEmail();
        this.deleted = false;
    }
    public UserEntity(){

    }


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AC_ID", unique = true,
            nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "AC_IDCLIENTE",
            nullable = false, length = 254)
    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    @Column(name = "AC_USERNAME",unique = true,
            nullable = false, length = 254)
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Column(name = "AC_PASSWD", nullable = false, length = 254)
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Column(name = "AC_CREATION_DATE", nullable = false)
    public Date getCreation_date() {
        return creation_date;
    }

    public void setCreation_date(Date creation_date) {
        this.creation_date = creation_date;
    }

    @Column(name = "AC_EMAIL", nullable = false, length = 254)
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "AC_TOKEN", length = 254)
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Column(name = "AC_NOTE", length = 254)
    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Column(name = "AC_LAST_ACCESS")
    public Date getLast_access() {
        return last_access;
    }

    public void setLast_access(Date last_access) {
        this.last_access = last_access;
    }

    @Column(name = "AC_PASSWORD_MODIFIED_DATE")
    public Date getPasswordModifiedDate() {
        return passwordModifiedDate;
    }

    public void setPasswordModifiedDate(Date passwordModifiedDate) {
        this.passwordModifiedDate = passwordModifiedDate;
    }

    @Column(name = "AC_PASSWORD_OLD", nullable = false, length = 254)
    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    @Column(name = "AC_DELETED", nullable = false)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
