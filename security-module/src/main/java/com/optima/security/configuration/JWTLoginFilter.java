package com.optima.security.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.exception.handler.AuthenticationExceptionHandler;
import com.optima.security.exceptions.DeletedUserException;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.ApplicationUser;
import com.optima.security.service.impl.TokenAuthenticationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;


public class JWTLoginFilter extends AbstractAuthenticationProcessingFilter {

    private static final Logger logger = LogManager.getLogger(JWTLoginFilter.class);

    @Autowired
    private TokenAuthenticationService tokenAuthenticationService;

    @Autowired
    private AuthenticationExceptionHandler exceptionHandler;

    public JWTLoginFilter(String url, AuthenticationManager authManager) {
        super(new AntPathRequestMatcher(url));
        setAuthenticationManager(authManager);
    }

    @Override
    public Authentication attemptAuthentication(
            HttpServletRequest req, HttpServletResponse res)
            throws AuthenticationException, IOException {
        ApplicationUser creds = new ObjectMapper()
                .readValue(req.getInputStream(), ApplicationUser.class);
        logger.info("{}: Api call: /loginJWT", creds.getUsername());
        Set<GrantedAuthority> grantedAuthorities = new HashSet<>();
        grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        try {
            return getAuthenticationManager().authenticate(
                    new UsernamePasswordAuthenticationToken(
                            creds.getUsername(),
                            creds.getPassword(),
                            grantedAuthorities
                    )
            );
        } catch (DeletedUserException e) {
            return (Authentication) exceptionHandler.handleDeletedUserException(res);
        }
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                            Authentication auth) throws IOException {
        try {
            tokenAuthenticationService.addAuthentication(req, res, auth);
        } catch (JwtTokenException ignored) {
        }
    }
}
