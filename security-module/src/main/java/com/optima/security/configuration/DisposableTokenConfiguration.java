package com.optima.security.configuration;

import com.optima.security.processors.TokenService;
import com.optima.security.processors.impl.DisposableTokenServiceImpl;
import com.optima.security.repository.JwtTokenRepository;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@ConditionalOnProperty("spring.datasource.url")
@EnableJpaRepositories("com.optima.security.repository")
@EntityScan("com.optima.security.model")
public class DisposableTokenConfiguration {

    @Bean
    protected TokenService disposableTokenService(JwtTokenRepository jwtTokenRepository) {
        return new DisposableTokenServiceImpl(jwtTokenRepository);
    }
}
