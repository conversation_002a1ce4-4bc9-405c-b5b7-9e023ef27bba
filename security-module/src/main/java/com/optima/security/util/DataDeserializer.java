package com.optima.security.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DataDeserializer extends JsonDeserializer<Date> {
    private SimpleDateFormat sdf;

    public DataDeserializer() {
        sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.ITALY);
    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            Date date = sdf.parse(jsonParser.getText());
            return sdf.parse(jsonParser.getText());
        } catch (ParseException e) {
            return null;
        }
    }
}
