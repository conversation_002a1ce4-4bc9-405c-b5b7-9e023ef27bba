package com.optima.security.processors;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import io.jsonwebtoken.Claims;
import org.springframework.security.authentication.AbstractAuthenticationToken;

public interface TokenService {
    AbstractAuthenticationToken process(<PERSON>laims claims);
    TokenType getProcessorType();
    String buildToken(Object o) throws JwtTokenException;
    String buildToken(Object o, String userId) throws JwtTokenException;
}
