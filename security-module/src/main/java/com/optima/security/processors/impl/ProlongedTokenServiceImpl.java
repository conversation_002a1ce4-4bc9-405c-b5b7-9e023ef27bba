package com.optima.security.processors.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.JwtToken;
import com.optima.security.model.UserPrincipal;
import com.optima.security.processors.TokenService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Date;

import static java.util.Collections.emptyList;

public class ProlongedTokenServiceImpl implements TokenService {

    @Value("${optima.security.token.prolonged.expiration-time:#{60*30*1000}}")
    private Long tokenExpirationTime;

    @Value("${optima.security.token.secret}")
    private String tokenSecret;

    @Override
    public AbstractAuthenticationToken process(Claims claims) {
        if (claims.getSubject() != null) {
            JwtToken jwtToken = new JwtToken();
            jwtToken.setUid(claims.getSubject());
            jwtToken.setTokenType(TokenType.PROLONGED);
            jwtToken.setExpirationTime(claims.getExpiration());
            return new UsernamePasswordAuthenticationToken(jwtToken, null, emptyList());
        }
        return null;
    }

    @Override
    public TokenType getProcessorType() {
        return TokenType.PROLONGED;
    }

    @Override
    public String buildToken(Object authentication) {
        if (authentication instanceof Authentication && ((Authentication) authentication)
                .getAuthorities().contains(new SimpleGrantedAuthority("ROLE_USER"))) {
            UserPrincipal userPrincipal = (UserPrincipal) ((Authentication) authentication).getPrincipal();
            return Jwts.builder()
                    .setSubject(userPrincipal.getId().toString())
                    .claim("id", userPrincipal.getId())
                    .claim("tokenType", TokenType.PROLONGED)
                    .claim("sName", userPrincipal.getsName())
                    .claim("email", userPrincipal.getBillingEmail() != null ? userPrincipal.getBillingEmail().getEmail() : "")
                    .claim("consumer", userPrincipal.getConsumer())
                    .claim("billingType", userPrincipal.getBillingType())
                    .claim("sottotipoCluster", userPrincipal.getSottotipoCluster())
                    .claim("billingAddress", userPrincipal.getBillingAddress())
                    .claim("sedeLegale", userPrincipal.getSedeLegale())
                    .claim("referenceNumber", userPrincipal.getReferenceNumber())
                    .setExpiration(new Date(System.currentTimeMillis() + this.tokenExpirationTime))
                    .signWith(SignatureAlgorithm.HS512, this.tokenSecret)
                    .compact();
        }
        return null;
    }

    @Override
    public String buildToken(Object o, String userId) throws JwtTokenException {
        return null;
    }
}
