package com.optima.chat.service.impl;

import com.optima.chat.exceptions.ChatInitializationException;
import com.optima.chat.service.UtilityService;
import com.optima.chat.wsdl.utility.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

public class UtilityServiceImpl extends WebServiceGatewaySupport implements UtilityService {

    private static final Logger logger = LogManager.getLogger(UtilityServiceImpl.class);

    @Value("${optima.chat.soap.ci.utility.ws.url}")
    private String cIUtilityWs;

    private GetAnonymousSessionKey getAnonymousSessionKey = new GetAnonymousSessionKey();

    @Override
    public GetAnonymousSessionKeyResponse getAnonymousSessionKey() {
        logger.info("Obtaining anonymous session key;");
        GetAnonymousSessionKeyResponse getAnonymousSessionKeyResponse = (GetAnonymousSessionKeyResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIUtilityWs,
                        getAnonymousSessionKey, new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/GetAnonymousSessionKey"));
        if (getAnonymousSessionKeyResponse != null) {
            logger.info("Anonymous session key has been obtained.");
            return getAnonymousSessionKeyResponse;
        }
        throw new ChatInitializationException("Anonymous session haven't been obtained. Stopping chat initialization.");
    }

    @Override
    public GetAndUpdateAnonymousCustomerIDResponse getAndUpdateAnonymousCustomerID(GetAndUpdateAnonymousCustomerID getAndUpdateAnonymousCustomerID) {
        logger.info("Obtaining anonymous customer id;");
        GetAndUpdateAnonymousCustomerIDResponse getAndUpdateAnonymousCustomerIDResponse = (GetAndUpdateAnonymousCustomerIDResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIUtilityWs, getAndUpdateAnonymousCustomerID,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/GetAndUpdateAnonymousCustomerID"));
        if (getAndUpdateAnonymousCustomerIDResponse != null) {
            logger.info("Anonymous customer id has been obtained.");
            return getAndUpdateAnonymousCustomerIDResponse;
        }
        throw new ChatInitializationException("Anonymous customer id haven't been obtained. Stopping chat initialization.");
    }

    @Override
    public CustomerLogoffByContactIDResponse closeTextChat(CustomerLogoffByContactID request) {
        logger.info("Closing chat session...");
        CustomerLogoffByContactIDResponse response = (CustomerLogoffByContactIDResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIUtilityWs, request,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/CustomerLogoffByContactID"));
        logger.info("Closing chat status response has been obtained: {}", response);
        return response;
    }

}
