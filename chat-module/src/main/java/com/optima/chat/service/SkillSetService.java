package com.optima.chat.service;

import com.optima.chat.wsdl.skill.GetSkillsetByName;
import com.optima.chat.wsdl.skill.GetSkillsetByNameResponse;
import com.optima.chat.wsdl.skill.IsSkillsetInService;
import com.optima.chat.wsdl.skill.IsSkillsetInServiceResponse;

public interface SkillSetService {

    GetSkillsetByNameResponse getSkillsetByNameResponse(GetSkillsetByName skillSet);

    IsSkillsetInServiceResponse isSkillsetInServiceResponse(IsSkillsetInService request);

}
