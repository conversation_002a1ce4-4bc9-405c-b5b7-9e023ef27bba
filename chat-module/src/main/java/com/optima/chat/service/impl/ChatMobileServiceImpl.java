package com.optima.chat.service.impl;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import com.optima.android.model.CustomerFirebase;
import com.optima.android.service.CustomerFirebaseService;
import com.optima.chat.service.ChatMobileService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
public class ChatMobileServiceImpl implements ChatMobileService {

    private final CustomerFirebaseService customerFirebaseService;

    private static final Logger logger = LogManager.getLogger(ChatMobileService.class);

    public ChatMobileServiceImpl(CustomerFirebaseService customerFirebaseService) {
        this.customerFirebaseService = customerFirebaseService;
    }

    @Override
    public void sendMessageToMobileDevice(String messageFromSupport, String customerId) {
        logger.info("MOBILE: Preparing to send a message to mobile device for user {}", customerId);
        CustomerFirebase customer = customerFirebaseService.getLastCustomerFirebaseByCustomerId(customerId);

        if (customer != null) {
            String registrationToken = customer.getFirebaseId();
            logger.info("MOBILE: Customer firebase obtained. Registration token: {}", registrationToken);

            Notification notification = new Notification("tag_firebase_chat", messageFromSupport);

            Message message = Message.builder().setNotification(notification)
                    .putData("tag", "tag_firebase_chat")
                    .setToken(registrationToken)
                    .build();

            // Send a message to the device corresponding to the provided
            // registration token.
            String response = null;
            try {
                logger.info("Trying to send a message to mobile device {}", message);
                response = FirebaseMessaging.getInstance().send(message);
            } catch (FirebaseMessagingException e) {
                logger.error("Error while trying to send a message to mobile device");
                e.printStackTrace();
            }
            logger.info("Successfully sent message: {}, {}", message, response);
        }
    }
}
