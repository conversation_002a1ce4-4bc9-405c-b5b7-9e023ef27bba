package com.optima.chat.service.impl;

import com.optima.chat.exceptions.ChatInitializationException;
import com.optima.chat.service.SkillSetService;
import com.optima.chat.wsdl.skill.GetSkillsetByName;
import com.optima.chat.wsdl.skill.GetSkillsetByNameResponse;
import com.optima.chat.wsdl.skill.IsSkillsetInService;
import com.optima.chat.wsdl.skill.IsSkillsetInServiceResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

public class SkillSetServiceImpl extends WebServiceGatewaySupport implements SkillSetService {

    private static final Logger logger = LogManager.getLogger(SkillSetServiceImpl.class);

    @Value("${optima.chat.soap.ci.skillset.ws.url}")
    private String cISkillSetWs;


    public GetSkillsetByNameResponse getSkillsetByNameResponse(GetSkillsetByName skillSet) {
        if (skillSet != null) {
            logger.info("Obtaining skill set by name: {}", skillSet);
            GetSkillsetByNameResponse skillsetByNameResponse = (GetSkillsetByNameResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(this.cISkillSetWs,
                            skillSet, new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/GetSkillsetByName"));
            if (skillsetByNameResponse != null) {
                logger.info("Skill set has been obtained.");
                return skillsetByNameResponse;
            }
            logger.info("No skill set have been found.");
            throw new ChatInitializationException();
        }
        throw new ChatInitializationException("Skill set request is null.");
    }

    public IsSkillsetInServiceResponse isSkillsetInServiceResponse(IsSkillsetInService request) {
        return (IsSkillsetInServiceResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cISkillSetWs, request,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/IsSkillsetInService"));
    }


}
