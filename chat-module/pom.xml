<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>selfCare</artifactId>
        <groupId>com.optimaitalia</groupId>
        <version>0.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>chat-module</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ws</groupId>
            <artifactId>spring-ws-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
            <version>6.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.optimaitalia</groupId>
            <artifactId>android-api-module</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.optimaitalia</groupId>
            <artifactId>android-api-module</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.optimaitalia</groupId>
            <artifactId>security-module</artifactId>
            <version>0.0.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>0.12.3</version>
                <executions>
                    <execution>
                        <id>schema-utility</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>com.optima.chat.wsdl.utility</generatePackage>
                            <schemas>
                                <schema>
                                    <url>http://172.16.8.53/ccmmwebservices/CIUtilityWs.asmx?wsdl</url>
                                </schema>
                            </schemas>
                        </configuration>
                    </execution>
                    <execution>
                        <id>schema-skill</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>com.optima.chat.wsdl.skill</generatePackage>
                            <schemas>
                                <schema>
                                    <url>http://172.16.8.53/ccmmwebservices/CISkillsetWs.asmx?wsdl</url>
                                </schema>
                            </schemas>
                        </configuration>
                    </execution>
                    <execution>
                        <id>schema-customer</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <args>
                                <arg>-XautoNameResolution</arg>
                            </args>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>com.optima.chat.wsdl.customer</generatePackage>
                            <schemas>
                                <schema>
                                    <url>http://172.16.8.53/ccmmwebservices/CICustomerWs.asmx?wsdl</url>
                                </schema>
                            </schemas>
                        </configuration>
                    </execution>
                    <execution>
                        <id>schema-comms</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <generatePackage>com.optima.chat.wsdl.comms</generatePackage>
                            <schemas>
                                <schema>
                                    <url>http://172.16.8.53/ccmmwebservices/CIWebCommsWs.asmx?wsdl</url>
                                </schema>
                            </schemas>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
