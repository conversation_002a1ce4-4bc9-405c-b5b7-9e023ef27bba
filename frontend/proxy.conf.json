{"/api/*": {"target": "http://localhost:8080", "secure": true}, "/api2/*": {"target": "http://localhost:8080", "secure": false}, "/psw/isExpired/*": {"target": "http://localhost:8080", "secure": true}, "/psw/setPassword": {"target": "http://localhost:8080", "secure": true}, "/psw/forgotPassword": {"target": "http://localhost:8080", "secure": false}, "/loginJWT": {"target": "http://localhost:8080", "secure": false}, "/loginAdminJWT": {"target": "http://localhost:8080", "secure": false}, "/isAdmin": {"target": "http://localhost:8080", "secure": false}, "/internal/authenticateUser": {"target": "http://localhost:8080", "secure": false}, "/checkUsername": {"target": "http://localhost:8080", "secure": false}, "/addNewCustomer": {"target": "http://localhost:8080", "secure": false}, "/changeEmail": {"target": "http://localhost:8080", "secure": false}, "/api/health": {"target": "http://localhost:8080", "secure": false}, "internal/updateDeleted": {"target": "http://localhost:8080", "secure": false}}