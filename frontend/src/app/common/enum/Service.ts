import {EnergiaType} from './EnergiaType';
import {GasType} from './GasType';
import {FissoType} from './FissoType';
import {FibraType} from './FibraType';
import {MobileServiceType} from './MobileServiceType';

export enum Service {
  GAS = 'Gas',
  VOCE = 'Voce',
  ENERGIA = 'Energia'
}

export enum ServiceType {
  INTERNET = 'INTERNET',
  TELEPHONE = 'TELEPHONE',
  GAS = 'GAS',
  ENERGY = 'ENERGY',
  AMAZON = 'AMAZON'
}

export enum ServicesNames {
  LUCE = 'LUCE',
  GAS = 'GAS',
  INTERNET = 'INTERNET',
  FISSO = 'FISSO',
  MOBILE = 'MOBILE'
}

export const alternativeServiceNames = {
  [EnergiaType.ENERGIA]: ServicesNames.LUCE,
  [EnergiaType.ELETTRICITA]: ServicesNames.LUCE,
  [GasType.GAS]: ServicesNames.GAS,
  [FissoType.WLR]: ServicesNames.FISSO,
  [FissoType.VOCE]: ServicesNames.FISSO,
  [FissoType.VOIP]: ServicesNames.FISSO,
  [FibraType.ADSL]: ServicesNames.INTERNET,
  [FibraType.ADSLHDSL]: ServicesNames.INTERNET,
  [MobileServiceType.MOBILE]: ServicesNames.MOBILE
};


export const serviceNamesToAlternativeServiceNamesMap = {
  [ServicesNames.LUCE]: [EnergiaType.ENERGIA, EnergiaType.ELETTRICITA],
  [ServicesNames.GAS]: [GasType.GAS],
  [ServicesNames.INTERNET]: [FibraType.ADSL, FibraType.ADSLHDSL, FibraType.HDSL],
  [ServicesNames.FISSO]: [FissoType.WLR, FissoType.VOCE, FissoType.VOIP],
  [ServicesNames.MOBILE]: [MobileServiceType.MOBILE],
};

export const serviceNameIdMap = {
  [ServicesNames.LUCE]: 4,
  [ServicesNames.GAS]: 8,
  [ServicesNames.INTERNET]: 3,
  [ServicesNames.FISSO]: 1,
  [ServicesNames.MOBILE]: 6,
};
