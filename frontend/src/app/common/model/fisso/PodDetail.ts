export interface PodDetail {
  idCli: number;
  dialer: string;
  stato: string;
  statoOperazione: string;
  tipoOperazione: string;
  dataInvioCarrier: number;
  tipoContratto: string;
  tipoNumero: string;
  tipoAccesso: string;
  dialerPrincipale: boolean;
  tipoCarrier: string;
  idFatturazione: number;
  variazioniLinea?: (VariazioniLineaEntity)[] | null;
  adslRiferimento?: null;
  idAdsl: string;
  dataDac: string;
  vlanId: string;
  credenzialiSIP: CredenzialiSIP;
  credenzialiRadius: CredenzialiRadius;
}
export interface CredenzialiSIP {
  username: string;
  password: string;
}
export interface CredenzialiRadius {
  username: string;
  password: string;
}
export interface VariazioniLineaEntity {
  idContratto: number;
  idCliPrincipale?: null;
  dialerCliPrincipale?: null;
  sede: Sede;
  linea: Linea;
  servizio: Servizio;
  dataAttivazione: number;
  dataDisattivazione?: number | null;
  dataInizioValidita: number;
  dataFineValidita?: number | null;
  progressivoFatturazione: number;
  codiceMigrazioneDon?: string | null;
  codiceMigrazioneOpt?: string | null;
  idCedente?: null;
  idVolturante?: null;
}
export interface Sede {
  idRecapito: number;
  descrizioneSede: string;
  cap: string;
}
export interface Linea {
  idTipoLinea: number;
  descrizioneTipoLinea: string;
  statoLinea: string;
}
export interface Servizio {
  idTipoServizio: number;
  descrizioneTipoServizio: string;
}
