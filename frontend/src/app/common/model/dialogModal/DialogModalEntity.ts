import {DialogModalDataTableEntity} from './DialogModalDataTableEntity';
import {MonthEntity} from '../monthModal/MonthEntity';

export class DialogModalEntity {
  title?: string;
  customTitle?: boolean;
  customTitleStyle?: Object;
  text?: string;
  data?: Array<MonthEntity>;
  currentMese?: string;
  isUnsaved?: boolean;
  currentMeseIndex?: number;
  hasDataForm?: boolean;
  dataFormSetting?: Array<DialogModalDataTableEntity>;
  img?: string;
  hasButtons?: boolean;
  customButtons?: Object;
  withAnullaButton?: boolean;
  hasFillField?: boolean;
  nextEntities?: Array<DialogModalEntity>;
  isFontItalic?: boolean;
  callbackToExecuteOnSubmit?: Function;
  callbackByValidationResult?: Function;
  callBackOnSubmitFirstModal?: Function;
}
