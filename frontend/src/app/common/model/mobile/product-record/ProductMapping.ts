import Dettaglio from '../contract-record/Dettaglio';

export interface ProductMapping {
  costoAnnuo?: null;
  costoAttivazione: number;
  costoRinnovo: number;
  descrizioneProdotto: string;
  flagSoloDati: boolean;
  idCluster: number;
  idProdotto: number;
  idProdottoEnabler: number;
  idProfiloTariffario: number;
  idTipoPeriodoRinnovo: number;
  idTipoProdotto: number;
  nomeCluster: string;
  nomeProdotto: string;
  nomeProfiloTariffario: string;
  nomeTipoPeriodoRinnovo: string;
  nomeTipoProdotto?: null;
  optionReqFlag: boolean;
  periodoRinnovo: number;
  dettaglio?: (Dettaglio)[] | null;
}
