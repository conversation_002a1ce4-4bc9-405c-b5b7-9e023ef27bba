import TariffPlan from './TariffPlan';
import {ProductMapping} from '../product-record/ProductMapping';

export default class MainProduct {
  id: number;
  activationPrice: number;
  allowedAsBonus: boolean;
  createdOn: number;
  name: string;
  parentId?: any;
  productMapping: ProductMapping;
  productOptions?: Array<any>;
  renewalPeriod?: any;
  renewalPrice: number;
  tariffPlan: TariffPlan;
  tariffPlanId: number;
  validityPeriod?: any;
  productIncompatibilities?: any;
  prId: number;
}
