import {ProductMapping} from '../product-record/ProductMapping';

export default class Product {
  id: number;
  activationPrice: number;
  allowedAsBonus: boolean;
  createdOn: number;
  name: string;
  parentId?: any;
  productMapping: ProductMapping;
  productOptions: (ProductOption)[] | null;
  renewalPeriod: number;
  renewalPrice: number;
  tariffPlan?: any;
  tariffPlanId?: any;
  renewalPeriodUnit: RenewalPeriodUnit;
  validityPeriod: number;
  productIncompatibilities?: any;
  prId: number;
}
export interface RenewalPeriodUnit {
  id: number;
  name: string;
  displayName: string;
  counterType: CounterType;
  parentId: number;
  coefficient: string;
  counterTypeId: number;
}
export interface ProductOption {
  activateOnDemand: boolean;
  id: number;
  option: Option;
  optionId: number;
  productId: number;
  renewable: boolean;
}
export interface Option {
  amount: number;
  liveAmount: null;
  counter: Counter;
  counterId: number;
  dynamicAmount: boolean;
  id: number;
  name: string;
  solelyAvailable: boolean;
  unit: Unit;
  unitId: number;
}
export interface Counter {
  counterType: CounterType;
  counterTypeId: number;
  description: string;
  id: number;
  main: boolean;
  mappingId: number;
  name: string;
}
export interface CounterType {
  balance: boolean;
  description: string;
  id: number;
  name: string;
}
export interface Unit {
  counterType: CounterType;
  counterTypeId: number;
  displayName: string;
  id: number;
  name: string;
  parentId: number;
}
