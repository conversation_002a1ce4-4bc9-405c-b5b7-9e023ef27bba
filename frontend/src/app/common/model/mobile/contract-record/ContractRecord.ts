import Msisdn from './Msisdn';
import Sim from './Sim';
import LastStatusChange from './LastStatusChange';
import MainProduct from './MainProduct';
import AdditionalProduct from './AdditionalProduct';

export class ContractRecord {
  idFattura: number;
  idContratto: number;
  canaleAcquisto: number;
  descrizioneCanaleAcquisto: string;
  additionalProducts: AdditionalProduct[];
  createdOn: number;
  customerId: number;
  dataEnabled: boolean;
  id: number;
  languageId: number;
  lastStatusChange: LastStatusChange;
  lastStatusChangeId: number;
  mainProduct: MainProduct;
  mainProductId: number;
  mainProductRenewalPolicy?: any;
  mainProductRenewalPolicyId?: any;
  mappingId: number;
  mmsEnabled: boolean;
  msisdn: Msisdn;
  msisdnId: number;
  ownerId?: any;
  premiumBarringEnabled: boolean;
  roamingDataEnabled: boolean;
  roamingEnabled: boolean;
  roamingLimit?: any;
  roamingLimitEnabled: boolean;
  sim: Sim;
  simId: number;
  smsIncomingEnabled: boolean;
  smsOutgoingEnabled: boolean;
  videoEnabled: boolean;
  voiceIncomingEnabled: boolean;
  voiceInternationalEnabled: boolean;
  voiceOutgoingEnabled: boolean;
  validityPeriod: number;
}
