export interface PodDetail {
  costoAttivazione: number;
  costoDisattivazione: number;
  numeroDiAppoggio: string;
  numeroRate: number;
  progressivoDiFatturazione: number;
  opzioni?: (OpzioniEntity)[] | null;
  rate?: (null)[] | null;
  idLinea: number;
  statoOperazione?: null;
  tipoOperazione?: null;
  stato: string;
  numeroRisorsa: string;
  numeroContatto: string;
  idProdottoAttivo: number;
  profiloProdottoAttivo: string;
  sede: Sede;
  idTipoLinea: number;
  descrizioneTipoLinea: string;
  idContratto: number;
  tipoContratto?: null;
  dataAttivazione: string;
  dataDisattivazione?: null;
  dataRichiestaAttivazione: string;
  dataRichiestaDisattivazione?: null;
  codiceMigrazione: string;
  codiceMigrazionePrec: string;
  idCedente?: null;
  idVolturante?: null;
  progressivoFatturazione: number;
  naked: boolean;
  migrazione: string;
  codUnivocoLinea: string;
  tipoxDSL: string;
  cliCollegati?: (null)[] | null;
  dataDac: string;
}

export interface OpzioniEntity {
  codice: string;
  costoAttivazione: number;
  costoDisattivazione: number;
  costoMese: number;
  dataFineValidita?: null;
  dataInizioValidita?: null;
  descrizione: string;
  id: number;
}

export interface Sede {
  idRecapito: number;
  descrizioneSede: string;
  cap: string;
}
