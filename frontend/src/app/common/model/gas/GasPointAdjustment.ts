export interface GasPointAdjustment {
  coefficienteAdesione: string;
  consumo: number;
  consumoAdeguato: string;
  dataFattura: Date;
  dataLettura: Date;
  elaborato: string;
  fatturaEmissione: string;
  finePeriodo: string;
  idCliente: string;
  inizioPeriodo: Date;
  letturaApertura: string;
  letturaChiusura: string;
  letturaEmissione: string;
  letturaErrata: string;
  letturaFatturata: string;
  letturaSwitch: string;
  prigineDati: string;
  pdr: string;
  valoreLettura: number;
}
