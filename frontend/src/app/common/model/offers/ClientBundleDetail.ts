import {ServiceStatus} from "../../enum/ServiceStatus";

export interface ClientBundleDetail {
  cliente: number;
  descPeriodo: string;
  disdetto?: null;
  fatt: number;
  finval?: null;
  id: number;
  idClientiBundle: number;
  listinominus: Listi;
  listinoplus: Listi;
  stato: Stato;
  pod: string;
  pdr: string;
}

export interface Listi {
  codice: string;
  euro: number;
  id: number;
  quant: number;
  ramo?: null;
  servizio: Servizio;
  tipo: number;
}

export interface Servizio {
  id: number;
  codice: number;
  desc: string;
}

export interface Stato {
  Codice: ServiceStatus;
}
