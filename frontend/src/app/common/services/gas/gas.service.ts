import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import { PodDetailsRequest } from '../../model/gas/PodDetailsRequest';
import { PodDetail } from '../../model/gas/PodDetail';
import { PdrAdditionalDeatil } from '../../model/gas/PdrAdditionalDeatil';
import { UserServices, Utility } from '../../model/services/userServices.model';
import { GasPointAdjustment } from '../../model/gas/GasPointAdjustment';
import {map} from 'rxjs/operators';
import {forkJoin} from 'rxjs/observable/forkJoin';

@Injectable()
export class GasService {

  constructor(private httpClient: HttpClient) {
  }


  getPodDetails(pdr: string, clientId: string): Observable<Array<PodDetail>> {
    if (!pdr || !clientId) {
      return Observable.empty();
    }
    return this.httpClient.post<Array<PodDetail>>('/api/gas/pod/details', {
      pdr,
      cliente: clientId
    } as PodDetailsRequest);
  }

  getPodAdditionalDetails(request: Array<PodDetailsRequest>): Observable<Array<PdrAdditionalDeatil>> {
    return this.httpClient.post<Array<PdrAdditionalDeatil>>('/api/gas/pdr/additional/data', request);
  }

  loadAdditionalPodDetails(utilities: Array<Utility>): Observable<Array<PdrAdditionalDeatil>> {
    if (utilities && utilities.length > 0) {
      const requests = utilities.map(utility => this.createPodDetailsRequest(utility));
      return forkJoin(requests).pipe(
        map(results => results.reduce((acc, val) => acc.concat(val), []))
      );
    }
    return Observable.empty();
  }

  private createPodDetailsRequest(utility: Utility): Observable<PdrAdditionalDeatil[]> {
    const request: PodDetailsRequest = {
      pdr: utility.utNumber,
      cliente: localStorage.getItem('clientId')
    };
    return this.getPodAdditionalDetails([request]);
  }

  loadAdditionalPodDetailsForService(service: UserServices): Observable<Array<PdrAdditionalDeatil>> {
    if (service) {
      return this.loadAdditionalPodDetails(service.utilities);
    }
    return Observable.empty();
  }

  gasPointAdjustments(clientId: string, pdr: string): Observable<Array<GasPointAdjustment>> {
    return this.httpClient.get<Array<GasPointAdjustment>>(`/api/gas/point/adjustments?clientId=${clientId}&pdr=${pdr}`);
  }

}
