import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

@Injectable()
export class PdfService {

  constructor(private httpClient: HttpClient) {
  }

  downloadTariffTransparencyPDF(clientId, contractId) {
    this.httpClient.get(`/api/transparency/pdf/${clientId}/${contractId}`).subscribe(data => window.open(data['urlPdf'][0]));
  }

  downloadContractsPDF(fileName: string): Observable<Blob> {
    return this.httpClient.post(`/api/contracts/pdf/${localStorage.getItem('clientId')}`,
      {spRelativeUri: fileName}, {responseType: 'blob'});
  }

  downloadInvoicePDF(url: string): Observable<Blob> {
    return this.httpClient.post(`/api/pdf/invoice/${localStorage.getItem('clientId')}`, {downloadUrl: url}, {responseType: 'blob'});
  }

  downloadFile(request) {
    request.map(res => {
      return {
        filename: 'filename.pdf',
        data: new Blob([res], {type: 'application/octet-stream'})
      };
    }).subscribe(blob => {
      const link = document.createElement('a');
      const file = new Blob([blob.data], {type: 'application/pdf'});
      link.href = window.URL.createObjectURL(file);
      link.download = blob.filename;
      link.click();
    });
  }

}
