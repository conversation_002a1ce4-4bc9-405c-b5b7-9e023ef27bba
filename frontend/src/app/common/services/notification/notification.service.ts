import { Injectable } from '@angular/core';
import { Toast } from 'angular2-toaster';
import { NotificationActions } from '../../../redux/notification/actions';
import { TostType } from '../../utils/TostType';

@Injectable()
export class NotificationService {

  constructor(private notificationActions: NotificationActions) {
  }

  showSuccessMessage(message = '') {
    const toast = {} as Toast;
    toast.title = 'Esito di variazione';
    toast.type = TostType.SUCCESS;
    toast.body = message;
    this.notificationActions.showNotification(toast);
  }

  successMessage(message = '', title = '', timeout = 5000) {
    const toast = {} as Toast;
    toast.title = title;
    toast.type = TostType.SUCCESS;
    toast.body = message;
    toast.timeout = timeout;
    this.notificationActions.showNotification(toast);
  }

  showInfoMessage(message: string) {
    const toast = {} as Toast;
    toast.type = TostType.INFO;
    toast.body = message;
    this.notificationActions.showNotification(toast);
  }

  errorMessage(message: string, title = '') {
    const toast = {} as Toast;
    toast.type = TostType.ERROR;
    toast.body = message;
    toast.title = title;
    this.notificationActions.showNotification(toast);
  }

}
