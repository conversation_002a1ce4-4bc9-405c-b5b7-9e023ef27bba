import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import ProspectUserRegisterForm, {EmailToSupportWithFiles, ProspectUserContractsInformation} from '../../model/prospect-user/prospectUser';
import {Observable} from 'rxjs/Observable';
import {ForgotPasswordRequest} from '../../model/forgotPassword/forgotPasswordRequest';
import {ForgotPasswordResponse} from '../../model/forgotPassword/forgotPasswordResponse';

@Injectable()
export class ProspectUserService {

  private groupId: string = null;
  private CF: string = null;

  constructor(private httpClient: HttpClient) {
  }

  setGroupIdAndCF(groupId: string, CF: string): void {
    this.groupId = groupId;
    this.CF = CF;
  }

  getGroupId(): string | null {
    return this.groupId;
  }

  getCF(): string | null {
    return this.CF;
  }

  // controller in security module
  sendRegisterForm(registerForm: ProspectUserRegisterForm) {
    return this.httpClient.post(`api/prospect/register`, registerForm, {observe: 'response'});
  }

  sendLoginForm(loginForm: any): Observable<any> {
    return this.httpClient.post<any>(`api/prospect/login`, loginForm, {observe: 'response'});
  }

  isTokenAvailable(token: string): Observable<any> {
    return this.httpClient.get<any>(`api/prospect/isExpired?access_token=${token}`);
  }

  setNewPassword(passwords: any, token: string): Observable<ForgotPasswordResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Auth': token
    });
    const options = {headers};
    return this.httpClient.post<ForgotPasswordResponse>('api/prospect/setPassword', passwords, options);
  }

  // controller in backend module
  getContractsByCodiceFiscale(codiceFiscale: string): Observable<ProspectUserContractsInformation> {
    return this.httpClient.get<ProspectUserContractsInformation>(`/api/prospect/information/contracts`, {
      params: new HttpParams().set('codiceFiscale', codiceFiscale)
    });
  }

  sendEmailToSupport(emailRequest: EmailToSupportWithFiles): Observable<any> {
    return this.httpClient.post<any>(`api/prospect/information/email/support`, emailRequest);
  }

  sendFiles(files: FormData): Observable<any> {
    return this.httpClient.post<any>(`api/prospect/information/upload-file`, files);
  }

  restorePassword(request: ForgotPasswordRequest): Observable<ForgotPasswordResponse> {
    return this.httpClient.post<ForgotPasswordResponse>(`api/prospect/information/forgotPassword`, request);
  }
}
