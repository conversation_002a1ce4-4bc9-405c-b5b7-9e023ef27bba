import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import EmailMessage from '../../model/EmailMessage';

@Injectable()
export class EmailService {

  constructor(private httpClient: HttpClient) {
  }

  sendEmail(emailMessage: EmailMessage | FormData): Observable<any> {
    return this.httpClient.post('api/email/new', emailMessage);
  }

  sendIntegratedSolutionRequest(emailMessage: EmailMessage): Observable<any> {
    return this.httpClient.post('api/email/integrated/solution', emailMessage);
  }

  sendTuttoInUnoRequest(emailMessage: EmailMessage): Observable<any> {
    return this.httpClient.post('api/email/tuttoinuno/msg', emailMessage);
  }

  sendRequestForBookVisitAgent(emailMessage: EmailMessage): Observable<any> {
    return this.httpClient.post('api/email/agent/bookVisit', emailMessage, {observe: 'response'});
  }
}
