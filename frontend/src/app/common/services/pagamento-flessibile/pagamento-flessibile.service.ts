import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

@Injectable()
export class PagamentoFlessibileService {

  constructor(private httpClient: HttpClient) {
  }

  isPagamentoFlessibile(): Observable<boolean> {
    return this.httpClient.get<boolean>(`api/pagamento-flessibile/${localStorage.getItem('clientId')}`);
  }

}
