import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

import {UserData} from '../../model/userData.model';
import {ChangePasswordIdentificationRequest, ChangePersonalDataRequest} from '../../model/ChangePersonalDataRequest';
import {ChangePersonalDataResponse} from '../../model/ChangePersonalDataResponse';
import {IncidentEventResponse} from '../../../routes/autolettura/model/IncidentEventResponse';
import {UserClusterInfo} from '../../model/user/UserClusterInfo';
import {GetLatestCreditPolicyStatusResponse} from '../../model/GetLatestCreditPolicyStatusResponse';

@Injectable()
export class UserDataService {

  readonly url = 'api/userData';

  constructor(private http: HttpClient) {
  }

  public getUserData(): Observable<UserData> {
    const headers = new HttpHeaders({'clientid': localStorage.getItem('clientId')});
    return this.http.get<UserData>(this.url, {headers: headers});
  }

  public getCreditCardStatus(): Observable<Boolean> {
    const headers = new HttpHeaders({'clientid': localStorage.getItem('clientId')});
    return this.http.get<Boolean>('api/creditCardStatus', {headers: headers});
  }

  public changeUserPersonalData(request: ChangePersonalDataRequest): Observable<ChangePersonalDataResponse> {
    return this.http.post<ChangePersonalDataResponse>('/api/user/change', request);
  }

  public changePasswordIdentification(request: ChangePasswordIdentificationRequest): Observable<ChangePersonalDataResponse> {
    return this.http.post<ChangePersonalDataResponse>('/api/user/change/password-identification', request);
  }

  public changeAddress(request: ChangePersonalDataRequest): Observable<IncidentEventResponse> {
    return this.http.post<IncidentEventResponse>('/api/user/change/address', request);
  }

  public getUserClusterInfo(clientId: string): Observable<Array<UserClusterInfo>> {
    return this.http.get<Array<UserClusterInfo>>(`/api/user/cluster/info?clientId=${clientId}`);
  }

  public isDashListUserExist(clientId: string): Observable<boolean> {
    return this.http.get<boolean>(`/api/dashlist/user/exist?clientId=${clientId}`);
  }

  public getUserCodeFromIncidentEvent(): Observable<any> {
    return this.http.post<any>(`/api/user/getUserCodeFromIncidentEvent/${localStorage.getItem('clientId')}`, {responseType: 'text'});
  }

  public getUserCondominioInfo(clientId: string): Observable<Array<any>> {
    return this.http.get<Array<any>>(`/api/user/condominio/${clientId}`);
  }

  public getLatestCreditPolicyStatus(clientId: string): Observable<GetLatestCreditPolicyStatusResponse> {
    return this.http.get<GetLatestCreditPolicyStatusResponse>(`/api/user/getLatestCreditPolicyStatus/${clientId}`);
  }
  public getUserCondominioFiscalCode(clientId: string): Observable<any> {
    return this.http.get<any>(`/api/user/condominio/fiscal-code/${clientId}`);
  }

}
