import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {AmazonPrime} from '../../model/amazonprime/AmazonPrime';
import {IncidentEventService} from '../incedentEvent/incident-event.service';

@Injectable()
export class AmazonPrimeService {

  constructor(private httpClient: HttpClient, private incidentEventService: IncidentEventService) { }

  getAmazonPrimeData(clientId: string): Observable<AmazonPrime> {
    return this.httpClient.get<AmazonPrime>(`/api/amazon-prime/prime-data?clientId=${clientId}`);
  }

  unsubscribe(clientId: string): Observable<AmazonPrime> {
    this.incidentEventService.disattivazioneAmazonPrimeRequest().subscribe();
    return this.httpClient.get<AmazonPrime>(`/api/amazon-prime/unsubscribe?clientId=${clientId}`);
  }

}
