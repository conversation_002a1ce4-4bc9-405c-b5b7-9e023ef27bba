import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {InitialBonusProgress} from '../../model/offers/InitialBonusProgress';
import {ClientBundleDetail} from '../../model/offers/ClientBundleDetail';
import {Offer} from '../../../redux/model/Offer';
import {ChangePromoMeseRequest} from '../../model/offers/ChangePromoMeseRequest';
import {PromoMeseOffWrapper} from '../../model/offers/PromoMeseOffWrapper';

@Injectable()
export class OffersService {

  constructor(private httpClient: HttpClient) {
  }

  loadClientOffers(clientId: string): Observable<Array<Offer>> {
    return this.httpClient.post<Array<Offer>>('api/offer/data', {clientId});
  }

  loadAndamentoContoRelax(clientId: string, billingId: number): Observable<InitialBonusProgress> {
    return this.httpClient.get<InitialBonusProgress>(`/api/user/initial/bonus/${clientId}/${billingId}`);
  }

  governanceOfferClientBundleDetails(clientId: string, billingId: number): Observable<Array<ClientBundleDetail>> {
    return this.httpClient.get<Array<ClientBundleDetail>>(`/api/user/offer/bundle/details/${clientId}/${billingId}`);
  }

  checkYourTariff(type: string, service: string, tariffCode: string) {
    return this.httpClient.get(`/api/user/check/tariff?type=${type}&service=${service}&tariffCode=${tariffCode}`,
      {responseType: 'text'});
  }

  getPromoMeseOff(clientId: string): Observable<Array<PromoMeseOffWrapper>> {
    return this.httpClient.get<Array<PromoMeseOffWrapper>>(`api/user/promo/mese/${clientId}`);
  }

  changePromoMeseRequest(changePromoMeseRequest: ChangePromoMeseRequest) {
    return this.httpClient.post('api/user/promo/mese', changePromoMeseRequest);
  }

  checkIncidentEventPromoMeseChange(): Observable<any> {
    return this.httpClient.get(`/api/support/checkIncidentChangePromo/${localStorage.getItem('clientId')}`);
  }

  checkIncidentMNP(msisdnId: string): Observable<any> {
    return this.httpClient.get(`/api/support/checkIncidentRichiestaPortabilita/${localStorage.getItem('clientId')}/${msisdnId}`);
  }

  checkIncidentEventPagamentoFlessibile(): Observable<any> {
    return this.httpClient.get(`/api/support/checkOpenIncidentEventPagamentoFlessibile/${localStorage.getItem('clientId')}`);
  }

  checkIncidentEventCrossSelling(): Observable<boolean> {
    return this.httpClient.get<boolean>(`/api/support/checkOpenIncidentEventCrossSelling/${localStorage.getItem('clientId')}`);
  }
}
