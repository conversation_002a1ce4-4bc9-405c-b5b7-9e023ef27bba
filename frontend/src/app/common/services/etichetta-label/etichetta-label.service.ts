import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {EtichettaLabel} from '../../model/etichetta-label/etichetta-label.model';

@Injectable()
export class EtichettaLabelService {

  readonly url = 'api/label/';

  constructor(private httpClient: HttpClient) {
  }

  getEtichettaLabelByPodUtNumber(pod: string): Observable<EtichettaLabel> {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.get<EtichettaLabel>(this.url + clientId + "/" + pod, {headers: headers});
  }

  createOrUpdateEtichettaLabel(etichettaLabel: EtichettaLabel): Observable<EtichettaLabel> {
    etichettaLabel.clientId = this.setClientId();
    return this.httpClient.post<EtichettaLabel>(this.url, etichettaLabel);
  }

  setClientId() {
    return localStorage.getItem('clientId');
  }

}
