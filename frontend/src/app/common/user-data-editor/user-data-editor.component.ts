import {Component, EventEmitter, Input, Output} from '@angular/core';
import {FormControl, FormGroup} from '@angular/forms';
import {FormUtils} from '../utils/FormUtils';
import {catchError} from 'rxjs/operators';
import {Observable} from 'rxjs/Observable';

@Component({
  selector: 'app-user-data-editor',
  templateUrl: './user-data-editor.component.html',
  styleUrls: ['./user-data-editor.component.scss']
})
export class UserDataEditorComponent {

  formGroup: FormGroup;

  formControl: FormControl;

  @Input('title')
  title: string;

  @Input('name')
  name: string;

  @Input('onAccept')
  onAccept: Function;

  @Input('modifiable')
  modifiable: boolean;

  @Input('type')
  type = 'text';

  @Input('options')
  options = [];

  @Input('forForm')
  formName: string;

  @Input('withConfirmation')
  withConfirmation = false;

  @Input('emptyOnEdit')
  emptyOnEdit = false;

  @Input('placeholder')
  placeholder = '';

  isEditing: boolean;

  showConfirmDialog = false;

  initialValue: string;

  isMasked = true;

  @Output() editFieldsEventEmitter: EventEmitter<any> = new EventEmitter<any>();

  constructor() {

  }

  @Input('formGroup')
  set group(formGroup: FormGroup) {
    if (formGroup && this.name) {
      this.formControl = <FormControl>formGroup.controls[this.name];
    }
    this.formGroup = formGroup;
  }

  @Input('readyToEdit')
  set _readyToEdit(val: boolean) {
    if (val){
      this.edit();
    }
  }


  showHideConfirmationPopup() {
    this.showConfirmDialog = !this.showConfirmDialog;
  }

  decline() {
    // this.formControl.setValue(this.initialValue);
    this.showHideConfirmationPopup();
  }

  accept() {
    FormUtils.setFormControlAsTouched(this.formControl);
    if (this.formControl.valid) {
      if (this.initialValue !== this.formControl.value) {
        this.showHideConfirmationPopup();
      } else {
        this.isEditing = false;
        this.editFieldsEventEmitter.emit({'name': this.name, 'isEditing' : this.isEditing , 'action': 'accept'});
      }
    }
  }

  confirm() {
    this.isEditing = false;
    this.editFieldsEventEmitter.emit({'name': this.name, 'isEditing' : this.isEditing, 'action': 'accept'});
    this.showHideConfirmationPopup();
    if (this.onAccept) {
      this.onAccept(this.formControl.value, this.initialValue).pipe(catchError((response) => {
        this.formControl.reset(this.initialValue);
        return Observable.of(response);
      })).subscribe();
    }
  }

  getMaskedValue(value: string): string {
    return value ? '*'.repeat(value.length) : '';
  }

  toggleMask() {
    this.isMasked = !this.isMasked;
  }

  edit() {
    this.isEditing = true;
    this.editFieldsEventEmitter.emit({'name': this.name, 'isEditing' : this.isEditing});
    this.initialValue = this.formControl.value;
    if (this.emptyOnEdit) {
      this.formControl.reset(null);
    }
  }

  closeEditor() {
    if (this.initialValue && this.formName === 'richiedi') {
      this.formControl.setValue(this.initialValue, {emitEvent: false});
      this.isEditing = false;
      this.editFieldsEventEmitter.emit({'name': this.name, 'isEditing' : this.isEditing, 'action': 'close'});
      this.formControl.setErrors(null);
    }
    if (!this.formName) {
      this.formControl.setValue(this.initialValue, {emitEvent: false});
      this.isEditing = false;
      this.formControl.setErrors(null);

    }
  }
}
