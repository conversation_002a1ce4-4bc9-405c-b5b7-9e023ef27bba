/* tslint:disable */
import ChartConfig from "./ChartConfig";

export class ChartBuilder {

  private defaultConfiguration: ChartConfig;

  private dataSet = [];

  private labels = [];

  private chartAnnotations = [];

  private enrichers = [];

  constructor() {
  }

  static builder(): ChartBuilder {
    return new this();
  }

  withDataSet(dataSet: Array<object>): ChartBuilder {
    this.dataSet.concat(dataSet);
    return this;
  }

  withLabels(labels: Array<object>): ChartBuilder {
    this.labels.concat(labels);
    return this;
  }

  withConfiguration(config: ChartConfig): ChartBuilder {
    this.defaultConfiguration = config;
    return this;
  }

  withAnnotations(annotations: Array<object>): ChartBuilder {
    this.chartAnnotations = this.chartAnnotations.concat(annotations);
    return this;
  }

  withEnricher(enricher: Function) {
    this.enrichers.push(enricher);
    return this;
  }

  build() {
    if (this.defaultConfiguration && this.defaultConfiguration.data && this.defaultConfiguration.options) {
      if (this.labels && this.labels.length) {
        this.defaultConfiguration.data.labels = this.labels;
      }
      if (this.dataSet && this.dataSet.length) {
        this.defaultConfiguration.data.datasets = this.dataSet;
      }
      if (this.defaultConfiguration.options) {
        this.defaultConfiguration.options.annotation.annotations= this.chartAnnotations;
      }
      this.enrichers.forEach(enricher => enricher(this.defaultConfiguration));
    }
    return this.defaultConfiguration;
  }


}
