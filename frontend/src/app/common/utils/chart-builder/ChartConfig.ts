export default class ChartConfig {
  type: string;
  data: Data;
  options: Options;
  config: Config;
}

export interface Config {
  plugins: any;
}

export interface Data {
  datasets: Array<any>;
  labels: Array<any>;
}

export interface DataSet {
  label: string;
  yAxisID: string;
  data: Array<any>;
  borderColor?: string | Array<string>;
  pointBackgroundColor?: string | Array<string>;
  pointHoverBorderColor?: string | Array<string>;
  backgroundColor?: string | Array<string>;
  tension?: number;
  borderWidth?: number;
  fill?: boolean;
  offsetGridLines?: boolean;
  pointBorderColor?: string | Array<string>;
}


export interface Options {
  events?: (null)[] | null;
  showAllTooltips?: boolean;
  legend: Legend | boolean;
  scales: Scales;
  annotation: Annotation;
  animation: any;
  layout: any;
  tooltips: any;
  hover: any;
  customElements: any;
  onClick?: any;
  title?: any;
}

export interface Legend {
  display: boolean;
}

export interface Scales {
  xAxes: Array<Axe>;
  yAxes: Array<Axe>;
}

export interface Axe {
  position: string;
  stacked: boolean;
  ticks: Ticks;
  gridLines: GridLines;
  scaleLabel: any;
  barThickness?: number;
}

export interface Ticks {
  stepSize?: number;
  min?: number;
  fontColor?: string;
  autoSkip?: boolean;
  display?: boolean;
  callback: any;
  fontStyle: number;
  padding?: number;
  fontFamily?: string;
  fontSize?: number;
  maxTicksLimit?: number;
  maxRotation?: number;
}

export interface GridLines {
  display?: boolean;
  color?: string;
  offset?: boolean;
  zeroLineColor?: string;
  zeroLineWidth?: number;
  tickMarkLength?: number;
}

export interface Annotation {
  events?: (null)[] | null;
  drawTime?: string;
  annotations?: (null)[] | null;
}

