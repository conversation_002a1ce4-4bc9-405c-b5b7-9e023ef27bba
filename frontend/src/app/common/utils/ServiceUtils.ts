import { UserServices } from '../model/services/userServices.model';
import { EnergiaType } from '../enum/EnergiaType';
import { GasType } from '../enum/GasType';
import { FibraType } from '../enum/FibraType';
import { FissoType } from '../enum/FissoType';
import { MobileServiceType } from '../enum/MobileServiceType';
import { ServiceType } from '../enum/Service';
import { ActiveServiceStatus, InActiveServiceStatus, ServiceStatus } from '../enum/ServiceStatus';
import {AmazonPrimeType} from '../enum/AmazonPrimeType';

export const serviceTypeMap = {
  [GasType.GAS]: ServiceType.GAS,
  [EnergiaType.ENERGIA]: ServiceType.ENERGY,
  [EnergiaType.ELETTRICITA]: ServiceType.ENERGY,
  [FissoType.VOIP]: ServiceType.TELEPHONE,
  [FissoType.WLR]: ServiceType.TELEPHONE,
  [FissoType.VOCE]: ServiceType.TELEPHONE,
  [FibraType.ADSL]: ServiceType.INTERNET,
  [FibraType.ADSLHDSL]: ServiceType.INTERNET,
  [FibraType.HDSL]: ServiceType.INTERNET,
  [AmazonPrimeType.AMAZON]: ServiceType.AMAZON
};

export class ServiceUtils {

  static isLuce(service: UserServices): boolean {
    return service && EnergiaType[service.serviceName];
  }

  static isGas(service: UserServices): boolean {
    return service && GasType[service.serviceName];
  }

  static isInternet(service: UserServices): boolean {
    return service && FibraType[service.serviceName];
  }

  static isFisso(service: UserServices): boolean {
    return service && FissoType[service.serviceName];
  }

  static isMobile(service: UserServices): boolean {
    return service && MobileServiceType[service.serviceName];
  }

  static getServiceType(service: string) {
    return serviceTypeMap[service];
  }

  static getInActiveServices(data: Array<UserServices>) {
    const result = {};
    data.forEach(value => {
      const activeUtilities = value.utilities.filter(utility => (InActiveServiceStatus[utility.status] ||
        utility.status === ServiceStatus.PORTED_OUT || utility.status === 'Disattivo' ) && utility.utNumber );
      if (activeUtilities.length > 0) {
        result[value.serviceName] = {'serviceName': value.serviceName, utilities: activeUtilities};
      }
    });
    return result;
  }

  static getActiveServices(data) {
    const result = {};
    if (data) {
      data.forEach((value) => {
        const activeUtilities = value.utilities.filter((utility) =>
          utility.status && ActiveServiceStatus[utility.status.toUpperCase()]);
        if (activeUtilities.length > 0) {
          result[value.serviceName] = {'serviceName': value.serviceName, utilities: activeUtilities};
        }
      });
    }
    return result;
  }

}
