import { PaginationCallback } from './PaginationCallback';
import { Paginator } from './Paginator';
import * as moment from 'moment';
import { NormalizeUtils } from '../NormalizeUtils';

const getNextPage = (context) => context.page.getValue() + 1;
const getPreviousPage = (context) => context.page.getValue() - 1;


export default class YearlyPagination implements PaginationCallback {

  private years: Array<string>;

  private nextPage: Function;
  private previousPage: Function;

  public paginationCallback(context: Paginator): any {
    const yearlyNormalized = NormalizeUtils.groupByExpression(context.array, item => moment(item, "MMM YYYY").year());
    const years = Object.keys(yearlyNormalized);
    this.years = years;
    this.nextPage = context.reverse ? getNextPage : getPreviousPage;
    this.previousPage = context.reverse ? getPreviousPage : getNextPage;
    context.pages = years.length;
    return (nextPage) => {
      context.hasNextHasPrevious(nextPage);
      let index;
      if (context.reverse) {
        index = years.length - nextPage;
      } else {
        index = nextPage - 1;
      }
      context.onChange(yearlyNormalized[years[Math.max(0, index)]]);
    };
  }

  nextValue(context): any {
    const {years, nextPage} = this;
    return years[years.length - nextPage(context)];
  }

  previousValue(context): any {
    const {years, previousPage} = this;
    return years[years.length - previousPage(context)];
  }

}
