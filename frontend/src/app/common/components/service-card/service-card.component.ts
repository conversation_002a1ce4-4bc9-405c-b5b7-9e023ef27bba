import { Component, Input } from '@angular/core';
import { ServiziAttiviService } from '../../services/servizi-attivi/servizi-attivi.service';
import { ServiceCard } from './ServiceCard';
import { OptimaIconUtils } from '../../utils/OptimaIconUtils';
import { InternetType } from '../../enum/InternetType';
import { ServiceStatus } from '../../enum/ServiceStatus';
import { MobileRecordStatus } from '../../enum/MobileRecordStatus';
import { ServiziAttiviRouteService } from '../../services/servizi-attivi-router/servizi-attivi-route.service';

@Component({
  selector: 'app-service-cart',
  templateUrl: './service-card.component.html',
  styleUrls: ['./service-card.component.scss']
})
export class ServiceCardComponent {

  serviceCard: ServiceCard;

  serviceIcon: string;

  utilityName: string;

  serviceName: string;

  @Input('card')
  set card(serviceCard: ServiceCard) {
    if (serviceCard && serviceCard.serviceName) {
      this.serviceCard = serviceCard;
      this.utilityName = this.attiviService.getNumberName(serviceCard.serviceName);
      this.serviceIcon = this.optimaIconUtils.getServiceIconByName(serviceCard.serviceName);
      this.serviceName = serviceCard.serviceName.toLowerCase() !== 'adsl' ? serviceCard.serviceName : InternetType.INTERNET;
    }
  }

  constructor(private routeService: ServiziAttiviRouteService,
              private attiviService: ServiziAttiviService, private optimaIconUtils: OptimaIconUtils) {
  }

  public redirect(serviceName, utNumber) {
    this.routeService.setRoute(serviceName, utNumber);
  }

  shouldShowEntra(utility) {
    return utility && (utility.status === ServiceStatus.ATTIVATO || utility.status === ServiceStatus.IN_ATTIVAZIONE)
      || (utility.status === MobileRecordStatus.ACTIVE || utility.status === MobileRecordStatus.INITIACTIVE);
  }

}

