import {UserServices} from '../../model/services/userServices.model';
import ServiceStateModel from '../../../redux/model/ServiceStateModel';
import {CardUtility, ServiceCard} from './ServiceCard';
import {ServiceType} from '../../enum/Service';
import {ServiceUtils} from '../../utils/ServiceUtils';
import {FibraType} from '../../enum/FibraType';
import {ServiceStatus} from '../../enum/ServiceStatus';
import {FissoType} from '../../enum/FissoType';
import {ServiziAttiviService} from '../../services/servizi-attivi/servizi-attivi.service';


const fissoCardGenerator = (service: UserServices, serviceState: ServiceStateModel) => {
  const { fissoPodDetails, activeServices} = serviceState;
  let fissoServices = [];
  Object.keys(FissoType).forEach(key => {
    const activeService = activeServices[FissoType[key]];
    if (activeService && activeService.serviceName !== service.serviceName) {
      fissoServices = fissoServices.concat(activeService.utilities);
    }
  });
  const serviceCard = new ServiceCard();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = [];
  service.utilities.forEach(utility => {
    const found = fissoServices.find(item => item.utNumber === utility.utNumber && !item.endDate);
    if (found) {
    }
    const linea = fissoPodDetails[utility.utNumber] && fissoPodDetails[utility.utNumber].variazioniLinea;
    if (!found) {
      serviceCard.utilities.push({
        status: utility.status,
        utilityNumber: utility.utNumber,
        addition: linea && linea.length ? linea[0].sede.descrizioneSede : null,
        motivazione: service.serviceName === FissoType.VOIP && utility.status === ServiceStatus.INATTIVABILE && utility.scrap,
        startDate: utility.startDate,
        endDate: utility.endDate
      } as CardUtility);
    }
  });
  return serviceCard;
};

const luceCardGenerator = (service: UserServices, serviceState: ServiceStateModel) => {
  const serviceCard = new ServiceCard();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = service.utilities.map(utility => {
    return {
      status: utility.status,
      utilityNumber: utility.utNumber,
      addition: serviceState.lucePodDetails[utility.utNumber] ? serviceState.lucePodDetails[utility.utNumber].sedeOperativa : null,
      motivazione: utility.status === ServiceStatus.INATTIVABILE && utility.scrap,
      startDate: utility.startDate,
      endDate: utility.endDate
    } as CardUtility;
  });
  return serviceCard;
};

const gasCardGenerator = (service: UserServices, serviceState: ServiceStateModel) => {
  const serviceCard = new ServiceCard();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = service.utilities.map(utility => {
    const serviceDetails = serviceState.gasPodDetails[utility.utNumber];
    const sedeOperativa = serviceDetails && serviceDetails.sedeOperativa && serviceDetails.sedeOperativa.length
      && serviceDetails.sedeOperativa[0];
    return {
      status: utility.status,
      utilityNumber: utility.utNumber,
      addition: sedeOperativa ? `${sedeOperativa.indirizzo},${sedeOperativa.comune},${sedeOperativa.provincia},
      ${sedeOperativa.cap}` : null,
      motivazione: utility.status === ServiceStatus.INATTIVABILE && utility.scrap,
      startDate: utility.startDate,
      endDate: utility.endDate
    } as CardUtility;
  });
  return serviceCard;
};

const adslCardGenerator = (service: UserServices, serviceState: ServiceStateModel) => {
  const serviceCard = new ServiceCard();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = service.utilities.map(utility => {
    const sede = serviceState.adslPodDetails[utility.utNumber] && serviceState.adslPodDetails[utility.utNumber].sede;
    return {
      status: utility.status,
      utilityNumber: utility.utNumber,
      addition: sede ? `${sede.cap},${sede.descrizioneSede}` : null,
      motivazione: service.serviceName === FibraType.ADSL && utility.status === ServiceStatus.INATTIVABILE && utility.scrap,
      startDate: utility.startDate,
      endDate: utility.endDate
    } as CardUtility;
  });
  return serviceCard;
};

const amazonCardGenerator = (service: UserServices, serviceState: ServiceStateModel) => {
  const serviceCard = new ServiceCard();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = service.utilities.map(utility => {
    return {
      status: utility.status,
      endDate: utility.endDate,
    } as CardUtility;
  });
  return serviceCard;

};

const defaultCardGenerator = (service: UserServices) => {
  const serviceCard = new ServiceCard();
  const attiviService = new ServiziAttiviService();
  serviceCard.serviceName = service.serviceName;
  serviceCard.utilities = service.utilities.map(utility => {
    return {
      status: attiviService.mobileStatusDecode(utility.status),
      utilityNumber: utility.utNumber,
      startDate: utility.startDate,
      endDate: utility.endDate
    } as CardUtility;
  });
  return serviceCard;
};

const generatorFactory = {
  [ServiceType.ENERGY]: luceCardGenerator,
  [ServiceType.GAS]: gasCardGenerator,
  [ServiceType.TELEPHONE]: fissoCardGenerator,
  [ServiceType.INTERNET]: adslCardGenerator,
  [ServiceType.AMAZON]: amazonCardGenerator,
};

export default class ServiceCardUtils {

  static getCardGenerator(serviceType: string) {
    const generator = generatorFactory[ServiceUtils.getServiceType(serviceType)];
    return generator ? generator : defaultCardGenerator;
  }

}
