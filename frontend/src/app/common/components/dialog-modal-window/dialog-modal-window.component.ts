import {Component, Input, OnInit} from '@angular/core';
import {DialogModalActions} from '../../../redux/dialogModal/actions';
import {Router} from '@angular/router';

@Component({
  selector: 'app-dialog-modal-window',
  templateUrl: './dialog-modal-window.component.html',
  styleUrls: ['./dialog-modal-window.component.scss']
})
export class DialogModalWindowComponent implements OnInit {

  @Input() marker: string;

  constructor(private dialogModalActions: DialogModalActions, private router: Router) {
  }

  ngOnInit() {
  }

  hideDialogModal() {
    this.redirectToHome();
    this.dialogModalActions.hideDialogModal();
  }

  redirectToHome() {
    if (this.marker && this.marker === 'amazon') {
      this.router.navigate(['/home/<USER>'])
    }
  }
}
