@import "~app/shared/styles/colors";
@import "~app/shared/styles/app/variables";
@import "~app/shared/styles/bootstrap/bootstrap/mixins/_clearfix";

.modal {
  margin-top: 5%;
  min-width: 65%;
}


.btnEmail {
  background: url("../../../../assets/img/bottoni-chat-segnalazione/Segnalazioni.png") no-repeat center;
  background-size: contain;
  width: 50px;
  height: 50px;

  em {
    color: #ffffff;
  }
}

.email-button {
  position: fixed;
  z-index: 20;
  margin-left: 95%;
  bottom: 5%;
}

.ngx-editor {
  color: $dark-blue;

  ::ng-deep.ngx-toolbar {
    font-size: 1.2rem !important;
    border: 1px solid $menu-border;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: white;
  }

  ::ng-deep.ngx-wrapper {
    border: none;
  }

  ::ng-deep.ngx-editor-textarea {
    border-left: 1px solid $menu-border !important;
    border-right: 1px solid $menu-border !important;
    border-top: none !important;
  }
}

.modal-dialog {
  width: 100%;
  height: 100%;
}

.modal-report-content {
  width: 65%;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid $menu-border;
  background-color: white;
  border-radius: 6px;
  outline: 0;
}

.modal-report-header {
  padding: $modal-title-padding;
  border-bottom: 1px solid $menu-border;
  opacity: 1;
  @include clearfix;
}

.modal-report-header .close {
  color: $dark-blue;
  text-shadow: none;
  opacity: 1;
  margin-top: -2px;
}

.modal-report-title {
  margin: 0;
  color: $dark-blue;
  line-height: $modal-title-line-height;
}

.modal-report-form {
  border: 1px solid $menu-border;
  background-color: white;
  border-radius: 4px;
  color: $dark-blue;
}

.modal-report-form::placeholder {
  color: $dark-blue;
  opacity: 0.5;
}

.modal-report-form:-ms-input-placeholder {
  color: $dark-blue;
  opacity: 0.5;
}

.modal-report-footer {
  padding: $modal-inner-padding;
  text-align: right;
  border-top: 1px solid $menu-border;
  @include clearfix; // clear it in case folks use .pull-* classes on buttons

  // Properly space out buttons
  .bttn + .bttn {
    margin-left: 5px;
    margin-bottom: 0; // account for input[type="submit"] which gets the bottom margin like all other inputs
  }

  // but override that for button groups
  .btn-group .bttn + .bttn {
    margin-left: -1px;
  }

  // and override it for block buttons as well
  .btn-block + .btn-block {
    margin-left: 0;
  }
}


@media only screen and (max-width: 1000px) {
  .email-button {
    margin-left: 91%;
  }
}

@media only screen and (max-width: 767px) {
  .email-button {
    position: fixed;
    z-index: 20;
    margin-left: 80%;
    bottom: 7%;
  }
}

.email-send-button {
  display: inline-block;
  font-weight: $btn-font-weight;
  cursor: pointer;
  width: 80px;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
  height: 34px;
}

.email-send-button:hover {
  background: #f0f5f9;
}

.email-decline-button {
  display: inline-block;
  font-weight: $btn-font-weight;
  cursor: pointer;
  width: 80px;
  border: 1px solid $dark-blue;
  color: white;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: $dark-blue;
  height: 34px;
}
