<div class="email-button" data-toggle="tooltip" title="Invia segnalazione">
  <a class="btn btnEmail btn-circle btn-lg" href="/faidate/segnalazioni/invia-segnalazioni">
  </a>
</div>
<div class="modal fade" bsModal #classicModal="bs-modal" tabindex="-1" role="dialog" [formGroup]="formGroup"
     aria-labelledby="mySmallModalLabel"
     aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-report-content">
      <div class="modal-report-header">
        <button type="button" class="close" aria-label="Close" (click)="closeDialog()">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-report-title">Invia una segnalazione ad Optima</h4>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <input class="form-control modal-report-form" formControlName="subject" placeholder="Oggetto.."/>

          <span class="text-danger" *ngIf="formGroup.controls['subject'].hasError('required')
          && (formGroup.controls['subject'].dirty || formGroup.controls['subject'].touched)">Campo obbligatorio.</span>

          <span class="text-danger" *ngIf="formGroup.controls['subject'].hasError('maxlength') && (formGroup.controls['subject'].dirty ||
       formGroup.controls['subject'].touched)"> La lunghezza minima è {{formGroup.controls['subject'].getError('minlength').requiredLength}}
            .
        Caratteria attuali {{formGroup.controls['subject'].getError('minlength').actualLength}}</span>

        </div>
        <div class="form-group">
            <app-ngx-editor class="ngx-editor" [minHeight]="'320px'" [placeholder]="''" [spellcheck]="true"
                            [formControlName]="'message'" [config]="{fontSize:'1.2rem'}">
            </app-ngx-editor>
          <span class="text-danger" *ngIf="formGroup.controls['message'].hasError('required')
          && (formGroup.controls['message'].dirty || formGroup.controls['message'].touched)">Il campo è obbligatorio</span>

          <span class="text-danger" *ngIf="formGroup.controls['message'].hasError('minlength') && (formGroup.controls['message'].dirty ||
       formGroup.controls['message'].touched)"> La lunghezza minima del messaggio deve essere {{formGroup.controls['message'].getError('minlength').requiredLength}}
            .
        Attualmente è di {{formGroup.controls['message'].getError('minlength').actualLength}}</span>

        </div>
      </div>
      <div class="modal-report-footer">
        <button class="bttn email-send-button" (click)="sendNewEmailMessage()">Invia</button>
        <button class="bttn email-decline-button" (click)="closeDialog()" >Chiudi</button>
      </div>
    </div>
  </div>
</div>
