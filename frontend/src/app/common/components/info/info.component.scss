//.info {
//  display: inline;
//}

.info-dropdown {
  font-size: 14px;
  position: relative;
  max-width: 320px;
  z-index: 200;
  background: #ffffff;
  border: 1px solid #b6cce3;
  //padding: 1%;
  //bottom: 70px;
  //right: 10px;
  //position: absolute;
 // width: 100px;
 // height: 20px;
  //line-height: 20px;
  padding: 10px;
  //font-size: 14px;
  text-align: center;
  color: rgb(113, 157, 171);
  //background: rgb(255, 255, 255);
 // border: 4px solid rgb(255, 255, 255);
  border-radius: 5px;
  text-shadow: rgba(0, 0, 0, 0.1) 1px 1px 1px;
  box-shadow: rgba(0, 0, 0, 0.1) 1px 1px 2px 0px;
}
//-----------------------------------------------

.top {
  min-width: 130px;
  width: 150px;
 max-width:320px;
  top:-6px;
  left:50%;
  transform:translate(-50%, -100%);
  padding:10px 10px;
  color:#444444;
  background-color:#ffffff;
  font-weight:normal;
  font-size:13px;
  border-radius:8px;
  position:absolute;
  z-index: 9999999;
  box-sizing:border-box;
  box-shadow:0 1px 8px rgba(0,0,0,0.5);
}

.top-position {
  left: 150px;
}

.top i {
  position:absolute;
  top:100%;
  left:50%;
  margin-left:-12px;
  width:24px;
  height:12px;
  overflow:hidden;
}

 .top i::after {
  content:'';
  position:absolute;
  width:12px;
  height:12px;
  left:50%;
  transform:translate(-50%,-50%) rotate(45deg);
  background-color:#ffffff;
  box-shadow:0 1px 8px rgba(0,0,0,0.5);
}
//------------------------------
@media only screen and (max-width: 768px) {
  .info-dropdown {
    bottom: 85px;
  }
}
