import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';

@Component({
  selector: 'app-info',
  templateUrl: './info.component.html',
  styleUrls: ['./info.component.scss']
})
export class InfoComponent implements OnInit {

  show: boolean;
  @Input('name')
  name: string;

  @ViewChild('infoDropdown')
  set infoDropdown(infoDropdown: ElementRef) {
    if (infoDropdown) {
      infoDropdown.nativeElement.focus();
    }
  }

  constructor() {
  }

  ngOnInit() {
  }


  showHideInfo() {
    this.show = !this.show;
  }


}
