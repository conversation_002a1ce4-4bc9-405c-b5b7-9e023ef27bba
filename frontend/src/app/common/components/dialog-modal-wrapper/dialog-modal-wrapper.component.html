<app-dialog-modal-window *ngIf="shouldShowDialogModal | async" [marker]="marker"
                         [ngClass]="isFontItalic? 'italic-font' : 'straight-font'">
  <img *ngIf="dialogModal?.img" class="image modal-image" src="{{dialogModal.img}}">
  <span *ngIf="dialogModal?.title && !dialogModal?.customTitle else customTitle" class="title modal-title">{{dialogModal.title}}</span>
  <div class="text modal-text">{{dialogModal.text}}</div>

  <div class="data-form row user-data-table-block" *ngIf="dialogModal.hasDataForm">
    <form [formGroup]="form">
      <div *ngFor="let item of dialogModal.dataFormSetting">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row" [ngStyle]="item.rowStyle">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">{{item.labelText}}</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data" *ngIf="item.dropdownValues && item.formName !== 'canoneRai'">
        <span class="dropdown-container">
          <select #dropdown class="dropdown-select" [ngStyle]="isPlaceHolder(item.formName)" [formControlName]="item.formName"
                  (change)="onChangeSelected(item.formName, dropdown.value)">
            <option value="" [selected]="true" data-default>{{item.placeholder}}</option>
            <option [ngValue]="val.value" *ngFor="let val of item.dropdownValues">
              {{val.text}}</option>
          </select>
        </span>
            <span class="text-danger input-wrong-field"
                  *ngIf="!scadenzaFormControl.value.length && item.formName === 'scadenzaRate' && isScadenxaEmpty">inserisci cadenza rate</span>
            <span class="text-danger input-wrong-field"
                  *ngIf="!casualeFormControl.value.length && item.formName === 'casuale' && isCasualeEmpty">inserisci il motivo della richiesta</span>
            <span class="text-danger input-wrong-field"
                  *ngIf="!importoDilazioneFormControl.value.length && item.formName === 'importoDilazione' && isImportoDilazioneEmpty">inserisci importo dilazione</span>
          </div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data" *ngIf="item.dropdownValues && item.formName === 'canoneRai'">
        <span class="dropdown-container">
          <select class="dropdown-select" [formControlName]="item.formName" [attr.disabled] = "isDisabledCanoneRai() ? 'disabled' : null">
            <option [ngValue]="val.value" *ngFor="let val of item.dropdownValues">
              {{val.text}}</option>
          </select>
        </span>
          </div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data" *ngIf="item.value && !item.editable">
            <input class="form-input" type="text" [formControlName]="item.formName" [ngStyle]="item.rowStyle" [attr.disabled] = true>
          </div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data" *ngIf="item.editable">
            <app-user-data-editor (editFieldsEventEmitter)="checkForEditingFields($event)" [formGroup]="form" [name]="item.formName"
                                  [modifiable]="true" [emptyOnEdit]="false"
                                  [onAccept]="" [forForm]="formName"
                                  [placeholder]="item.placeholder">
              <confirm-message>
              <span
                class="confirm-message">{{getConfirmationText(item.formName)}}</span>

              </confirm-message>
            </app-user-data-editor>
            <span class="text-danger input-wrong-field"
            *ngIf="(numeroRateFormControl.value === null || numeroRateFormControl.value.length === 0)
            && item.formName === 'numeroRate' && isNumeroRateEmpty">inserisci numero rate</span>
            <span class="text-danger input-wrong-field"
            *ngIf="(emailFormControl.value === null || emailFormControl.value.length === 0)
            && item.formName === 'email' && isEmailEmpty">inserisci e-mail</span>
            <span class="text-danger input-wrong-field"
            *ngIf="(phoneNumberFormControl.value === null || phoneNumberFormControl.value.length === 0)
            && item.formName === 'phoneNumber' && isPhoneNumberEmpty">inserisci numero di telefono</span>
            <div class="text-danger input-wrong-field no-padding"
                 *ngIf="editingNumeroRate && item.formName === 'numeroRate'">
              <span class="confirm-description">conferma i dati cliccando su  <span
                class="fa fa-check accept-button"></span> </span>
            </div>
            <div class="text-danger input-wrong-field no-padding"
                 *ngIf="editingEmail && item.formName === 'email'">
              <span class="confirm-description">conferma i dati cliccando su  <span
                class="fa fa-check accept-button"></span> </span>
            </div>
            <div class="text-danger input-wrong-field no-padding"
                 *ngIf="editingPhoneNumber && item.formName === 'phoneNumber'">
              <span class="confirm-description">conferma i dati cliccando su  <span
                class="fa fa-check accept-button"></span> </span>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="buttons modal-buttons" *ngIf="dialogModal.hasDataForm && !dialogModal.nextEntities">
    <div class="cancel-button" (click)="childWindow.hideDialogModal()" *ngIf="dialogModal.withAnullaButton">ANNULLA
    </div>
    <div class="continue-button"
         (click)="onSubmit()">
      PROCEDI
    </div>
  </div>

  <div class="buttons modal-buttons" *ngIf="dialogModal.hasDataForm && dialogModal.nextEntities">
    <div class="cancel-button" (click)="childWindow.hideDialogModal()" *ngIf="dialogModal.withAnullaButton">ANNULLA
    </div>
    <div class="continue-button"
         (click)="onSubmitSecondModal()">
      PROCEDI
    </div>
  </div>

  <div
    *ngIf="dialogModal.hasButtons && !dialogModal.customButtons && !dialogModal.hasFillField && !dialogModal.dataFormSetting else fieldFormTemplate"
    class="buttons modal-buttons">
    <div class="cancel-button" (click)="childWindow.hideDialogModal()">ANNULLA</div>
    <div class="continue-button"
         (click)="dialogModal?.nextEntities? executeNext(): dialogModal.callbackToExecuteOnSubmit? executeFunctionBySubmit() : childWindow.hideDialogModal()">
      PROCEDI
    </div>
  </div>

  <div
    *ngIf="dialogModal.hasButtons && dialogModal.customButtons"
    class="buttons modal-buttons">
    <div class="accept-modal-button"
         (click)="dialogModal?.nextEntities? executeNext(): dialogModal.callbackToExecuteOnSubmit? executeFunctionBySubmit() : childWindow.hideDialogModal()">
      {{dialogModal.customButtons.accept}}
    </div>
    <div class="cancel-button" (click)="childWindow.hideDialogModal()">{{dialogModal.customButtons.cancel}}</div>
  </div>

  <ng-template #customTitle>
    <span class="title modal-title custom-title">{{dialogModal.title}}</span>
  </ng-template>
  <ng-template #fieldFormTemplate>
    <div *ngIf="dialogModal?.hasButtons && dialogModal?.hasFillField">
      <form [formGroup]="form" (ngSubmit)="executeNext($event)">
        <div class="text-danger input-wrong-code" *ngIf="hasError">
          Codice errato!
        </div>
        <input class="fill-field modal-fill-field" formControlName="inputField"
               [ngClass]="{'form-submitted': formSubmitted}">
        <div class="buttons modal-buttons">
          <div class="cancel-button" (click)="childWindow.hideDialogModal()">ANNULLA</div>
          <button class="continue-button" type="submit">PROCEDI</button>
        </div>
      </form>
    </div>
  </ng-template>
  <app-spinner class="spinner" *ngIf="isShowSpinner"></app-spinner>
</app-dialog-modal-window>
