import {Component, OnDestroy, OnInit} from '@angular/core';
import {ModalEntity} from '../../model/modal/ModalEntity';
import {select} from '@angular-redux/store';
import {Subscription} from 'rxjs/Subscription';
import {Observable} from 'rxjs/Observable';
import {ObservableUtils} from '../../utils/ObservableUtils';

@Component({
  selector: 'app-modal-wrapper',
  templateUrl: './modal-wrapper.component.html',
  styleUrls: ['./modal-wrapper.component.scss']
})
export class ModalWrapperComponent implements OnInit, OnDestroy {

  modal: ModalEntity;

  @select(['modal', 'modalEntity'])
  modalEntity: Observable<ModalEntity>;

  @select(['modal', 'show'])
  shouldShowModal: Observable<boolean>;

  modalSubscription: Subscription;

  constructor() {
  }

  ngOnInit() {
    this.modalSubscription = this.modalEntity.subscribe(entity => {
      this.modal = entity;
    });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.modalSubscription]);
  }
}
