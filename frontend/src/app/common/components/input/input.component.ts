import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.scss']
})
export class InputComponent implements OnInit {

  @Input('title') title: string;

  @Input('name') name: string;

  @Input('type') type: string;

  @Input('inputLength') inputLength = 6;

  @Input('disabled') disabled: boolean;

  @Input('required') required = false;

  @Input('formGroup') formGroup: FormGroup;

  @Input('step') step: any;

  @Input('placeholder') placeholder: string;

  constructor() {
  }

  ngOnInit() {
  }

}
