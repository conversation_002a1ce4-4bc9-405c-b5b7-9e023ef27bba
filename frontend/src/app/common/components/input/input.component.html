<div [formGroup]="formGroup" *ngIf="formGroup">
  <input *ngIf="!type || type !=='area'" class="form-control app-input" [attr.type]="type" [formControlName]="name"
         step="{{step}}"
         [formControl]="formGroup.controls[name]" [attr.disabled]="disabled" [attr.placeholder]="placeholder"/>

  <textarea *ngIf="type ==='area'" class="form-control" [attr.type]="type" [formControlName]="name"
            [formControl]="formGroup.controls[name]" [attr.disabled]="disabled"
            [attr.placeholder]="placeholder"></textarea>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('required') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">Campo obbligatorio.</span>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('max') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">
        Max value is {{formGroup.controls[name].getError('max').max}}.</span>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('min') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">
        Min value is {{formGroup.controls[name].getError('min').min}}.</span>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('invalidValue') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">
        Invalid value.</span>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('error') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">
        {{formGroup.controls[name].errors.error}}</span>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('increase') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">
        {{formGroup.controls[name].errors.increase}}</span>
  <span class="text-danger" *ngIf="formGroup.controls[name].hasError('minlength') && (formGroup.controls[name].dirty ||
       formGroup.controls[name].touched)">Lunghezza minima {{formGroup.controls[name].getError('minlength').requiredLength}} caratteri.</span>
  <span class="text-danger" *ngIf="formGroup.controls[name].hasError('maxlength') && (formGroup.controls[name].dirty ||
       formGroup.controls[name].touched)">Lunghezza massima {{formGroup.controls[name].getError('maxlength').requiredLength}} caratteri.</span>
</div>
