import {Component, OnInit, ViewChild} from '@angular/core';
import {DialogModalEntity} from '../../model/dialogModal/DialogModalEntity';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {MonthModalWindowComponent} from '../month-modal-window/month-modal-window.component';
import {MonthEntity} from '../../model/monthModal/MonthEntity';

@Component({
  selector: 'app-month-modal-wrapper',
  templateUrl: './month-modal-wrapper.component.html',
  styleUrls: ['./month-modal-wrapper.component.scss']
})
export class MonthModalWrapperComponent implements OnInit {

  @ViewChild(MonthModalWindowComponent)
  childWindow: MonthModalWindowComponent;

  @select(['monthModal', 'dialogModalEntity'])
  dialogModalEntity: Observable<DialogModalEntity>;

  @select(['monthModal', 'show'])
  shouldShowMonthModal: Observable<boolean>;

  dialogModal: DialogModalEntity;

  constructor() {
  }

  ngOnInit() {
    this.dialogModalEntity.subscribe(entity => this.dialogModal = entity);
  }

  selectMonth(monthEntity: MonthEntity, months: Array<MonthEntity>) {
    if (monthEntity.unableToSelect || !monthEntity.clickable) {
      return;
    }
    months.forEach(month => month.selected = false);
    monthEntity.selected = !monthEntity.selected;
  }

  confirmMonthSelection() {
    const selectedMonth = this.dialogModal.data.find(month => month.selected);
    const meseIndex = this.dialogModal.currentMeseIndex;

    this.dialogModal.callbackToExecuteOnSubmit(selectedMonth, meseIndex);
  }
}
