import {Component, OnInit} from '@angular/core';
import {ModalActions} from '../../../redux/modal/actions';

@Component({
  selector: 'app-modal-window',
  templateUrl: './modal-window.component.html',
  styleUrls: ['./modal-window.component.scss']
})
export class ModalWindowComponent implements OnInit {

  constructor(private modalActions: ModalActions) {
  }

  ngOnInit() {
  }

  hideModal() {
    this.modalActions.hideModal();
  }
}
