import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Component({
  selector: 'app-confirm-window',
  templateUrl: './confirm-window.component.html',
  styleUrls: ['./confirm-window.component.scss']
})
export class ConfirmWindowComponent implements OnInit {

  @Input('decline') decline: Function;

  @Input('confirm') confirm: Function;

  @Input('showConfirmDialog') showConfirmDialog: boolean;

  @Input('control') formControl: AbstractControl;

  constructor() {
  }

  ngOnInit() {
  }

}
