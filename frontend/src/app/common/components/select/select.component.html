<div [formGroup]="formGroup" *ngIf="formGroup">
  <select class="form-control" [formControlName]="name" [formControl]="formGroup.controls[name]">
    <option *ngIf="emptyLabel" value="" [selected]="!formGroup.controls[name].value">{{emptyLabel}}</option>
    <option *ngIf="!emptyLabel" value=""></option>
    <option *ngFor="let item of options" [attr.value]="item" [selected]="item===formGroup.controls[name].value">
      {{item}}
    </option>
    <option *ngFor="let item of optionsMap | keys" [attr.value]="item"
            [selected]="item===formGroup.controls[name].value">
      {{optionsMap[item]}}
    </option>
  </select>
  <span class="text-danger"
        *ngIf="formGroup.controls[name].hasError('required') && (formGroup.controls[name].dirty || formGroup.controls[name].touched)">Campo Obbligatorio</span>
</div>
<div class="col-sm-4">
  <small *ngIf="required&&!disabled" class="text-muted">required</small>
</div>

