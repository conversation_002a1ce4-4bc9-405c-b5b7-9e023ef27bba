import {Component, Input} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../utils/FormUtils';
import Validator from '../utils/Validator';

@Component({
  selector: 'app-address-editor',
  templateUrl: './address-editor.component.html',
  styleUrls: ['./address-editor.component.scss']
})
export class AddressEditorComponent {

  formGroup: FormGroup;

  formControl: FormControl;

  @Input('title')
  title: string;

  @Input('name')
  name: string;

  @Input('onAccept')
  onAccept: Function;

  @Input('options')
  options;

  @Input('emptyOnEdit')
  emptyOnEdit = false;

  @Input('placeholder')
  placeholder = '';

  isEditing: boolean;

  showConfirmDialog = false;

  initialValue: string;

  showCheckboxDialog = false;

  checkboxValue: string;

  addressFromGroup: FormGroup;

  constructor(fb: Form<PERSON>uilder) {
    this.addressFromGroup = fb.group({
      toponimo: [null, {validators: [Validators.required, Validators.minLength(3), Validators.maxLength(15), Validator.onlyTextAllowed()]}],
      via: [null, {validators: [Validators.required, Validators.minLength(3), Validators.maxLength(50), Validator.onlyTextNumberAllowed()]}],
      civico: [null, {validators: [Validators.minLength(1), Validators.maxLength(8), Validator.onlyTextNumberAllowed()]}],
      cap: [null, {validators: [Validators.required, Validators.minLength(5), Validators.maxLength(6), Validator.digits()]}],
      comune: [null, {validators: [Validators.required, Validators.minLength(3), Validators.maxLength(50), Validator.onlyTextAllowed()]}]
    });
  }

  @Input('formGroup')
  set group(formGroup: FormGroup) {
    if (formGroup && this.name) {
      this.formControl = <FormControl>formGroup.controls[this.name];
    }
    this.formGroup = formGroup;
  }

  showHideConfirmationPopup() {
    this.showConfirmDialog = !this.showConfirmDialog;
  }

  accept() {
    FormUtils.setFormControlAsTouched(this.formControl);
    FormUtils.setFormControlsAsTouched(this.addressFromGroup);
    if (this.addressFromGroup.valid && (this.formControl.valid || !this.options)) {
      this.showHideConfirmationPopup();
    }
  }

  confirm() {
    this.isEditing = false;
    this.showHideConfirmationPopup();
    if (this.onAccept) {
      // newValue, oldValue
      this.onAccept(this.addressFromGroup.value, this.formControl.value).subscribe();
      this.addressFromGroup.reset();
      this.formGroup.reset();
    }
  }

  edit() {
    this.isEditing = true;
    this.initialValue = this.formControl.value;
    if (this.options && this.options.length > 1) {
      this.formControl.setValue(null);
    }
    if (this.options && this.options.length === 1 && !this.formControl.value) {
      this.formControl.setValue(this.options[0]);
    }
  }

  closeEditor() {
    this.formControl.setValue(this.initialValue, {emitEvent: false});
    this.addressFromGroup.reset();
    this.isEditing = false;
  }

  closeCheckbox() {
    this.formControl.setValue(this.checkboxValue);
    this.showHideCheckbox();
  }

  showHideCheckbox() {
    this.showCheckboxDialog = !this.showCheckboxDialog;
  }

  acceptCheckboxDialog() {
    this.showHideCheckbox();
  }

  initializeCheckbox() {
    this.edit();
    this.checkboxValue = this.formControl.value;
    this.showHideCheckbox();
  }

  openCheckboxDialog() {
    this.isEditing = true;
    this.checkboxValue = this.formControl.value;
    this.showHideCheckbox();
  }
}
