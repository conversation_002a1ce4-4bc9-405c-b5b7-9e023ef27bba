import { Injectable } from '@angular/core';

@Injectable()
export class MenuService {

    menuItems: Array<any>;
    sideMenu: Array<any>;
    menuWithSubMenu: Array<any>;
    serviceMenu: Array<any>;
    menuItemsWithChildren: Object;

    constructor() {
      this.menuItems = [];
      this.sideMenu = [];
      this.menuWithSubMenu = [];
      this.serviceMenu = [];
      this.menuItemsWithChildren = {};
    }

    addMenu(items: Array<{
        text: string,
        heading?: boolean,
        link?: string,     // internal route links
        elink?: string,    // used only for external links
        target?: string,   // anchor target="_blank|_self|_parent|_top|framename"
        icon?: string,
        alert?: string,
        submenu?: Array<any>
    }>) {
        items.forEach((item) => {
            this.menuItems.push(item);
        });
    }

  addMenuWithSubMenu(items: Array<{
    text: string,
    link?: string,
    submenu?: Array<any>
  }>) {
    items.forEach((item) => {
      this.menuWithSubMenu.push(item);
    });
  }
  addServiceMenu(items: Array<{
    text: string,
    link?: string,
    icon?: string,
    active: boolean,
    isScolitoPresent: boolean;
  }>) {
    items.forEach((item) => {
      this.serviceMenu.push(item);
    });
  }
  addSideMenu(items: Array<{
    text: string,
    subText: string,
    link?: string,     // internal route links
    icon?: string,
    display: boolean,
    submenu?: Array<any>
  }>) {
    items.forEach((item) => {
      this.sideMenu.push(item);
    });
  }
  getMenu() {
        return this.menuItems;
    }
  getSideMenu() {
    return this.sideMenu;
  }
  geMenuWithSubMenu() {
    return this.menuWithSubMenu;
  }

  getServiceMenu() {
    return this.serviceMenu;
  }
  getMenuItemsWithChildLinks() {
      return this.menuItemsWithChildren;
  }
}
