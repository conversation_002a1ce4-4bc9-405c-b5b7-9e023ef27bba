export enum ProfileLayoutRoutes {
  USER_DATA = '/profile/data',
  // CONFIRM_PAYMENT = '/profile/data/confirm-payment',
  YOUR_CONTRACTS = '/profile/contract',
  ALL_IN_ONE = '/profile/tutto-in-uno',
  SHIPMENTS = '/profile/shipment',
  COMMUNICATION = '/profile/comunicazioni',
  UTILITIES_HISTORY = '/profile/storico/utenze',
  ENERGY_CARD = '/profile/card/energy',
  MOBILE_CARD = '/profile/card/mobile'
}

export enum FaiDaTeLayoutRoutes {
  ROUTER_CONFIGURATION_PREFIX = '/faidate/routerConfigurazione',
  YOUR_SERVICES = '/faidate/servizi-attivi',
  YOUR_SERVICES_MOBILE = '/faidate/servizi-attivi/mobile',
  YOUR_SERVICES_LUCE = '/faidate/servizi-attivi/energia',
  YOUR_SERVICES_GAS = '/faidate/servizi-attivi/gas',
  YOUR_SERVICES_VOICE = '/faidate/servizi-attivi/wlr',
  YOUR_SERVICES_FIBRA = '/faidate/servizi-attivi/adsl',
  YOUR_SERVICES_AMAZON_PRIME = '/faidate/servizi-attivi/amazon-prime',
  YOUR_SERVICES_TELECONSULTO_MEDICO = '/faidate/servizi-attivi/teleconsulto-medico',
  YOUR_SERVICES_TUTELA_LEGALE = '/faidate/servizi-attivi/tutela-legale',
  YOUR_SERVICES_ASSISTENZA_H24 = '/faidate/servizi-attivi/assistenza-h24',
  YOUR_SERVICES_TOTAL_SECURITY = '/faidate/servizi-attivi/total-security',
  YOUR_SERVICES_SAFE_CALL = '/faidate/servizi-attivi/safe-call',
  YOUR_REPORTS = '/faidate/segnalazioni',
  AUTO_READING = '/faidate/autolettura',
  ROUTER_CONFIGURATION = '/faidate/routerConfigurazione/configuraIlTuoRouter',
  ROUTER_SOFTWARE = '/faidate/routerConfigurazione/aggiorna',
  RECONTACT_REQUEST = '/faidate/recontact'
}

export enum QuestionAnswerLayoutRoutes {
  ASSICURAZIONI = '/support/questions/assicurazioni',
  DEVICE = '/support/questions/device',
  LUCE = '/support/questions/ee',
  GAS = '/support/questions/gas',
  MOBILE = '/support/questions/conto-relax',
  TUTTO_IN_UNO = '/support/questions/pi',
  VOCE_INTERNET = '/support/questions/voce-adsl',
  FATTURA = '/support/questions/fattura',
  INFO_GENERICHE = '/support/questions/info-generice',
}
