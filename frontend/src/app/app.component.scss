.footerBgMobile .chat-button {
  position: static !important;
}

body {
  font-family: 'Lato-Regular';
  font-style: normal;
}

@media screen and (max-width: 800px) {
  .desktop-hidden {
    display: initial;
  }
  .mobile-hidden {
    display: none;
  }
}

@media screen and (min-width: 800px) {
  .desktop-hidden {
    display: none;
  }
  .mobile-hidden {
    display: initial;
  }
}

.mat-button, .mat-flat-button, .mat-icon-button, .mat-stroked-button {
  min-width: auto !important;
}
