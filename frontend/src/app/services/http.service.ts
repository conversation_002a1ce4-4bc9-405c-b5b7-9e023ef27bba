import {Injectable} from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

const httpOptions = {
  headers: new HttpHeaders({ 'Content-Type': 'application/json' })
};



@Injectable()
export class HttpService {

  constructor(private httpClient: HttpClient) {
  }
  // http.get() to load data from a single API endpoint

  get(url, headers) {
    return this.httpClient.get(url, {headers: headers});
  }
  // send a POST request to the API to create a new data object
 post(url, data) {
    const body = JSON.stringify(data);
    return this.httpClient.post(url, body, httpOptions);
  }
}
