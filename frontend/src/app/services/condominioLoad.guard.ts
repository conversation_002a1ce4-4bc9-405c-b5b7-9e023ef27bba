import {Injectable} from '@angular/core';
import {CanLoad, Route, Router} from '@angular/router';
import {Observable} from 'rxjs/Observable';

@Injectable()
export class CondominioLoadGuard implements CanLoad {

  constructor(private router: Router) {
  }

  canLoad(route: Route): Observable<boolean> | Promise<boolean> | boolean {
    if (localStorage.getItem('sottotipoCluster') !== 'Condominio') {
      this.router.navigate(['/home/<USER>']);
      return false;
    }
    return true;
  }

}
