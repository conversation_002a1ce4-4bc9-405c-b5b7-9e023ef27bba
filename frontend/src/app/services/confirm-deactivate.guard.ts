import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanDeactivate} from '@angular/router';
import {UserTuttoInUnoComponent} from '../routes/profilePage/containers/userTuttoInUno/user-tutto-in-uno.component';


@Injectable()
export class ConfirmDeactivateGuard implements CanDeactivate<UserTuttoInUnoComponent> {

  canDeactivate(component: UserTuttoInUnoComponent, currentRoute: ActivatedRouteSnapshot) {
    return component.allowRedirectAndShowDeactivateInfoModalWithUnSavedChanges();
  }


}
