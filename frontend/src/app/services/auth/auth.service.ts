import {Injectable} from '@angular/core';
import {JwtHelperService} from '@auth0/angular-jwt';
import {SessionActions} from '../../redux/session/session.actions';
import {ISession} from '../../redux/session/session.types';
import {NgRedux} from '@angular-redux/store';
import {Router} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {HttpService} from '../http.service';
import {APP_LOCAL_STORAGE_KEYS} from '../../shared/app.constant';
import {HttpBackend, HttpClient} from '@angular/common/http';
import {catchError, map} from 'rxjs/operators';
import {of} from 'rxjs/observable/of';

const helper = new JwtHelperService();

@Injectable()
export class AuthService {

  redirectUrl: string; // store the URL so we can redirect after logging in
  private urlLogin = 'loginJWT';
  private urlAdminLogin = 'loginAdminJWT';
  private urlRefresh = 'api/getCredentials';
  private urlForLoginCheck = 'api/checkIfUserAlreadyRestoredPassword';
  private urlForAllUsersEmailCheck = 'api/getAllUsersWithTheSameEmail';
  private urlForSSOTokenCheck = '/api/validSSOToken';
  isLoggedIn = false;
  isRemember = false;
  sottopitoCluster = '';
  private httpClient: HttpClient;

  constructor(// public jwtHelper: JwtHelperService,
    private sessionActions: SessionActions,
    private ngRedux: NgRedux<any>,
    private router: Router,
    private httpService: HttpService,
    handler: HttpBackend
  ) {
    this.httpClient = new HttpClient(handler);
  }

  public getToken(): string {
    return localStorage.getItem('token');
  }

  public isAuthenticated(): boolean {
    // get the token
    const token = this.getToken();
    // return a boolean reflecting
    // whether or not the token is expired
    return helper.isTokenExpired(token);
  }

  public login(loginData: any, queryStringParams: string): Observable<any> {
    this.sessionActions.loginUser();
    return this.httpService.post(this.urlLogin + queryStringParams, loginData)
      .do(
        (data: any) => {
          this.isRemember = loginData.isRemember;
          localStorage.setItem(APP_LOCAL_STORAGE_KEYS.accessToken, data['accessToken']);
          this.decodeTokenAndSetPropertiesInStorage(data.accessToken);
          this.loginSuccess(data);
          this.isLoggedIn = true;
          // redirect to route

          if (data['redirectUri']) {
            window.location.href = data['redirectUri'];
            return;
          }
          let redirect;
          if (localStorage.getItem('sottotipoCluster') === 'Condominio') {
            redirect = '/gestione-condomini';
          } else {
            redirect = this.redirectUrl ? this.redirectUrl : '/home/<USER>';
          }
          this.router.navigate([redirect]);
        },
        error => {
          // Check if the error is due to a deleted account
          if (error.error && error.error.error === 'deleted_account') {
            throw {deletedAccount: true};
          } else {
            this.loginError(error);
          }
        }
      );
  }

  public loginAdmin(loginData: any): Observable<any> {
    this.sessionActions.loginUser();
    return this.httpService.post(this.urlAdminLogin, loginData)
      .do(
        data => {

          this.isLoggedIn = true;
          this.isRemember = loginData.isRemember;
          localStorage.setItem(APP_LOCAL_STORAGE_KEYS.sessionData, JSON.stringify(data));
          this.loginSuccess(data);
          // redirect to route
          const redirect = this.redirectUrl ? this.redirectUrl : '/home/<USER>';
          this.router.navigate([redirect]);
        },
        error => {
          this.loginError(error);
        }
      );
  }

  // updating the token through refresh token (with claims)
  public refreshToken() {
    const data: any = {
      refreshToken: this.ngRedux.getState().session.refresh_token
    };
    this.httpService.get(this.urlRefresh, data)
      .subscribe(
        (respose: any) => {
          this.loginSuccess(respose);
        },
        (error: any) => {
          this.loginError(error);
        }
      );
  }

  public checkIfUserAlreadyRestoredPassword(fields): Observable<any> {
    return this.httpService.post(this.urlForLoginCheck, fields);
  }

  public getAllUsersWithTheSameEmail(fields): Observable<any> {
    return this.httpService.get(this.urlForAllUsersEmailCheck, fields);
  }

  public goHome() {
    this.router.navigate(['/home/<USER>']);
  }

  public logout() {
    localStorage.removeItem(APP_LOCAL_STORAGE_KEYS.sessionData);
    localStorage.removeItem(APP_LOCAL_STORAGE_KEYS.accessToken);
    localStorage.clear();
    this.sessionActions.logoutUser();
    location.reload();
  }

  public checkLogin() {
    if (localStorage.getItem(APP_LOCAL_STORAGE_KEYS.accessToken)) {
      const data = {accessToken: localStorage.getItem(APP_LOCAL_STORAGE_KEYS.accessToken)};
      this.loginSuccess(data);
      this.isLoggedIn = true;
    } else {
      this.isLoggedIn = false;
    }
    return this.isLoggedIn;
  }

  public checkSSOLogin(token: string, clientId: string) {
    const data = {token: token, clientId: clientId};
    return this.httpService.post(this.urlForSSOTokenCheck, data).pipe(
      map((response: any) => {
        this.isRemember = false;
        localStorage.setItem(APP_LOCAL_STORAGE_KEYS.accessToken, response['accessToken']);
        this.decodeTokenAndSetPropertiesInStorage(response.accessToken);
        this.loginSuccess(response);
        this.isLoggedIn = true;
        return true;
      }),
      catchError((error) => {
        this.loginError(error);
        this.isLoggedIn = false;
        return of(false);
      })
    );
  }

  private loginSuccess(data: any) {
    this.sottopitoCluster = localStorage.getItem('sottotipoCluster');

    // This if allow to decode token and set data to storage for Condominio user
    // only for login, because in condominio component we inject clientId for services implicitly
    if (!this.isLoggedIn && this.sottopitoCluster === 'Condominio' || this.sottopitoCluster !== 'Condominio') {
      this.decodeTokenAndSetPropertiesInStorage(data.accessToken);
    }

    const session: ISession = {
      access_token: data.accessToken,
      refresh_token: data.refreshToken,
      user: data,
      hasError: false,
      isLoading: false
    };
    this.sessionActions.loginUserSuccess(session);

    // save data in localStorage
    if (this.isRemember) {
      localStorage.setItem(APP_LOCAL_STORAGE_KEYS.sessionData, JSON.stringify(session));
    }
  }

  decodeTokenAndSetPropertiesInStorage(token: any) {
    const decodeToken = helper.decodeToken(token);
    localStorage.setItem('clientId', decodeToken['id']);
    localStorage.setItem('sottotipoCluster', decodeToken['sottotipoCluster']);
    localStorage.setItem('clientName', decodeToken['name']);
    localStorage.setItem('clientSName', decodeToken['sName']);
  }

  private loginError(error: any) {
    this.sessionActions.loginUserError();
    console.error(error);
  }
}
