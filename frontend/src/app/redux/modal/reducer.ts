import {Type} from './types';
import ModalStateModel from '../model/ModalStateModel';

const initialState = new ModalStateModel();

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.SHOW_MODAL:
      return Object.assign({}, {show: true, modalEntity: action.modalEntity});
    case Type.HIDE_MODAL:
      return Object.assign({}, {show: false});
    default:
      return state;
  }
}
