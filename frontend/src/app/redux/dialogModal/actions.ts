import {dispatch} from '@angular-redux/store';
import {DialogModalEntity} from '../../common/model/dialogModal/DialogModalEntity';
import {Type} from './types';


export class DialogModalActions {
  @dispatch()
  showDialogModal = (dialogModalEntity: DialogModalEntity) => ({
    type: Type.SHOW_DIALOG_MODAL,
    dialogModalEntity: dialogModalEntity
  })

  @dispatch()
  hideDialogModal = () => ({
    type: Type.HIDE_DIALOG_MODAL
  })

}
