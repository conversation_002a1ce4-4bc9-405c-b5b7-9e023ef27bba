import { Type } from './types';
import { dispatch, select } from '@angular-redux/store';
import { Injectable } from '@angular/core';
import { ContractRecord } from '../../common/model/mobile/contract-record/ContractRecord';
import { Observable } from 'rxjs/Observable';
import { MobileState } from '../model/MobileState';


@Injectable()
export class MobileActions {

  @select(['mobile'])
  private readonly mobile: Observable<MobileState>;

  private mobileState: MobileState;

  constructor() {
    this.mobile.subscribe(mobileState => {
      this.mobileState = mobileState;
    });
  }

  @dispatch()
  contractRecordsLoaded = (contractRecords: Array<ContractRecord>) => ({
    type: Type.CONTRACT_RECORDS_LOADED,
    contractRecords
  })

  @dispatch()
  contractRecordsLoading = (clientId) => ({
    type: Type.CONTRACT_RECORDS_LOADING,
    clientId
  })

  loadContractRecordsIfNotExist(clientId: string) {
    const {contractRecordsLoading, contractRecordsLoaded} = this.mobileState;
    if (!contractRecordsLoading && !contractRecordsLoaded) {
      this.contractRecordsLoading(clientId);
    }
  }

}
