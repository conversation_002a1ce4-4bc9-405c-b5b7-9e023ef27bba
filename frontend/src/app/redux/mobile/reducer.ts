import { Type } from './types';
import { MobileState } from '../model/MobileState';

const initialState = {
  contractRecords: []
} as MobileState;

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.CONTRACT_RECORDS_LOADING:
      return {...state, contractRecordsLoading: true};
    case Type.CONTRACT_RECORDS_LOADED:
      return {
        ...state,
        contractRecords: action.contractRecords,
        contractRecordsLoading: false,
        contractRecordsLoaded: true
      };
    default:
      return state;
  }

}
