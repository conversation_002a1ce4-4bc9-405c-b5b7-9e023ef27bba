import {Injector, NgModule} from '@angular/core';
import createSagaMiddleware from 'redux-saga';

import {DevToolsExtension, NgRedux, NgReduxModule} from '@angular-redux/store';
import {NgReduxRouter, NgReduxRouterModule} from '@angular-redux/router';
// Redux ecosystem stuff.
import {rootReducer} from './reducers';
import {CommonModule} from '@angular/common';
import {IAppState} from './model';
import {NotificationActions} from './notification/actions';
import {SpinnerAction} from './spinner/actions';
import {UserActions} from './user/actions';
import {ServicesActions} from './services/actions';
import {MobileActions} from './mobile/actions';
import {SessionActions} from './session/session.actions';
import {ContabileActions} from './contabile/actions';
import {ChatActions} from './chat/actions';
import {CommunicationActions} from './communication/actions';

import {rootSaga} from './saga/root';
import {ModalActions} from './modal/actions';
import {DialogModalActions} from './dialogModal/actions';
import {NotificaMdpActions} from './notifica-mdp/actions';
import {ModalInputEmitActions} from './dialogModalInputEmit/actions';
import {SaldoActions} from './saldo/actions';
import {RichiediActions} from './richiedi/actions';
import {MonthModalActions} from './monthModal/actions';

@NgModule({
  imports: [NgReduxModule, NgReduxRouterModule, CommonModule],
  providers: [
    NotificationActions, NgReduxRouter, SpinnerAction, UserActions, ServicesActions, MobileActions, SessionActions,
    ChatActions, ContabileActions, CommunicationActions, ModalActions, DialogModalActions, MonthModalActions,
    ModalInputEmitActions, NotificaMdpActions, SaldoActions, RichiediActions]
})
export class ReduxModule {
  constructor(
    public store: NgRedux<IAppState>,
    devTools: DevToolsExtension,
    ngReduxRouter: NgReduxRouter,
    private injector: Injector
  ) {
    const sagaMiddleware = createSagaMiddleware({
      context: {
        context: this.injector
      }
    });
    // Tell Redux about our reducers and epics. If the Redux DevTools
    // chrome extension is available in the browser, tell Redux about
    // it too.
    store.configureStore(
      rootReducer,
      {},
      [sagaMiddleware
        // createLogger()
      ],
      devTools.isEnabled() ? [devTools.enhancer()] : []);

    // Enable syncing of Angular router state with our Redux store.
    if (ngReduxRouter) {
      // ngReduxRouter.initialize();
    }
    sagaMiddleware.run(rootSaga);
  }
}


