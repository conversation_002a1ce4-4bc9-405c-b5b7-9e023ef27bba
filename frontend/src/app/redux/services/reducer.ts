import {Type} from './types';
import ServiceStateModel from '../model/ServiceStateModel';

const initialState = new ServiceStateModel();

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.SERVICES_LOADING:
      return {...state, servicesLoading: true};
    case Type.SERVICES_LOADED:
      return {
        ...state,
        services: action.services,
        activeServices: action.activeServices,
        inactiveServices: action.inactiveServices,
        hasActiveServices: action.hasActiveServices,
        hasInactiveServices: action.hasInactiveServices,
        normalizedServices: action.normalizedServices,
        servicesLoaded: true,
        servicesLoading: false
      };
    case  Type.ENERGIA_POD_DETAILS_LOADING:
      return {...state, lucePodDetailsLoading: true};
    case  Type.ENERGIA_POD_DETAILS_LOADED:
      return {...state, lucePodDetails: action.podDetails, lucePodDetailsLoaded: true, lucePodDetailsLoading: false};
    case  Type.GAS_POD_DETAILS_LOADING:
      return {...state, gasPodDetailsLoading: true};
    case  Type.GAS_POD_DETAILS_LOADED:
      return {...state, gasPodDetails: action.podDetails, gasPodDetailsLoaded: true, gasPodDetailsLoading: false};
    case  Type.INTERNET_POD_DETAILS_LOADING:
      return {...state, internetPodDetailsLoading: true};
    case  Type.INTERNET_POD_DETAILS_LOADED:
      return {
        ...state,
        adslPodDetails: action.podDetails,
        activeAdslPodDetails: action.activePodDetails,
        adslPodDetailsLoaded: true,
        internetPodDetailsLoading: false
      };
    case  Type.FISSO_POD_DETAILS_LOADING:
      return {...state, fissoPodDetailsLoading: true};
    case  Type.FISSO_POD_DETAILS_LOADED:
      return {...state, fissoPodDetails: action.podDetails, fissoPodDetailsLoaded: true, fissoPodDetailsLoading: false};
    case  Type.AMAZON_PRIME_POD_DETAILS_LOADING:
      return {...state, amazonprimePodDetailsLoading: true};
    case  Type.AMAZON_PRIME_POD_DETAILS_LOADED:
      return {...state, amazonprimePodDetails: action.podDetails, amazonprimePodDetailsLoaded: true, amazonprimePodDetailsLoading: false};
    case  Type.ROUTER_INFO_LOADED:
      return {...state, routerInfo: action.routerInfo, routerInfoLoaded: true, routerInfoLoading: false};
    case  Type.ROUTER_INFO_LOADING:
      return {...state, routerInfoLoading: true};
    default:
      return state;
  }

}
