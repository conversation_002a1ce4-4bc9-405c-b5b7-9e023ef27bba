import { getContext, put, takeEvery } from 'redux-saga/effects';
import { Type } from '../services/types';
import { GasService } from '../../common/services/gas/gas.service';
import { NormalizeUtils } from '../../common/utils/NormalizeUtils';


function* loadAdditionalPodDetails(action) {
  const context = yield getContext('context');
  const service = context.get(GasService);
  const podDetails = yield (<GasService>service).loadAdditionalPodDetailsForService(action.service).toPromise();
  yield put({type: Type.GAS_POD_DETAILS_LOADED, podDetails: NormalizeUtils.normalizeList(podDetails, 'pdrValore')});
}


export function* gasRootSaga() {
  yield takeEvery(Type.GAS_POD_DETAILS_LOADING, loadAdditionalPodDetails);
}
