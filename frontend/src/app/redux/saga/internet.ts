import {getContext, put, takeEvery} from 'redux-saga/effects';
import {Type} from '../services/types';
import {NormalizeUtils} from '../../common/utils/NormalizeUtils';
import {FibraService} from '../../common/services/fibra/fibra.service';
import {ActiveServiceStatus} from '../../common/enum/ServiceStatus';


function* loadInternetPodDetails(action) {
  const context = yield getContext('context');
  const service = context.get(FibraService);
  const podDetails = yield (<FibraService>service).getPodDetails(action.clientId).toPromise();
  yield put({
    type: Type.INTERNET_POD_DETAILS_LOADED,
    podDetails: NormalizeUtils.normalizeListWithExprssion(podDetails,
      item => item.numeroRisorsa ? item.numeroRisorsa : item.numeroContatto),
    activePodDetails: NormalizeUtils.normalizeListWithExprssion(podDetails.filter(item =>
        !!ActiveServiceStatus[item.stato.toUpperCase()]),
      item => item.numeroRisorsa ? item.numeroRisorsa : item.numeroContatto)
  });
}


export function* internetRootSaga() {
  yield takeEvery(Type.INTERNET_POD_DETAILS_LOADING, loadInternetPodDetails);
}
