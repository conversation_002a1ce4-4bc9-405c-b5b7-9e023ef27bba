import { all } from 'redux-saga/effects';
import { gasRootSaga } from './gas';
import { energyRootSaga } from './luce';
import { internetRootSaga } from './internet';
import { fissoRootSaga } from './fisso';
import { routerRootSaga } from './router';
import { mobileRootSaga } from './mobile';
import { communicationRootSaga } from './communication';

export function* rootSaga() {
  yield all([
    gasRootSaga(), energyRootSaga(), internetRootSaga(), fissoRootSaga(), routerRootSaga(),
    mobileRootSaga(), communicationRootSaga()
  ]);
}
