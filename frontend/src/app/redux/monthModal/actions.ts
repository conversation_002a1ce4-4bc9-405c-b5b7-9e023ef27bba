import {dispatch} from '@angular-redux/store';
import {DialogModalEntity} from '../../common/model/dialogModal/DialogModalEntity';
import {Type} from './types';

export class MonthModalActions {

  @dispatch()
  showMonthModal = (dialogModalEntity: DialogModalEntity) => ({
    type: Type.SHOW_MONTH_MODAL,
    dialogModalEntity: dialogModalEntity
  })

  @dispatch()
  sendUnsavedStatusToSubscribers = (dialogModalEntity: DialogModalEntity) => ({
    type: Type.SEND_UNSAVED_STATUS,
    dialogModalEntity: dialogModalEntity
  })

  @dispatch()
  hideMonthModal = () => ({
    type: Type.HIDE_MONTH_MODAL
  })

}
