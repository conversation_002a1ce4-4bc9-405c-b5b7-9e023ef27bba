import {Type} from './types';
import DialogModalStateModel from '../model/DialogModalStateModel';

const initialState = new DialogModalStateModel();

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.SHOW_MONTH_MODAL:
      return Object.assign({}, {show: true, dialogModalEntity: action.dialogModalEntity});
    case Type.HIDE_MONTH_MODAL:
      return Object.assign({}, {show: false});
      case Type.SEND_UNSAVED_STATUS:
      return Object.assign({}, {isUnsaved: action.dialogModalEntity.isUnsaved});
    default:
      return state;
  }
}
