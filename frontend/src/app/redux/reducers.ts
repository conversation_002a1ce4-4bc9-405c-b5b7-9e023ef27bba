import {combineReducers, ReducersMapObject} from 'redux';
import {routerReducer} from '@angular-redux/router';
import notification from './notification/reducer';
import spinner from './spinner/reducer';
import chat from './chat/reducer';
import user from './user/reducer';
import {sessionReducer} from './session/session.reducer';
import services from './services/reducer';
import mobile from './mobile/reducer';
import modal from './modal/reducer';
import dialogModal from './dialogModal/reducer';
import monthModal from './monthModal/reducer';
import emitInput from './dialogModalInputEmit/reducer';
import contabile from './contabile/reducer';
import notificaMdp from './notifica-mdp/reducer';
import dilazione from './dilazione/reducer';
import communication from './communication/reducer';
import saldo from './saldo/reducer';
import richiedi from './richiedi/reducer';

// Define the global store shape by combining our application's
// reducers together into a given structure.
export const rootReducer = combineReducers({
  session: sessionReducer,
  spinner,
  modal,
  dialogModal,
  monthModal,
  emitInput,
  notification,
  notificaMdp,
  user,
  services,
  mobile,
  router: routerReducer,
  chat,
  contabile,
  dilazione,
  communication,
  saldo,
  richiedi
} as ReducersMapObject);
