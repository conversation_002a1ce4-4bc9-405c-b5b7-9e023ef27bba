import { IPayloadAction } from '../util';
import { SessionActions } from './session.actions';
import { ISession, UserDetails } from './session.types';
// import { INITIAL_STATE } from './session.initial-state';


const INITIAL_USER_STATE: UserDetails = {
    id: null,
    consumer: null,
    crmGuiId: '',
    indirizzoEmail: null,
    referenceNumber: null,
    name: '',
    sName: '',
    partitaIva: '',
    codiceFiscale: '',
    sedeLegale: null,
    billingAddress: null,
    billingType: '',
    tipologiaContratto: '',
    tipoCliente: '',
};

const INITIAL_STATE: ISession = {
    access_token: null,
    refresh_token: null,
    user: INITIAL_USER_STATE,
    hasError: false,
    isLoading: false,
};



export function sessionReducer(
    state: ISession = INITIAL_STATE,
    action: IPayloadAction<ISession, string>
): ISession {
    switch (action.type) {
        case SessionActions.LOGIN_USER:
            return Object.assign({}, state, {
                access_token: null,
                user: {},
                hasError: false,
                isLoading: true
            });

        case SessionActions.LOGIN_USER_SUCCESS:
            return Object.assign({}, state, {
                access_token: action.payload.access_token,
                refresh_token: action.payload.refresh_token,
                user: action.payload.user,
                hasError: false,
                isLoading: false,
            });


        case SessionActions.LOGIN_USER_ERROR:
            return Object.assign({}, state, {
                access_token: null,
                refresh_token: null,
                user: {},
                hasError: true,
                isLoading: false,
            });

        case SessionActions.LOGOUT_USER:
            return INITIAL_STATE;

        default:
            return state;
    }
}

