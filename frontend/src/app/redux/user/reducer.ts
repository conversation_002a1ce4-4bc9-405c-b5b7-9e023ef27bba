import { Type } from './types';

const initialState = {userDetails: {}, clientOffers: [], activeOffers: []};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.SET_USER_DETAILS:
      return {...state, userDetails: action.userDetails};
    case Type.USER_INFO_LOADED:
      return {...state, userInfo: action.userInfo};
    case Type.USER_OFFERS_LOADED:
      return {
        ...state,
        clientOffers: action.offers,
        activeOffers: action.activeOffers,
        hasActiveOffers: action.hasActiveOffers
      };
    default:
      return state;
  }

}
