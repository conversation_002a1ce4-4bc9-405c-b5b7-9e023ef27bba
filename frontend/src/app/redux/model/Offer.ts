export interface Offer {
  offerType?: number;
  adsl?: (AdslEntity)[] | null;
  canoneMensile?: number;
  canoneStabilizzato?: number;
  ee?: (EeEntity)[] | null;
  gas?: (GasEntity)[] | null;
  saldoContoRelax?: number;
  tipoCommercialeDescrizione?: string;
  scadenzaAnnoContrattuale: number;
  voce?: (VoceEntity)[] | null;
  mobile?: (MobileEntity)[] | null;
  assicurazione?: (AssicurazioneEntity)[] | null;
  coverCare?: (CoverCareEntity)[] | null;
  billingId?: number;
  idInvoiceSaldoPrevisionale?: number;
  valoreSaldoPrevisionale?: number;
  valueLastSaldoCr?: any;
}

export interface AdslEntity {
  idLinea: number;
  stato: Stato;
  costoMese?: number;
}

export interface Stato {
  codice: string;
  descrizione: string;
}

export interface EeEntity {
  kwh: number;
  stato: Stato;
  utenza: string;
}

export interface GasEntity {
  mc: number;
  stato: Stato;
  utenza: string;
}

export interface VoceEntity {
  descrizione: string;
  minuti: number;
  ramo: string;
  stato: Stato;
  costoMese?: number;
}

export interface MobileEntity {
  mobileIdData: number;
  mobileId: number;
  subscriptionId: number;
  codiceOfferta: string;
  statoSim: string;
  nomeOfferta: string;
  note: string;
}

export interface AssicurazioneEntity {
  idPolizza: number;
  tipoPolizza: string;
  compagnia: string;
  statoCRM: string;
  note: string;
}

export interface CoverCareEntity {

}
