import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {SelectModule} from 'ng2-select';

import {SharedModule} from '../../shared/shared.module';


import {HomeService} from '../home/<USER>/home/<USER>';
import {HttpService} from '../../services/http.service';

import {CdkTableModule} from '@angular/cdk/table';
import {MatTableModule} from '@angular/material';
import {CommonModule} from '../../common/common.module';
import {RouteService} from '../../layout/submenu/route.service';
import {LayoutModule} from '../../layout/layout.module';
import {ChartsModule} from 'ng2-charts';
import {ShortUserDataComponent} from './container/shortUserData/shortUserData.component';
import {UserServicesService} from '../profilePage/userServices/userServices.service';

const routes: Routes = [
  {
    path: 'short/data', component: ShortUserDataComponent
  }

];

@NgModule({
  imports: [
    LayoutModule,
    SharedModule,
    RouterModule.forChild(routes),
    SelectModule,
    MatTableModule,
    CdkTableModule,
    CommonModule,
    ChartsModule
  ],
  providers: [
    HttpService,
    UserServicesService,
    RouteService,
    HomeService
  ],
  declarations: [
    ShortUserDataComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    RouterModule
  ]
})
export class AdditionalUserDataPageModule {
}
