import {Compo<PERSON>, OnD<PERSON>roy} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import 'rxjs/add/observable/empty';
import * as R from 'ramda';
import {BillingType} from '../../../../common/enum/BillingType';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {UserData} from '../../../../common/model/userData.model';
import {IncidentEventCategory} from '../../../../common/enum/IncidentEventCategory';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {UserActions} from '../../../../redux/user/actions';
import {NotificationService} from '../../../../common/services/notification/notification.service';

import {ChangePersonalDataRequest} from '../../../../common/model/ChangePersonalDataRequest';
import {ChangePersonalDataResponse} from '../../../../common/model/ChangePersonalDataResponse';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import {Address} from '../../../profilePage/model/Address';
import {IncidentEventResponse} from '../../../autolettura/model/IncidentEventResponse';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {Cluster} from '../../../../common/enum/Cluster';
import {AddressType} from '../../../../common/enum/AdderssType';
import Validator from '../../../../common/utils/Validator';


const rewriteUserData = R.compose(
  R.ifElse(
    R.propEq('fiscalCode', ''),
    R.omit(['fiscalCode']),
    R.omit(['vatNumber'])
  ));

const modalitaFatturaErrorMessage = `Non è possibile attivare la spedizione elettronica della fattura Optima per
 mancanza della mail tra i tuoi dati. Per procedere registra la mail cliccando su “Modifica E-mail"`;

const modalitaFatturaValidator = (input: FormControl, parameters: UserData) => {
  return input.value === BillingType.ELECTRONICA && !parameters.email;
};
const errorMessage = 'Errore nel salvataggio della variazione.';

const successMessage = 'La tua richiesta di variazione è stata registrata. Visualizzerai il nuovo dato entro qualche minuto. Grazie.';

@Component({
  selector: 'app-short-user-data',
  templateUrl: './shortUserData.html',
  styleUrls: ['shortUserData.scss']
})
export class ShortUserDataComponent implements OnDestroy {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  @select(['user', 'clientOffers'])
  private clientOffers;

  userData: UserData;

  showAddress: boolean;

  incidentEventCategory = IncidentEventCategory;

  modalitaFatturaOptions = [BillingType.PRINT_LIST, BillingType.ELECTRONICA];

  officeAddress: string;

  indirizzoFatturazioneOptions;

  formGroup: FormGroup;

  isBusiness: boolean;

  private active = [];

  userInfoSubscription: Subscription;

  clientOffersSubscription: Subscription;

  constructor(private service: UserDataService,
              private fb: FormBuilder, private userActions: UserActions,
              private notificationService: NotificationService) {

    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      if (userData) {
        this.formGroup = this.fb.group({
          cliente: [userData.nameInInvoice, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(3), Validator.onlyTextNumberAllowed()]
          }],
          modalitaFattura: [userData.billingType, {validators: [Validator.invalidIf(modalitaFatturaValidator, userData, modalitaFatturaErrorMessage)]}],
          indirizzoFatturazione: [null, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(10)]
          }],
          indirizzoFatturazioneNew: [],
          officeAddress: [null, {
            validators: [Validators.required, Validator.notEmpty(), Validators.maxLength(100),
              Validators.minLength(10)]
          }],
          officeAddressNew: [],
          numeriRiferimento: [userData.phoneNumber, {
            validators: [Validators.required, Validator.notEmpty(),
              Validator.digits(), Validators.minLength(8), Validators.maxLength(15)]
          }],
          email: [userData.email, {
            validators: [Validators.required, Validator.notEmpty(), Validators.minLength(10), Validators.maxLength(100),
              Validators.pattern('^\\w+[\\w-\\.]*\\@\\w+((-\\w+)|(\\w*))\\.[a-z]{2,3}$')]
          }]
        });
        this.userData = rewriteUserData(userData);
        this.isBusiness = userData.cluster.value === Cluster.BUSINESS || userData.cluster.value === Cluster.PUBLIC_ADMINISTRATION;
        this.showAddress = userData.billingType === BillingType.PRINT_LIST && userData.addresses && userData.addresses.length > 0;
        if (this.showAddress) {
          const addresses = userData.addresses.filter(address => address.tipo === AddressType.BILING);
          if (addresses.length > 0) {
            this.formGroup.controls.indirizzoFatturazione.setValue(addresses[0].indirizzo);
            this.indirizzoFatturazioneOptions = addresses.map(i => i.indirizzo);
          }
        }
        const officeAddressesOptions = userData.addresses.filter(address => address.tipo === AddressType.REGITERED_OFFICE)
          .map(i => i.indirizzo);
        if (officeAddressesOptions) {
          this.formGroup.controls.officeAddress.setValue(officeAddressesOptions[0]);
          this.officeAddress = officeAddressesOptions[0];
        }
      }
    });

    this.clientOffersSubscription = this.clientOffers.subscribe((response: any) => {
      if (response.length > 0) {
        const mills = new Date().getTime();
        this.active = response.filter(value => value.scadenzaAnnoContrattuale > mills || value.scadenzaAnnoContrattuale == null);
      }
    });

  }

  getModalitaPagamentoPdf() {
    if (this.userData.cluster.value === Cluster.BUSINESS) {
      return 'http://www.optimaitalia.com/downloadFile.php?relUri=Modifica%20modalit%C3%A0%20di%20pagamento%20BU.pdf';
    }
    if (this.active) {
      return 'http://www.optimaitalia.com/downloadFile.php?relUri=Modifica%20modalit%C3%A0%20di%20pagamento%20PIC%20(2).pdf';
    }
    return 'http://www.optimaitalia.com/downloadFile.php?relUri=Modifica%20modalit%C3%A0%20di%20pagamento%20CO.pdf';
  }

  getDocument() {
    window.open(this.getModalitaPagamentoPdf());
  }

  changePersonalData(incidentEventCategory: IncidentEventCategory) {
    return (value, oldValue) => {
      const request = {
        clientId: localStorage.getItem('clientId'), incidentEventCategory, value, oldValue
      } as ChangePersonalDataRequest;
      return this.buildOnAccept(request);
    };
  }

  changeShipmentMethodAddition(incidentEventCategory: IncidentEventCategory) {
    return (value, oldValue) => {
      const request = {
        clientId: localStorage.getItem('clientId'),
        incidentEventCategory,
        value: value.toUpperCase(),
        oldValue
      } as ChangePersonalDataRequest;
      return this.buildOnAccept(request);
    };
  }

  buildOnAccept(request) {
    return Observable.of({}).flatMap(() => {
      return this.service.changeUserPersonalData(request).catch(() => {
        this.notificationService.showInfoMessage(errorMessage);
        return Observable.of({} as ChangePersonalDataResponse);
      });
    }).flatMap(response => {
      if (response.errorStatus && response.errorStatus.code === '200') {
        this.notificationService.showSuccessMessage(successMessage);
        return this.service.getUserData();
      }
      this.notificationService.showInfoMessage(errorMessage);
      return Observable.empty();
    }).flatMap(response => {
      this.userActions.userInfoLoaded(response as UserData);
      return Observable.empty();
    });
  }

  changeAddress(incidentEventCategory: IncidentEventCategory) {
    return (value: Address, oldValue?: string) => {
      const request = new ChangePersonalDataRequest();
      request.clientId = localStorage.getItem('clientId');
      request.addressToChange = incidentEventCategory === IncidentEventCategory.CHANGE_OFFICE_ADDRESS ? this.officeAddress : oldValue;
      request.newAddress = `${value.toponimo},${value.via},${value.civico},${value.cap},${value.comune}`;
      request.incidentEventCategory = incidentEventCategory;
      return Observable.of({}).flatMap(() => {
        return this.service.changeAddress(request).catch(() => {
          this.notificationService.showInfoMessage(errorMessage);
          return Observable.of({status: ServiceResponseStatus.KO} as IncidentEventResponse);
        });
      }).flatMap(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.notificationService.showSuccessMessage(successMessage);
          return this.service.getUserData();
        }
        return Observable.empty();
      }).flatMap(response => {
        this.userActions.userInfoLoaded(response as UserData);
        return Observable.empty();
      });
    };
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription, this.clientOffersSubscription]);
  }

}
