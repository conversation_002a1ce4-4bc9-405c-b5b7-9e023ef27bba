<div class="container-fluid">
  <div class="row">
    <div class="col-lg-12">
      <div class="row">
        <div class="panel panel-default shadow-z2">
          <div class="panel-body">
            <div class="col-xl-12 col-lg-12 col-md-12 right">
              <div class="desktop">
                <div class="col-md-12 app--user-data view  ">
                  <img class="d-block w-100" src="../../../../../assets/img/Banner_Fattura_Elettronica.jpg"
                       alt="First slide">
                </div>
              </div>

              <div class="col-md-12 app--user-data" *ngIf="!userData">
                <div class="user-data-table-block" style="padding: 15px 10px">
                  <span>Non risulta alcuna utenza attiva</span>
                </div>
              </div>
              <div class="col-md-12 app--user-data" *ngIf="userData" [formGroup]="formGroup">
                <div class="row">
                  <div class="col-md-12 user-data-block">
                    <div class="row user-data-table-block">

                      <div class="col-lg-12 col-md-12 col-sm-12 detail-row">
                        <div class="col-lg-5 col-md-5 col-sm-12 subject">Modalità di ricezione fattura:</div>
                        <div class="col-lg-7 col-md-7 col-sm-12 data">
                          <app-user-data-editor [formGroup]="formGroup" name="modalitaFattura"
                                                [modifiable]="true"
                                                [onAccept]="changeShipmentMethodAddition(incidentEventCategory.CHANGE_INVOICE_SHIPPING_METHOD)"
                                                type="select"
                                                [options]="modalitaFatturaOptions">
                            <confirm-message *ngIf="formGroup.controls['modalitaFattura'].value==='Elettronica'">
                          <span class="confirm-message">Stai richiedendo la spedizione elettronica della fattura Optima. Riceverai la fattura al tuo
                          indirizzo mail <b>{{formGroup.controls['email'].value}}</b>. Per modificare la mail una volta completata questa
                          operazione clicca su “Variazione E-mail".</span>
                            </confirm-message>
                            <confirm-message *ngIf="formGroup.controls['modalitaFattura'].value==='Cartacea'">
                          <span class="confirm-message">Stai richiedendo la spedizione cartacea della fattura Optima. Riceverai la fattura al tuo
                          indirizzo <b>{{formGroup.controls['officeAddress'].value}}</b>. Per modificare l’indirizzo di spedizione
                          della fattura una volta completata questa operazione clicca su “Variazione Indirizzo di fatturazione".
                          </span>
                            </confirm-message>
                          </app-user-data-editor>
                        </div>
                      </div>
                      <div class="col-lg-12 col-md-12 col-sm-12 detail-row">
                        <div class="col-lg-5 col-md-5 col-sm-12 subject">E-mail:</div>
                        <div class="col-lg-7 col-md-7 col-sm-12 data">
                          <app-user-data-editor [formGroup]="formGroup" name="email"
                                                [onAccept]="changePersonalData(incidentEventCategory.CHANGE_EMAIL)"
                                                [modifiable]="true" [emptyOnEdit]="true"
                                                placeholder="Inserisci la nuova email">
                            <confirm-message>
                          <span class="confirm-message">La nuova email inserita è <b>“{{formGroup.controls['email'].value}}”</b>,
                          per completare l’operazione clicca su "Conferma"
                          </span>
                            </confirm-message>
                          </app-user-data-editor>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>



