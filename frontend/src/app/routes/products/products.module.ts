import { NgModule } from '@angular/core';
import { CommonModule as AngularCommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { CommonModule } from '../../common/common.module';
import { ProductsLayoutComponent } from './containers/products-layout/products-layout.component';
import { IntegratedProductComponent } from './containers/integrated-product/integrated-product.component';
import { ScioltoProductComponent } from './containers/sciolto-product/sciolto-product.component';


const routes: Routes = [{
  path: '', component: ProductsLayoutComponent, children: [
    {path: 'integrated/:service', component: IntegratedProductComponent},
    {path: 'sciolto/:service', component: ScioltoProductComponent}]
}];

@NgModule({
  imports: [
    AngularCommonModule,
    RouterModule.forChild(routes),
    CommonModule
  ],
  declarations: [ProductsLayoutComponent, IntegratedProductComponent, ScioltoProductComponent]
})

export class ProductsModule {
}
