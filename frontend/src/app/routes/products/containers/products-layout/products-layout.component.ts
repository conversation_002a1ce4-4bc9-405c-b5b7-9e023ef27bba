import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { select } from '@angular-redux/store';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { ObservableUtils } from '../../../../common/utils/ObservableUtils';
import { MenuService } from '../../../../core/menu/menu.service';

@Component({
  selector: 'app-products-layout',
  templateUrl: './products-layout.component.html',
  styleUrls: ['./products-layout.component.scss']
})
export class ProductsLayoutComponent implements OnDestroy {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  @select(['services', 'activeServices'])
  activeServices: Observable<object>;

  private readonly activeServicesSubscription: Subscription;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private menu: MenuService) {
    const servicesList = menu.getServiceMenu().map(item => item.title);
    const service = (<BehaviorSubject<object>>activatedRoute.children[0].params).getValue()['service'];
    this.activeServicesSubscription = this.activeServices.subscribe(services => {
      if (services && service && servicesList.indexOf(service.toUpperCase()) < 0) {
        router.navigate(['/home/<USER>']);
      }
    });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.activeServicesSubscription]);
  }

}
