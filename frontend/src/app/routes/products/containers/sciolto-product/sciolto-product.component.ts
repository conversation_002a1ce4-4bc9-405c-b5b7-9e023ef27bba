import {Compo<PERSON>, OnD<PERSON>roy} from '@angular/core';
import {ActivatedRoute, Params} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import EmailMessage from '../../../../common/model/EmailMessage';
import {select} from '@angular-redux/store';
import {UserData} from '../../../../common/model/userData.model';
import {EmailService} from '../../../../common/services/email/email.service';
import {NotificationService} from '../../../../common/services/notification/notification.service';
import {messages} from '../config/config';
import {IncidentEventResponse} from '../../../autolettura/model/IncidentEventResponse';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';
import {serviceNameIdMap} from '../../../../common/enum/Service';

@Component({
  selector: 'app-sciolto-product',
  templateUrl: './sciolto-product.component.html',
  styleUrls: ['./sciolto-product.component.scss']
})
export class ScioltoProductComponent implements OnDestroy {

  @select(['user'])
  userInfo: Observable<any>;

  @select(['services', 'services'])
  services: Observable<any>;

  userData: UserData;

  usersServices: Array<any>;

  hasActiveOffers: boolean;

  routerParams: BehaviorSubject<Params>;


  userInfoSubscription: Subscription;

  servicesSubscription: Subscription;

  constructor(private activatedRoute: ActivatedRoute, private mailService: EmailService,
              private notificationService: NotificationService, private incidentEventService: IncidentEventService) {
    this.routerParams = <BehaviorSubject<Params>>activatedRoute.params;
    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      this.userData = userData.userInfo;
      this.hasActiveOffers = userData.hasActiveOffers;
    });
    this.servicesSubscription = this.services.subscribe(services => {
      this.usersServices = services;
    });
  }

  createIncidentEvent(serviceType): Observable<IncidentEventResponse> {
    return this.hasActiveOffers ? this.incidentEventService.remodulationProductRequest()
      : this.incidentEventService.activateNewProductRequest(serviceType);
  }

  sendIntegratedSolutionRequest() {
    const {userData, routerParams, notificationService, incidentEventService} = this;
    const serviceId = serviceNameIdMap[routerParams.getValue().service.toUpperCase()];
    if (serviceId) {
      this.mailService.sendIntegratedSolutionRequest({
        message: `Ciao, il cliente ${localStorage.getItem('clientId')} richiede da selfcare ricontatto per attivazione
       nuovi servizi al ${userData.phoneNumber}. Grazie e buon lavoro!!`,
        subject: 'Richiesta attivazione nuovi servizi'
      } as EmailMessage)
        .flatMap(() => this.createIncidentEvent(serviceId))
        .subscribe((response: IncidentEventResponse) =>
            incidentEventService.createIncidentEventResponseNotification(response, messages.successMessage),
          () => notificationService.errorMessage(messages.failedMessage)
        );
      return;

    }
    notificationService.errorMessage(messages.failedMessage);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription, this.servicesSubscription]);
  }
}
