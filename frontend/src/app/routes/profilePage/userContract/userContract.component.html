<div class="col-lg-9 col-md-9 contracts-layout">

  <div class="col-lg-12 col-md-12 page-title">
    <div class="title-image"></div>
    <div class="text">I TUOI CONTRATTI</div>
  </div>

  <div *ngIf="contracts?.length" class="col-lg-12 col-md-12 col-sm-12 contracts-result-table clearfix">
    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-3 table-header">Contratto №</div>
    <div class="col-lg-3 col-md-2 col-sm-2 col-xs-3 table-header">Data stipula:</div>
    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-3 table-header">Servizi inclusi:</div>
    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-3 table-header">Scarica</div>

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 contract-row no-padding" *ngFor="let row of contracts">
      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-3 contract-id">{{row.id}}</div>
      <div class="col-lg-3 col-md-2 col-sm-2 col-xs-3 contract-date">{{row.dataStipula| date : "dd/MM/y"}}</div>
      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-3 contract-info">
        <div *ngIf="!row.idContrattoPI&&row.descTipoContratto!=='Pib' &&
         row.descTipoContratto!=='Pic' && row.descTipoContratto!=='Vita mia';else children">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 contract-type-description no-padding">
            <div class="col-lg-4 col-md-3 col-sm-4 col-xs-12 no-padding">{{row.descTipoContratto}}</div>
            <div class="col-lg-8 col-md-9 col-sm-8 col-xs-12 no-padding" *ngIf="showContractButtons(row)">
              <!--<div class="app-button">-->
                <!--<a class="link-button" href="javascript: void(0)"-->
                   <!--(click)="downloadContractPdf(row.id)">Trasparenza tariffaria </a>-->
              <!--</div>-->
              <div class="app-button">
                <a target="_blank" class="link-button"
                   href="http://www.optimaitalia.com/condizioni-generali-di-contratto.html">
                  Condizioni generali</a>
              </div>
            </div>
          </div>
        </div>

        <ng-template #children>
          <div *ngIf="row.descTipoContratto==='Pib'|| row.descTipoContratto==='Pic' ||
           row.descTipoContratto==='Vita mia'">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 contract-type-description no-padding"
                 *ngFor="let item of row.items">
              <div class="col-lg-4 col-md-3 col-sm-4 col-xs-12 type-description no-padding">
                {{item.descTipoContratto}}</div>
              <div class="col-lg-8 col-md-9 col-sm-8 col-xs-12 no-padding" *ngIf="showContractButtons(item)">
                <!--<div class="app-button">-->
                  <!--<a href="javascript: void(0)" class="link-button"-->
                     <!--(click)="downloadContractPdf(item.id)">Trasparenza-->
                    <!--tariffaria </a>-->
                <!--</div>-->
                <div class="app-button">
                  <a target="_blank" class="link-button"
                     href="http://www.optimaitalia.com/condizioni-generali-di-contratto.html">Condizioni
                    generali</a>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </div>
      <div *ngIf="row.spRelativeUri " class="col-lg-3 col-md-4 col-sm-4 col-xs-3" (click)="downloadContractsPdf(row.spRelativeUri)">
        <img src="/assets/img/optimaIcons/pdf_icon.png" class="pdf-icon" alt="Scarica contratto">
      </div>
      <div *ngIf="!row.spRelativeUri" class="col-lg-3 col-md-4 col-sm-4 col-xs-3">
        <!--<button class="button-download-pdf" (click)="showModalWindowForUser(row.id)">RICHIEDI COPIA CONTRATTO</button>-->
      </div>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showModalWindowForUserWithoutEmail">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Per procedere è necessario registrare un indirizzo mail nella sezione “Profilo”/”I tuoi
      dati” cliccando su “Modifica E-mail”
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showModalWindowForUserWithEmail">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">La tua richiesta di copia del contratto Optima è stata presa in carico. Riceverai riscontro
      entro le prossime 48 ore lavorative nella sezione “Fai da te”/“Le tue segnalazioni”.
    </div>
    <div class="modal-text">Grazie.</div>
  </div>
</div>
