@import "~app/shared/styles/colors";

.contracts-layout {
  color: $dark-blue;

  .page-title {
    background: #ffffff;
    border: 2px solid $menu-border;
    border-radius: 5px;
    height: 45px;

    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_ItuoiContratti.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 50px;
      float: left;
    }

    .text {
      color: $dark-blue;
      margin-top: 11px;
      float: left;
    }
  }

  .pdf-icon {
    height: 40px;
    cursor: pointer;
  }

  .button-download-pdf {
    border: 1px solid #9BC641;
    color: white;
    background-color: #9BC641;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
  }

  .contracts-result-table {
    border: 1px solid $menu-border;
    border-radius: 5px;
    margin-top: 2%;
    padding-bottom: 3%;

    .table-header {
      font-weight: bold;
      font-size: 16px;
      border-bottom: 1px solid $menu-border;
      padding-top: 20px;
    }

    .contract-row {
      border-bottom: 1px solid $menu-border;
      padding-top: 5px;
      padding-bottom: 5px;

      .contract-info {
        padding-right: 0;
      }

      .contract-type-description {
        margin-bottom: 5px;

        &:last-child {
          margin-bottom: 0;
        }

        div {
          display: inline-block;
        }

        .app-button {
          border: 1px solid $dark-blue;
          border-radius: 5px;
          padding: 0 5px;
          margin-right: 10px;

          a {
            color: $dark-blue;
            text-decoration: none;
          }
        }
      }

    }
  }

}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 680px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 30px;
  font-size: 25px;
}

@media screen and (max-width: 1120px) {
  .contracts-layout {
    .contracts-result-table {
      .contract-type-description {
        .app-button {
          margin-right: 5px;
        }
      }

      .contract-id, .contract-date, .contract-info, .table-header {
        padding-left: 10px;
        padding-right: 0;
      }

      /*.table-header {
        height: 69px;
      }*/
    }

  }
}

@media screen and (max-width: 991px) {
  .contracts-layout {
    padding: 0;

    .contract-row {
      .app-button {
        margin-top: 5px;
        margin-right: 5px;
      }
    }

    .page-title {
      display: none;
    }
  }
}

@media screen and (max-width: 720px) {
  .contracts-layout {
    .contracts-result-table {
      .table-header {
        font-size: 14px;
      }

      .contract-row {
        font-size: 12px;

        .contract-type-description {
          .app-button {
            font-size: 10px;
            margin-bottom: 5px;
            margin-right: 0;
          }
        }
      }


    }


  }
}

@media screen and (max-width: 485px) {
  .contracts-layout {
    .contracts-result-table {
      .contract-row {
        .app-button {
          font-size: 10px;
          margin-bottom: 5px;
          margin-right: 0;
        }
      }

      .contract-date, .table-header, .contract-info {
        padding-left: 0;
      }

      .contract-info {
        padding-right: 0;
      }

      .contract-id {
        padding-left: 5px;
      }

      .table-header {
        &:first-child {
          padding-left: 5px;
        }
      }
    }
  }
}


@media screen and (max-width: 400px) {
  .contracts-layout {
    .contracts-result-table {
      padding-left: 3px;
      padding-bottom: 3px;

      .contract-type-description {
        .app-button {
          padding: 0 3px !important;
          margin-bottom: 5px;
          margin-right: 0;
        }
      }
    }
  }
}
