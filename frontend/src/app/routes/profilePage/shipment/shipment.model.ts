export interface Shipment {
  id: Number;
  shipmentHeader: ShipmentHeader;
  shipmentAddress: ShipmentAddress;
  piva: String;
  cf: String;
  startDate: Date;
  listArticoli: [ListArticoli];
}


export interface ShipmentAddress {
  common: String;
  postalCode: Number;
  address: String;
}

export interface ShipmentHeader {
  articleType: String;
  trackingNr: Number;
  causal: String;
  modificationDate: Date;
  status: String;
  number: String;
}
export interface ListArticoli {
  qta: String;
}
