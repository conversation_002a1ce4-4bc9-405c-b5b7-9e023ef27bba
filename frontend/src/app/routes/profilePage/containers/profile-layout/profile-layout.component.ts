import {AfterViewChecked, ChangeDetector<PERSON><PERSON>, Component, OnDestroy, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {NavigationEnd, NavigationStart, Router} from '@angular/router';
import {profileMobileLayoutOrdersMap} from '../../config/config';
import {ConstantUtil} from '../../../../utils/ConstantUtil';
import {UserServicesService} from '../../userServices/userServices.service';

@Component({
  selector: 'app-profile-layout',
  templateUrl: './profile-layout.component.html',
  styleUrls: ['./profile-layout.component.scss']
})
export class ProfileLayoutComponent implements On<PERSON><PERSON>roy, OnInit, AfterViewChecked {

  isMobile = ConstantUtil.isMobile;

  @select(['services', 'hasActiveServices'])
  hasActiveServices: Observable<boolean>;

  @select(['services', 'hasInactiveServices'])
  hasInactiveServices: Observable<boolean>;

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  @select(['user', 'hasActiveOffers'])
  hasActiveOffers: Observable<boolean>;

  @select(['user', 'userInfo'])
  userInfo: Observable<any>;

  @select(['user', 'clientOffers'])
  clientOffers: Observable<any>;
  @select(['services', 'activeServices'])
  services: Observable<object>;
  userName: string;
  userSurname: string;

  hasTutoInUnoActive: Observable<boolean>;

  userInfoSubscription: Subscription;
  servicesSubscription: Subscription;
  mobileLayoutOrder = 11;

  lastRouteUrl: string;
  activeServices: Object;

  constructor(private router: Router, private cdRef: ChangeDetectorRef, private _userService: UserServicesService) {
    router.events.subscribe((value) => {
      if (value instanceof NavigationEnd) {
        this.lastRouteUrl = value.url;
      }
    });
    this.servicesSubscription = this.services.subscribe(services => {
     this.activeServices = services;
    });
  }

  switchOpenClose(routeUrl: string) {
    if (this.isMobile) {
      if (this.lastRouteUrl === routeUrl) {
        this.router.navigateByUrl('/profile');
      }
    }
  }

  ngOnInit(): void {
    this.hasTutoInUnoActive = this.clientOffers.map(items => items.filter(value => value.adsl || value.ee || value.gas
      || value.voce)).map(offers => offers.length > 0);
    this.userInfoSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userName = userInfo.firstName ? userInfo.firstName : '';
        this.userSurname = userInfo.lastName ? userInfo.lastName : '';
      }
    });
    this.mobileLayoutOrder = profileMobileLayoutOrdersMap[this.router.url];
    this.router.events.subscribe((router: NavigationStart) => {
      this.mobileLayoutOrder = profileMobileLayoutOrdersMap[router.url];
    });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription, this.servicesSubscription]);
  }

  ngAfterViewChecked(): void {
    this.cdRef.detectChanges();
  }

}
