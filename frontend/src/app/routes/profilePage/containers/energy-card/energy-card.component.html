<div class="col-md-9">
  <div class="col-lg-12 page-title">
    <div class="title-image"></div>
    <div class="text font-weight-bold">ENERGY CARD</div>
  </div>
  <div class="app--user-energy-card">

    <!--Block for enter code-->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 energy-card-border-style" *ngIf="!showBlockForChooseContract">
      <div class="font-weight-bold label-text text-center-block">Digita il codice della tua Energy Card per accedere al
        voucher Energia
        con cui potrai pagare le tue prossime fatture Luce e/o Gas
      </div>
      <div class="input-container" [formGroup]="formGroup">
        <input type="text" class="text-uppercase" placeholder="CODICE ENERGY CARD" formControlName="voucher">
        <button class="btn-conferma" (click)="checkCode()">CONFERMA</button>
      </div>
      <span class="text-danger" *ngIf="formGroup.controls['voucher'].hasError('required') &&
       (formGroup.controls['voucher'].dirty || formGroup.controls['voucher'].touched)">Campo Obbligatorio</span>

      <!--Block with already used voucher-->
      <div *ngIf="showAlreadyUsedCard">
        <hr>
        <div class="font-weight-bold label-text">LE TUE ENERGY CARD</div>
        <div class="totale-block">
          <div>
            <div class="label-text font-weight-bold">Totale</div>
            <div><i>Credito Energy Card disponibile</i></div>
          </div>
          <div class="font-size-18 font-weight-bold">{{totalSumVouchers ? totalSumVouchers : 0}} €</div>
        </div>
        <div *ngFor="let card of usedCardsInformation">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 short-items-energy-card font-weight-bold"
               (click)="card.showGeneralInformation = !card.showGeneralInformation">
            Energy Card n. {{card.codiceCoupon}} <img *ngIf="!card.showGeneralInformation"
                                                      src="/assets/img/icons/arrow-down_16x16.png"
                                                      alt="Arrow">
            <img *ngIf="card.showGeneralInformation" style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
                 alt="Arrow">
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 details-energy-card" *ngIf="card.showGeneralInformation">
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Importo</div>
            <div class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.importCoupon ? card.importCoupon : '0'}} €</div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Data attivazione</div>
            <div
              class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.dataAttivazione | date:'dd/MM/yyyy'}}</div>
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-7 margin-down">Credito residuo Energy Card</div>
            <div class="col-lg-8 col-md-8 col-sm-8 col-xs-5 font-weight-bold margin-down">{{card.creditoResiduo ? card.creditoResiduo : '0'}} €
            </div>

            <div *ngIf="!card.showContractsInDetails"
                 class="col-lg-12 col-md-12 col-sm-12 col-xs-12 font-weight-bold short-items-table"
                 (click)="card.showContractsInDetails = !card.showContractsInDetails">
              Contratto e
              utenze associate <img src="/assets/img/icons/arrow-down_16x16.png"
                                    alt="Arrow">
            </div>

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 border-table no-padding"
                 *ngIf="card.showContractsInDetails">
              <div style="display: flex; align-items: center; justify-content: space-between; cursor: pointer"
                   (click)="card.showContractsInDetails = !card.showContractsInDetails"
                   class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header font-weight-bold">
                Contratto e utenze associate <img style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
                                                  alt="Arrow">
              </div>
              <div
                class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                Data stipula
              </div>
              <div
                class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                Indirizzo
              </div>
              <div *ngIf="isExtraSmall"
                   class="col-xs-6 cell-style border-bottom-table">{{card.dataStipula | date:'dd/MM/yyyy'}}</div>
              <div *ngIf="isExtraSmall"
                   class="col-xs-6 border-left-table border-right-table cell-style border-bottom-table">{{card.indirizzo}}</div>
              <div
                class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                POD <img class="cell-image" src="assets/img/service-icons/icon_luce_small.png" alt="luce"></div>
              <div
                class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style border-bottom-table background-header font-weight-bold">
                PDR <img class="cell-image" src="assets/img/service-icons/icon_gas_small.png" alt="gas"></div>

              <div *ngIf="!isExtraSmall"
                   class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style">{{card.dataStipula | date:'dd/MM/yyyy'}}</div>
              <div *ngIf="!isExtraSmall"
                   class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-left-table border-right-table cell-style">{{card.indirizzo}}</div>
              <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style">{{card.pod}}</div>
              <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style">{{card.pdr}}</div>
            </div>

            <div *ngIf="!card.showAgreementsInDetails"
                 class="col-lg-12 col-md-12 col-sm-12 col-xs-12 font-weight-bold short-items-table"
                 (click)="card.showAgreementsInDetails = !card.showAgreementsInDetails">
              Storico Movimentazioni <img src="/assets/img/icons/arrow-down_16x16.png"
                                          alt="Arrow">
            </div>

            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 border-table no-padding"
                 *ngIf="card.showAgreementsInDetails">
              <div style="display: flex; align-items: center; justify-content: space-between; cursor: pointer"
                   (click)="card.showAgreementsInDetails = !card.showAgreementsInDetails"
                   class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header font-weight-bold">
                Storico Movimentazioni <img style="rotate: 180deg" src="/assets/img/icons/arrow-down_16x16.png"
                                            alt="Arrow">
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-2 col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                Fattura n.
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-3 col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                Importo Energy Card in fattura
              </div>
              <div *ngIf="isMediumDevices && card.numeroFattura"
                   class="col-md-6 col-sm-6 col-xs-6 border-right-table cell-style border-bottom-table">{{card.numeroFattura}}</div>
              <div *ngIf="isMediumDevices && card.numeroFattura"
                   class="col-md-6 col-sm-6 col-xs-6 cell-style border-bottom-table">{{card.importoTipoCardInFattura ?
                card.importoTipoCardInFattura + ' €' : ''}}
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-3 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style border-bottom-table background-header font-weight-bold">
                Fattura di riferimento
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-3 col-md-4 col-sm-4 col-xs-4 cell-style border-bottom-table background-header font-weight-bold">
                Data emissione Fattura
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-1 col-md-4 col-sm-4 col-xs-4 border-left-table cell-style border-bottom-table background-header font-weight-bold">
                Scarica
              </div>
              <div *ngIf="!isMediumDevices && card.numeroFattura"
                   class="col-lg-2 col-md-2 col-sm-2 col-xs-2 border-right-table cell-style">{{card.numeroFattura}}</div>
              <div *ngIf="!isMediumDevices && card.numeroFattura"
                   class="col-lg-3 col-md-3 col-sm-3 col-xs-3 border-right-table cell-style">{{card.importoTipoCardInFattura ?
                card.importoTipoCardInFattura + ' €' : ''}}
              </div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-3 col-md-4 col-sm-4 col-xs-4 border-right-table cell-style">{{card.fatturaRiferimento}}</div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-3 col-md-4 col-sm-4 col-xs-4 cell-style">{{card.dataEmissione | date:'dd/MM/yyyy'}}</div>
              <div *ngIf="card.numeroFattura"
                   class="col-lg-1 col-md-4 col-sm-4 col-xs-4 border-left-table cell-style font-weight-bold cell-button-download"
                   (click)="card.numeroFattura ? downloadFile(card.downloadUrl) : ''">{{card.numeroFattura ? '↓ PDF' : ''}}</div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!--Block for connect card with contract -->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 energy-card-border-style" *ngIf="showBlockForChooseContract">
      <div class="font-weight-bold header-text">La tua Energy Card è pari a {{cardValue}} €</div>
      <div class="label-text"><i>Potrai usufruire di questo importo per pagare le tue prossime fatture Optima
        relativamente ai servizi Luce e/o Gas fino a svuotamento della carta</i>
      </div>
      <hr>
      <div class="font-weight-bold label-text">Per utilizzare il voucher è necessario associarlo a un contratto Optima
      </div>
      <!--Table-->
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 border-table font-weight-bold no-padding"
           *ngFor="let contract of contracts" [formGroup]="formGroupChooseContract">
        <div
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12 header-table border-bottom-table background-header border-header">
          <input type="radio" formControlName="option" value="{{contract.idContratto}}" name="option">Contratto
          №{{contract.idGruppo}}
        </div>
        <div
          class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
          Data stipula
        </div>
        <div
          class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
          Indirizzo
        </div>
        <div *ngIf="isExtraSmall"
             class="col-xs-6 cell-style border-bottom-table">{{contract.dataStipula | date:'dd/MM/yyyy'}}</div>
        <div *ngIf="isExtraSmall && contract.contractsDetailsAfterAggregation.indirizzoFornitura.length > 1"
             class="col-lg-3 col-md-3 col-sm-3 border-left-table cell-style min-height no-padding"
             style="flex-direction: column;">
          <div *ngFor="let indirizzo of contract.contractsDetailsAfterAggregation.indirizzoFornitura"
               class="col-lg-12 col-md-12 col-sm-12 no-padding">
            <div class="border-bottom-table" style="padding: 5px; min-height: 58px;">{{indirizzo}}</div>
          </div>
        </div>
        <div *ngIf="isExtraSmall && contract.contractsDetailsAfterAggregation.indirizzoFornitura.length <= 1"
             class="col-xs-6 border-left-table cell-style border-bottom-table">{{contract.indirizzoFornitura}}</div>
        <div
          class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style border-bottom-table background-header font-weight-bold">
          POD <img class="cell-image" src="assets/img/service-icons/icon_luce_small.png" alt="luce"></div>
        <div
          class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style border-bottom-table background-header font-weight-bold">
          PDR <img class="cell-image" src="assets/img/service-icons/icon_gas_small.png" alt="gas"></div>
        <div *ngIf="!isExtraSmall"
             class="col-lg-3 col-md-3 col-sm-3 cell-style min-height">{{contract.dataStipula | date:'dd/MM/yyyy'}}</div>

        <div *ngIf="!isExtraSmall && contract.contractsDetailsAfterAggregation.indirizzoFornitura.length > 1"
             class="col-lg-3 col-md-3 col-sm-3 border-left-table border-right-table cell-style min-height no-padding"
             style="flex-direction: column;">
          <div *ngFor="let indirizzo of contract.contractsDetailsAfterAggregation.indirizzoFornitura"
               class="col-lg-12 col-md-12 col-sm-12 no-padding">
            <div class="border-bottom-table" style="padding: 5px; min-height: 58px;">{{indirizzo}}</div>
          </div>
        </div>
        <div *ngIf="!isExtraSmall && contract.contractsDetailsAfterAggregation.indirizzoFornitura.length <= 1"
             class="col-lg-3 col-md-3 col-sm-3 border-left-table border-right-table cell-style min-height">{{contract.indirizzoFornitura}}</div>

        <div *ngIf="contract.contractsDetailsAfterAggregation.POD.length > 1"
             class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style min-height no-padding"
             style="flex-direction: column;">
          <div *ngFor="let POD of contract.contractsDetailsAfterAggregation.POD"
               class="col-lg-12 col-md-12 col-sm-12 no-padding">
            <div class="border-bottom-table" style="padding: 5px; min-height: 58px;">{{POD}}</div>
          </div>
        </div>
        <div *ngIf="contract.contractsDetailsAfterAggregation.POD.length <= 1"
             class="col-lg-3 col-md-3 col-sm-3 col-xs-6 border-right-table cell-style min-height">{{contract.POD}}</div>

        <div *ngIf="contract.contractsDetailsAfterAggregation.PDR.length > 1"
             class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style min-height no-padding"
             style="flex-direction: column;">
          <div *ngFor="let PDR of contract.contractsDetailsAfterAggregation.PDR"
               class="col-lg-12 col-md-12 col-sm-12 no-padding">
            <div class="border-bottom-table" style="padding: 5px; min-height: 58px;">{{PDR}}</div>
          </div>
        </div>
        <div *ngIf="contract.contractsDetailsAfterAggregation.PDR.length <= 1"
             class="col-lg-3 col-md-3 col-sm-3 col-xs-6 cell-style min-height">{{contract.PDR}}</div>
      </div>

      <span class="text-danger show" *ngIf="formGroupChooseContract.controls['option'].hasError('required') &&
       (formGroupChooseContract.controls['option'].dirty || formGroupChooseContract.controls['option'].touched)"
            style="margin-bottom: 10px;">Campo Obbligatorio</span>
      <button *ngIf="contracts.length" class="btn-conferma btn-prosegui" (click)="chooseContract()">PROSEGUI</button>
    </div>
  </div>
</div>

<div class="modal-div show" *ngIf="showErrorWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <img class="image modal-image" src="/assets/img/icons/Alert.png" alt="Alert">
    <div class="modal-text">Il codice inserito non è valido</div>
  </div>
</div>

<div class="modal-div show" *ngIf="showSuccessWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <img class="image modal-image" src="/assets/img/icons/ok.png" alt="OK">
    <div class="modal-text" style="font-size: 30px"><b>Hai attivato la tua Energy Card!</b></div>
    <div class="modal-text" style="font-size: 20px">Il voucher Energia sarà utilizzato per pagare le fatture delle
      utenze Luce e/o Gas associate al tuo contratto <b>№{{this.chosenContract.idGruppo}}</b></div>
  </div>
</div>
