import {Component, OnInit} from '@angular/core';
import {VoucherCardService} from '../../../../common/services/voucher-card/voucher-card.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {ContractService} from '../../../../common/services/contracts/userContract.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {ContractsDetails, RedeemVoucher, VoucherInformation} from '../../model/RedeemVoucher';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import {PdfService} from '../../../../common/services/pdf/pdf.service';

@Component({
  selector: 'app-energy-card',
  templateUrl: './energy-card.component.html',
  styleUrls: ['./energy-card.component.scss']
})
export class EnergyCardComponent implements OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  formGroup: FormGroup;
  formGroupChooseContract: FormGroup;
  showErrorWindow: boolean;
  showSuccessWindow: boolean;
  showAlreadyUsedCard: boolean;
  showBlockForChooseContract: boolean;
  contracts: ContractsDetails[] = [];
  chosenContract: ContractsDetails;
  cardValue: number;
  usedCardsInformation: VoucherInformation[] = [];
  totalSumVouchers: string;
  isExtraSmall = window.innerWidth <= 765;
  isMediumDevices = window.innerWidth <= 1200;

  constructor(private voucherCardService: VoucherCardService, private formBuilder: FormBuilder, private contractService: ContractService,
              private downloadFileService: PdfService, private incidentEventService: IncidentEventService) {
    this.voucherCardService.getInformationAboutUserCards('2').subscribe(response => {
      if (response.entities && response.entities.length > 0) {
        this.showAlreadyUsedCard = true;
        for (let i = 0; i < response.entities.length; i++) {
          this.aggregationInformation(response.entities[i]);
        }
        this.totalSumVouchers = this.usedCardsInformation.reduce((sum, item) => sum + item.creditoResiduo, 0).toFixed(2);
      }
    });
    this.formGroup = this.formBuilder.group({
      voucher: [null, Validators.required]
    });
    this.formGroupChooseContract = this.formBuilder.group({
      option: [null, Validators.required]
    });
  }

  aggregationInformation(card: VoucherInformation) {
    let foundMatch = false;
    if (this.usedCardsInformation && this.usedCardsInformation.length === 0) {
      this.usedCardsInformation.push(card);
    } else {
      for (let i = 0; i < this.usedCardsInformation.length; i++) {
        if (this.usedCardsInformation[i].codiceCoupon === card.codiceCoupon) {
          foundMatch = true;
          this.usedCardsInformation[i].pod !== null ? this.usedCardsInformation[i].pdr = card.pdr : this.usedCardsInformation[i].pod = card.pod;
        }
      }
      if (!foundMatch) {
        this.usedCardsInformation.push(card);
      }
    }
  }

  downloadFile(endUrl) {
    this.downloadFileService.downloadInvoicePDF(endUrl).subscribe(res => {
      const blob = new Blob([res], {type: 'application/pdf'});
      const data = window.URL.createObjectURL(blob);
      window.open(data, '_blank');
    });
  }

  ngOnInit() {
  }

  checkCode() {
    if (!this.formGroup.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    } else {
      this.voucherCardService.checkVoucherCard(this.formGroup.value.voucher).subscribe(result => {
        if (!result.entities || result.entities[0].stato !== 'DA_ASSOCIARE_CLIENTE'
          || result.entities[0].tipoCard !== 'Energy Card') {
          this.showErrorWindow = true;
        } else {
          this.cardValue = result.entities[0].valore;
          this.aggregateContracts();
          this.showBlockForChooseContract = true;
        }
      });
    }
  }

  aggregateContracts() {
    this.voucherCardService.getContractsInformation().subscribe(contracts => {
      contracts.response.forEach(contract => {
        if ((contract.tipoContratto === 'ENERGIA' || contract.tipoContratto === 'GAS') && contract.tipoProdotto !== 'PS'
          && contract.idGruppo) {
          const existingContract = this.contracts.find(item => item.idGruppo === contract.idGruppo);
          if (existingContract) {
            this.contracts.forEach(item => {
              if (item.idGruppo === contract.idGruppo) {
                let isFound = false;
                for (let i = 0; i < item.contractsDetailsAfterAggregation.indirizzoFornitura.length; i++) {
                  if (item.contractsDetailsAfterAggregation.indirizzoFornitura[i] === contract.indirizzoFornitura) {
                    isFound = true;
                    if (!item.contractsDetailsAfterAggregation.POD[i]) {
                      item.contractsDetailsAfterAggregation.POD[i] = contract.POD;
                    }
                    if (!item.contractsDetailsAfterAggregation.PDR[i]) {
                      item.contractsDetailsAfterAggregation.PDR[i] = contract.PDR;
                    }
                  }
                }
                if (!isFound) {
                  item.contractsDetailsAfterAggregation.indirizzoFornitura.push(contract.indirizzoFornitura);
                  item.contractsDetailsAfterAggregation.POD.push(contract.POD);
                  item.contractsDetailsAfterAggregation.PDR.push(contract.PDR);
                }
              }
            });
          } else {
            this.contracts.push(new ContractsDetails(contract));
          }
        }
      });
    });
  }

  chooseContract() {
    if (!this.formGroupChooseContract.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupChooseContract);
    } else {
      this.chosenContract = this.contracts.find(item => item.idContratto === parseInt(this.formGroupChooseContract.value.option, 10));
      this.showSuccessWindow = true;
      const requestBody = new RedeemVoucher(localStorage.getItem('clientId'), 'SelfCare', this.formGroup.value.voucher,
        parseInt(localStorage.getItem('clientId'), 10), parseInt(this.formGroupChooseContract.value.option, 10), false);
      this.voucherCardService.redeemVoucherCard(requestBody).subscribe(response => {
        if (response.entities && response.entities[0].stato === 'DA_UTILIZZARE') {
          this.incidentEventService.openIncidentEventForRedeemVoucher(this.formGroup.value.voucher, 'ENERGY', this.cardValue).subscribe(responseIncident => {
            if (responseIncident.status === ServiceResponseStatus.OK) {
              this.showSuccessWindow = true;
            }
          });
        }
      });
    }
  }

  hideModalWindow() {
    if (this.showSuccessWindow) {
      window.location.reload();
    } else {
      this.showErrorWindow = false;
    }
  }
}
