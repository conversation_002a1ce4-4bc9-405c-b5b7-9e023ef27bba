@import "~app/shared/styles/colors";

.page-title {
  border: 2px solid #B0C7DD;
  border-radius: 10px;
  height: 70px;
  display: flex;
  align-items: center;

  .title-image {
    background: url("/assets/img/optima/Set_Icone_ENERGY_MOBILE_CARD.png") no-repeat center;
    background-size: contain;
    width: 39px;
    height: 39px;
    float: left;
    margin-left: 5px;
  }

  .text {
    color: $dark-blue;
    margin-left: 20px;
    float: left;
    font-size: 15px;
  }
}

.app--user-mobile-card {
  margin-top: 2%;
  float: left;
  width: 100%;
  color: $dark-blue;
}

.mobile-card-border-style {
  border: 1px solid #B0C7DD;
  border-radius: 10px;
  padding: 23px;
}

.header-text {
  font-size: 20px;
  color: $dark-blue;
  margin-bottom: 8px;
}

hr {
  height: 1px;
  border-width: 0;
  background-color: #B0C7DD;
  margin-top: 1.5%;
  margin-bottom: 1.5%;
}

.label-text {
  font-size: 15px;
}

.input-container {
  display: flex;
  height: 35px;
  margin-top: 20px;
}

input[type="text"] {
  border: 1px solid #B0C7DD;
  border-radius: 5px;
  width: 250px;
  text-align: center;
  margin-right: 25px;
  color: $dark-blue;
  font-weight: bold;
  height: 35px;
}

input[type="radio"] {
  width: 16px;
  height: 16px;
  border: 2px solid #36749A;
  margin-right: 15px;
  margin-top: 0;
}

input[type="text"]::placeholder {
  text-align: center;
  font-size: 15px;
  color: #979797;
  font-weight: normal;
}

input[type="text"]::-ms-input-placeholder { /* Edge 12+ */
  text-align: center;
  font-size: 15px;
  color: #979797;
  font-weight: normal;
}

.btn-conferma {
  background: #36749A;
  border: 1px solid #36749A;
  border-radius: 10px;
  font-size: 12px;
  color: white;
  padding-left: 15px;
  padding-right: 15px;
  font-weight: bold;
}

.btn-prosegui {
  height: 35px;
  width: 95px;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.form-control {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  width: 340px;
}

.input-number-container {
  display: flex;
  align-items: center;
  height: 35px;
  margin-top: 15px;
  margin-bottom: 15px;
  width: 462px;

  label {
    font-weight: normal;
    margin: 0;
    width: 122px;
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 350px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 600px;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 70%;
  margin: auto;
  padding-top: 30px;
  font-size: 28px;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: 4% auto;
  width: 20%;
  display: block;
}

.totale-block {
  background: #FFFFFF;
  border: 1px solid #B2C9DF;
  border-radius: 5px;
  padding: 11px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 13px;
  margin-bottom: 8px;
  width: 55%;
}

.font-size-18 {
  font-size: 18px;
}

.short-items-mobile-card {
  padding: 5px 15px;
  font-size: 15px;
  background: #ECF2F7;
  border-radius: 5px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.details-mobile-card {
  padding: 15px;
  font-size: 15px;
}

.margin-down {
  margin-bottom: 10px;
}

.short-items-table {
  background: #FFFFFF;
  border: 0.75px solid #B2C9DF;
  border-radius: 5px;
  margin-top: 20px;
  padding: 5px 15px 5px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer
}

.short-items-table-contracts {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.border-table {
  border: 0.75px solid #B2C9DF;
  border-radius: 5px;
  margin-top: 20px;
  margin-bottom: 20px;
  font-size: 15px;

  .header-table {
    height: 33px;
    display: flex;
    align-items: center;
  }

  .border-bottom-table {
    border-bottom: 0.75px solid #B2C9DF;
  }

  .cell-style {
    height: auto;
    min-height: 29px;
    display: flex;
    align-items: center;
    overflow-wrap: anywhere;
  }

  .cell-button-download {
    text-decoration: underline;
    cursor: pointer;
    font-size: 13px;
    white-space: nowrap;
  }

  .cell-image {
    width: 17px;
    height: 17px;
    margin-left: 10px;
  }

  .min-height {
    min-height: 70px;
  }

  .border-right-table {
    border-right: 0.75px solid #B2C9DF;
  }

  .border-left-table {
    border-left: 0.75px solid #B2C9DF;
  }

  .background-header {
    background: #F0F4F8;
  }

  .border-header {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
  }
}

@media screen and (max-width: 991px) {
  .page-title {
    display: none;
  }
  .mobile-card-border-style {
    background-color: white;
  }
  .details-energy-card {
    padding: 10px;
  }
}

@media screen and (max-width: 695px) {
  .border-table {
    .cell-style {
      min-height: 61px;
    }
  }
}

@media screen and (max-width: 650px) {
  .input-container {
    justify-content: center
  }
  .text-center-block {
    text-align: center;
  }
  .totale-block {
    margin: 13px auto 8px;
    width: 85%;
  }
  hr {
    margin-top: 2.5%;
    margin-bottom: 2.5%;
  }
  .inner-modal-div {
    width: 90%;
  }
}

@media screen and (max-width: 575px) {
  .border-table {
    font-size: 13px;
  }
  .input-number-container {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .border-table {
    .cell-style {
      min-height: 81px;
    }
  }
}

@media screen and (max-width: 415px) {
  .label-text {
    text-align: center;
  }
  .input-container {
    height: auto;
    flex-direction: column;
    align-items: center;
  }
  .btn-conferma {
    width: 104px;
    height: 35px;
  }
  input[type="text"] {
    margin-right: 0;
    margin-bottom: 15px;
    width: 100%;
  }
  .totale-block {
    width: 100%;
    padding: 11px 11px;
  }

}
