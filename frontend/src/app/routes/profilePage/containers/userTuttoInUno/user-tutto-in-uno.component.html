<div class="col-md-9 app--user-tutto-in-uno">
  <div class="col-lg-12 page-title">
    <div class="title-image"></div>
    <div class="text">IL TUO TUTTO-IN-UNO</div>
  </div>

  <!-- OLD LAYOUT (for non-light and non-stabilized offer) $unstabilizedOffers | async && !light-->
  <div *ngIf="$unstabilizedOffers | async"><!--*ngIf="!stabilizatto && !light"-->
    <div *ngIf="hasActiveOffers | async; else noOffers">
      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding tuto-in-uno-card"
           *ngFor="let activeOffer of $unstabilizedOffers | async">
        <div class="flex">
          <div class="col-lg-8 col-md-8 col-xs-12 col-sm-12 client-summary-block m-top-15">
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered no-padding">
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-8 col-md-8 col-xs-7 col-sm-8 bill-type">
                  <b>Canone Tutto-In-Uno</b> (per i soli servizi attivi)
                </div>
                <div
                  class="col-lg-4 col-md-4 col-sm-4 col-xs-5 bill-summary">
                  <b>€ {{activeOffer.canoneMensile | number : '1.2-2'}}</b></div>
              </div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-8 col-md-8 col-sm-8 col-xs-7 bill-type"><b>Saldo Conto Relax</b></div>

              <div class="col-lg-2 col-md-2 col-sm-2 col-xs-5 bill-summary"
                   *ngIf="activeOffer.valoreSaldoPrevisionale >= 0 || activeOffer?.valueLastSaldoCr >= 0  else negativeBalance;">
                <b>€ {{activeOffer.saldoContoRelax | number : '1.2-2'}}</b>
              </div>
              <ng-template #negativeBalance>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 bill-summary negative">
                  <b>€ {{activeOffer.saldoContoRelax | number : '1.2-2'}}</b>
                </div>
               <!-- <div
                  class="col-lg-2 col-md-2 col-sm-2 col-xs-3 negative-balance">
                  <button class="negative-balance-button"
                          (click)="openNegativeBalanceModal(activeOffer.billingId, activeOffer.saldoContoRelax)">PAGA
                  </button>
                </div>-->
              </ng-template>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="initialBonusProgress">
              <div class="col-lg-8 col-md-8 col-xs-7 col-sm-8 bill-type"><b>Incentivo fedeltà</b></div>
              <div class="col-lg-4 col-md-4 col-xs-5 col-sm-4 bill-summary">
                <b>€ {{initialBonusProgress[activeOffer.billingId]?.currentInitialBonus}}</b></div>
            </div>
          </div>
        </div>
        <div class="col-lg-4 col-md-4 col-xs-12 col-sm-6 expiry-year-block bordered m-top-15 no-padding">

          <div class="col-lg-12 col-md-12 col-sm-6 col-xs-6 expiry-year-title">
            Scadenza anno contrattuale in corso:
          </div>
          <div class="col-lg-12 col-md-12 col-sm-6 col-xs-6 expiry-year">
            {{activeOffer.scadenzaAnnoContrattuale | date : 'dd/MM/yyyy'}}
          </div>

          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 remodulation-request"
               *ngIf="activeOffer.valoreSaldoPrevisionale < 0">
            <button class="remodulation-request-button"
                    (click)="openRemodulationRequestModal(activeOffer.billingId)">
              Richiedi rimodulazione
            </button>
          </div>
        </div>

      </div>

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
           *ngIf="activeOffer?.ee?.length || activeOffer?.gas?.length || activeOffer?.voce?.length || activeOffer?.adsl?.length">
        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row-title no-padding">
          <div class="col-lg-5 col-md-5 col-xs-4 column-title right-border">Servizi inclusi</div>
          <div class="col-lg-4 col-md-4 col-xs-4 column-title right-border">Consumi inclusi</div>
          <div class="col-lg-3 col-md-3 col-xs-4 column-title">Tariffe extrasoglia</div>
        </div>
        <div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngFor="let ee of activeOffer.ee">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>POD {{ee.utenza}}</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 bill-type">
              <span class="consumption-label-mobile"><b>Consumi inclusi:</b></span>
              <b class="consumi-label">{{ee.kwh}} kWh/mese</b>
            </div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation">
              <button class="tariff-button" (click)="checkYourTariff('ENERGIA', ee.utenza)">Tariffe extrasoglia</button>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
               *ngFor="let gas of activeOffer.gas">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>PDR {{gas.utenza}}</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 bill-type">
              <span class="consumption-label-mobile"><b>Consumi inclusi:</b></span>
              <b class="consumi-label">{{gas.mc}} Smc/mese</b>
            </div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation">
              <button class="tariff-button" (click)="checkYourTariff('GAS', gas.utenza)">Tariffe extrasoglia</button>
            </div>
          </div>

          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="activeOffer.voce?.length">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>Telefonia Fissa</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 bill-type">
              <span class="consumption-label-mobile"><b>Consumi inclusi:</b></span>
              <div *ngFor="let voce of activeOffer.voce">
                <b><span *ngIf="!voce.minuti;else minuti" class="consumi-label">FLAT</span>
                  <ng-template #minuti>{{voce.minuti}}</ng-template>
                  <span *ngIf="voce.descrizione === 'Minuti verso fisso'">
                    minuti <span class="verso-numeri-label"> verso numeri fissi</span>/mese
                  </span>
                  <span *ngIf="voce.descrizione === 'Minuti verso mobile'">
                    minuti <span class="verso-numeri-label"> verso numeri mobili</span>/mese
                  </span>
                </b>
              </div>
            </div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation" *ngIf="voce">
              <button class="tariff-button" (click)="checkYourTariff('VOCE', voce.utenza)">Tariffe extrasoglia</button>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="activeOffer.adsl">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>Internet</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 bill-type">
              <span class="consumption-label-mobile"><b>Consumi inclusi:</b></span>
              <span><b class="consumi-label">Illimitato</b></span>
            </div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation">
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="chartConfigs && chartConfigs[activeOffer.billingId]"
           class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding tuto-in-uno-chart">
        <div class="col-lg-12 col-md-12 title">Andamento Conto Relax</div>
        <div class="col-lg-12 col-md-12 chart-block">
          <app-chart [config]="chartConfigs[activeOffer.billingId]"></app-chart>
        </div>
      </div>
    </div>
  </div>
  <ng-template #noOffers>
    <div class="no-content">Non risulta alcuna utenza attiva.</div>
  </ng-template>

    <!--stabilizatto part (new layout)-->
  <div *ngIf="$stabilizedOffers | async"> <!--*ngIf="stabilizatto && !light"-->
    <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding tuto-in-uno-card"
         *ngFor="let activeOffer of $stabilizedOffers | async">
      <div class="flex">
        <div
          class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered m-top-15 no-padding expiry-year-block-stabilizatto">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 expiry-year-title-stabilizatto">
            Scadenza anno contrattuale in corso:
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 expiry-year-stabilizatto">
            {{activeOffer.scadenzaAnnoContrattuale | date : 'dd/MM/yyyy'}}
          </div>
        </div>

      </div>

      <div class="icons">
        <div class="icon-luce luce-icon-stabilizatto" *ngIf="activeOffer?.ee?.length">
          <label class="icon-stabilizatto-label"> Luce </label>
        </div>
        <div class="icon-gas gas-icon-stabilizatto" *ngIf="activeOffer?.gas?.length">
          <label class="icon-stabilizatto-label"> Gas </label>
        </div>
      </div>

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
           *ngIf="activeOffer.ee || activeOffer.gas">
        <div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b> Canone Energia (per i soli servizi attivi)</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-value">
              <b class="consumi-label">
                &euro; {{activeOffer.canoneStabilizzato ? (activeOffer.canoneStabilizzato | number : '1.2-2') : '0.00'}}</b>
            </div>
          </div>

          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>Saldo Conto Stabilità</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-value"
                 *ngIf="activeOffer.valoreSaldoPrevisionale >= 0 || activeOffer?.valueLastSaldoCr >= 0;  else negativeBalance;">
              <b class="consumi-label"> &euro; {{activeOffer.saldoContoRelax | number : '1.2-2'}}</b>
            </div>
            <ng-template #negativeBalance>
              <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 bill-summary negative">
                <b>€ {{activeOffer.saldoContoRelax | number : '1.2-2'}}</b>
              </div>
              <div class="col-lg-2 col-md-2 col-sm-2 col-xs-3 negative-balance">
                <button class="negative-balance-button"
                        (click)="openNegativeBalanceModal(activeOffer.billingId, activeOffer.saldoContoRelax)">PAGA
                </button>
              </div>
            </ng-template>
          </div>

          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="initialBonusProgress &&
            initialBonusProgress[activeOffer.billingId]?.currentInitialBonus">
            <div class="col-lg-5 col-md-5 col-xs-12 bill-type"><b>Incentivo Fedeltà</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-value">
            <span><b
              class="consumi-label">€ {{initialBonusProgress[activeOffer.billingId]?.currentInitialBonus
              ? initialBonusProgress[activeOffer.billingId]?.currentInitialBonus : '0.00'}}</b></span>
            </div>
          </div>
        </div>

      </div>

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
           *ngIf="activeOffer?.ee?.length || activeOffer?.gas?.length">
        <div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
            <div class="col-lg-5 col-md-5 col-xs-12 stabilizato-header"><b>Utenze incluse nel canone</b></div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Consumi</b></div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 stabilizato-header"><b>Tariffe extrasoglia</b></div>
          </div>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngFor="let ee of activeOffer.ee">
            <div class="col-lg-5 col-md-5 col-xs-12 stabilizato-label"><b>Luce POD</b>  {{ee.utenza}}  </div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><b>{{ee.kwh}} kWh/mese</b></div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation">
              <button class="tariff-button" (click)="checkYourTariff('ENERGIA', ee.utenza)">Tariffe extrasoglia</button>
            </div>
          </div>

          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
               *ngFor="let gas of activeOffer.gas">
            <div class="col-lg-5 col-md-5 col-xs-12 stabilizato-label"><b>Gas PDR</b> {{gas.utenza}} </div>
            <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><b>{{gas.mc}} Smc/mese</b></div>
            <div class="col-lg-3 col-md-3 col-xs-12 col-sm-12 tariff-navigation">
              <button class="tariff-button" (click)="checkYourTariff('GAS', gas.utenza)">Tariffe extrasoglia</button>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="chartConfigs && chartConfigs[activeOffer.billingId] && isChartExistMap.get(activeOffer.billingId)"
           class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding tuto-in-uno-chart">
        <div class="col-lg-12 col-md-12 title">Andamento Conto Relax</div>
        <div class="col-lg-12 col-md-12 chart-block">
          <app-chart [config]="chartConfigs[activeOffer.billingId]"></app-chart>
        </div>
      </div>

      <!-- Previous voce check, can be useful getEmptyVoceAmount(activeOffer) !== activeOffer.voce.length-->
      <div *ngIf="(activeOffer?.voce)
             || (activeOffer?.mobile && getEmptyMobileAmount(activeOffer) !== activeOffer.mobile.length)
             || activeOffer?.adsl
             || activeOffer?.assicurazione
             || activeOffer?.coverCare">
        <div class="icons">
          <div *ngIf="activeOffer?.voce && activeOffer.voce.length >= 1"
               class="icon-fisso voce-icon-stabilizatto">
            <label class="icon-stabilizatto-label"> Voce </label>
          </div>
          <div *ngIf="activeOffer?.adsl" class="icon-internet internet-icon-stabilizatto">
            <label class="icon-stabilizatto-label"> Internet </label>
          </div>
          <div *ngIf="activeOffer?.mobile && getEmptyMobileAmount(activeOffer) !== activeOffer.mobile.length"
               class="icon-mobile mobile-icon-stabilizatto">
            <label class="icon-stabilizatto-label"> Mobile </label>
          </div>
          <div *ngIf="activeOffer?.assicurazione" class="icon-assicurazione assicurazione-icon-stabilizatto">
            <label class="icon-stabilizatto-label"> Assicurazione </label>
          </div>
          <div *ngIf="activeOffer?.coverCare" class="icon-assistenza-caldaia assistenza-icon-stabilizatto">
            <label class="icon-stabilizatto-label "> Assistenza caldaia </label>
          </div>
        </div>

        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">
          <div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-header"><b>Servizi</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Utenze incluse</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Offerta</b></div>
            </div>

<!--            Voce with group, descrizione and minuti check -->

<!--            <div *ngIf="activeOffer?.voce">-->
<!--              <div *ngFor="let group of activeOffer.voce | groupBy: 'descrizione'"-->
<!--                   class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding shaded-starting-from-odd">-->

<!--                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Voce</b></div>-->
<!--                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">-->
<!--                  <u><a href="/home/<USER>" style="color: #36749C">Dettaglio utenze</a></u>-->
<!--                </div>-->
<!--                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">-->
<!--                <span *ngIf="!group.value[0].minuti && !(group.key === 'Minuti verso fisso'-->
<!--                    || group.key === 'Minuti verso mobile')">Traffico voce illimitato</span>-->
<!--                  <span *ngIf="group.key === 'Minuti verso fisso'-->
<!--                    || group.key === 'Minuti verso mobile'">Traffico voce a consumo</span>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->

            <div *ngIf="activeOffer?.voce">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Voce</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                <u><a href="/home/<USER>" style="color: #36749C">Dettaglio utenze</a></u>
              </div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
<!--                Traffico voce illimitato-->
              </div>
            </div>

            <div *ngIf="activeOffer?.adsl">
              <!--*ngFor="let adsl of activeOffer.adsl"-->
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding"
                   [ngClass]="getLengthOfGroupedArray(activeOffer.voce, 'descrizione')%2 == 0
                 ? 'shaded-starting-from-odd'
                 : 'shaded-starting-from-even'">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Internet</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                  <u><a href="/home/<USER>" style="color: #36749C">Dettaglio utenze</a></u></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                  Traffico dati illimitato
                </div>
              </div>
            </div>

            <div *ngIf="activeOffer?.mobile && getEmptyMobileAmount(activeOffer) !== activeOffer.mobile.length">
              <div *ngFor="let group of activeOffer.mobile | groupBy: 'nomeOfferta'"
                   class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding"
                   [ngClass]="getLengthOfGroupedArray(activeOffer.voce, 'descrizione')
                 + (activeOffer?.adsl ? activeOffer.adsl.length: 0) %2 == 0
                 ? 'shaded-starting-from-odd'
                 : 'shaded-starting-from-even'">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Mobile</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                  <u><a href="/home/<USER>" style="color: #36749C">Dettaglio utenze</a></u>
                </div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                  {{group.key === 'null' ? ' ' : group.key}}
                </div>
              </div>
            </div>

            <div
              *ngIf="activeOffer?.assicurazione && getEmptyAssicurazioneAmount(activeOffer) !== activeOffer.assicurazione.length">
              <div *ngFor="let group of activeOffer.assicurazione | groupBy: 'tipoPolizza'"
                   class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding"
                   [ngClass]="getLengthOfGroupedArray(activeOffer.voce, 'descrizione')
                 + (activeOffer?.adsl ? activeOffer.adsl.length: 0)
                 + getLengthOfGroupedArray(activeOffer.mobile, 'nomeOfferta') %2 == 0
                 ? 'shaded-starting-from-odd'
                 : 'shaded-starting-from-even'">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Assicurazione</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                {{group.value.length}} {{group.value.length > 1 ? 'Polizze' : 'Polizza'}}
                </div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                  {{group.key === 'null' ? ' ' : group.key}}
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 stabilizatto-button-container">
<!--        <button class="stabilizatto-button {{tariffButtonStyle}}" (click)="switchDisplayTariffTable()">TARIFFE</button>-->
<!--        <button class="stabilizatto-button" routerLink="/profile/tutto-in-uno/sconti">SCONTI</button>-->
          <span *ngIf="promoMeseOff && promoMeseOff.size > 0 && promoMeseOff?.has(activeOffer.billingId)">
            <button class="stabilizatto-button {{meseOffButtonStyleMap.get(activeOffer.billingId)}}" (click)="switchDisplayMeseOffTable(activeOffer.billingId)">PROMO MESEOFF</button>
          </span>
      </div>

      <div class="row">
        <div class="col-xl-12 col-md-12 hidden-xs" *ngIf="displayTariffTable">
          <ng-container *ngTemplateOutlet="tariffTable"></ng-container>
        </div>
      </div>

      <ng-template #tariffTable>
        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">
          <div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-header"><b>Servizi</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Tariffe</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Tariffe scontate</b></div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><u
                [matMenuTriggerFor]="tariffarioMenu">Piano Tariffario</u></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">-</div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="activeOffer.adsl">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Internet</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Illimitato></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Prezzo &euro;/Mese</div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                <b>Locali e nazionali Flat </b> <b> Mobile a consumo</b>
              </div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                <p>Prezzo &euro;/Mese </p>
                <p style="text-underline: #36749C">Piano tariffario </p>
              </div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><b>30 Smc</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label" style="text-underline: #36749C">
                Piano tariffario
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 stabilizatto-button-container"
           *ngIf="promoMeseOff?.has(activeOffer.billingId) && displayMeseOffTableMap.get(activeOffer.billingId)">
        <p>
          La promozione MeseOff prevede lo sconto del canone mensile.<br>
          Qui in basso puoi consultare e modificare {{promoMeseOff.get(activeOffer.billingId).length > 1 ? 'i mesi' : 'il mese'}} di erogazione dello sconto.
        </p>
        <b>Hai {{promoMeseOff.get(activeOffer.billingId).length}} {{promoMeseOff.get(activeOffer.billingId).length > 1 ? 'mesi' : 'mese'}} a cui è applicato lo sconto Promo MeseOff.</b>
      </div>

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
           *ngIf="promoMeseOff?.has(activeOffer.billingId) && displayMeseOffTableMap.get(activeOffer.billingId)">
        <ng-container *ngTemplateOutlet="meseOffTable"></ng-container>

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 stabilizatto-button-container">
          <button class="stabilizatto-button-success" (click)="promoMeseOffChangeStatus ? confirmareButtonHandler(activeOffer.billingId) : null"
                  [ngClass]="{'disabled': !promoMeseOffChangeStatus}">
            CONFERMA LA VARIAZIONE
          </button>
        </div>
      </div>

      <ng-template #meseOffTable>
        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
            <div *ngFor="let item of promoMeseOff.get(activeOffer.billingId); index as i" class="col-lg-4 col-md-4 col-xs-12 col-sm-12 mese-stabilizato-header">
              <b>Mese {{i+1}}</b>
              <br>

              <b>{{getMonthNameByDate(item.mese.dataInizioValidita)}}</b>
              <span *ngIf="!checkIfMeseIsModifiable(item.mese.dataInizioValidita); else meseModify" placement="right" triggers="click"
                    [popover]="mesePopTemplate">
                                        <em class="info-circle">i</em>
                                    </span>
              <ng-template #meseModify>
                  <span class="fa fa-pencil edit-button" tooltip="Modifica"
                        (click)="openMeseModal(activeOffer.billingId, item.mese.promoAttributes.dataLimiteInferiore, item.mese.promoAttributes.dataLimiteSuperiore,
                        item.mese.dataInizioValidita, i)">
                  </span>
              </ng-template>

            </div>
          </div>
        </div>
      </ng-template>

    </div>
  </div>

  <!--light part with unstabilized offer (new layout, not used yet due to lack of service for light value)-->
  <div *ngIf="light && $unstabilizedOffers | async">
    <div *ngIf="hasActiveOffers | async; else noOffers">

      <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 no-padding tuto-in-uno-card"
           *ngFor="let activeOffer of $unstabilizedOffers | async">
        <div class="flex">
          <div
            class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered m-top-15 no-padding expiry-year-block-stabilizatto">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 expiry-year-title-stabilizatto">
              Scadenza anno contrattuale in corso:
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 expiry-year-stabilizatto">
              {{activeOffer.scadenzaAnnoContrattuale | date : 'dd/MM/yyyy'}}
            </div>
          </div>
        </div>

        <div class="icons-light">
          <div class="icon-luce light-icon"></div>
          <p class="icon-light-label">Luce</p>
          <div class="icon-gas light-icon"></div>
          <p class="icon-light-label">Gas</p>
          <div class="icon-fisso light-icon"></div>
          <p class="icon-light-label">Voce</p>
          <div class="icon-internet light-icon"></div>
          <p class="icon-light-label">Internet</p>
          <div class="icon-mobile light-icon"></div>
          <p class="icon-light-label">Mobile</p>
          <div class="icon-assicurazione light-icon"></div>
          <p class="icon-light-label "> Assicurazione </p>
          <div class="icon-assistenza-caldaia light-icon"></div>
          <p class="icon-light-label "> Assistenza caldaia </p>
        </div>

        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
             *ngIf="activeOffer?.ee?.length
             || activeOffer?.gas?.length
             || activeOffer?.voce?.length
             || activeOffer?.adsl?.length">
          <div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-header"><b>Servizi</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Utenze incluse</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Offerta</b></div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
                 *ngFor="let ee of activeOffer.ee">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Luce</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                <u (click)="checkYourTariff('ENERGIA', ee.utenza)">{{activeOffer.ee.length}}
                  punto</u></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">A consumo</div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
                 *ngFor="let gas of activeOffer.gas">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Gas</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">
                <u (click)="checkYourTariff('GAS', gas.utenza)">{{activeOffer.gas.length}}
                  punto</u></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Prezzo &euro;/Mese</div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
                 *ngFor="let adsl of activeOffer.adsl">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Internet</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><u>{{activeOffer.adsl.length}}
                linee</u>
              </div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Traffico dati illimitato</div>
            </div>
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding"
                 *ngFor="let voce of activeOffer.voce">
              <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                <u (click)="checkYourTariff('VOCE', voce.utenza)"> {{activeOffer.voce.length}} linee </u>
              </div>
              <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                <p> Traffico voce illimitato </p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 stabilizatto-button-container">
          <!--<button class="stabilizatto-button {{tariffButtonStyle}}" (click)="switchDisplayTariffTable()">
            TARIFFE
          </button>-->
          <!--<button class="stabilizatto-button" routerLink="/profile/tutto-in-uno/sconti">SCONTI</button>-->
          <span *ngIf="promoMeseOff && promoMeseOff?.size > 0 && promoMeseOff?.has(activeOffer.billingId)">
          <button class="stabilizatto-button {{meseOffButtonStyleMap.get(activeOffer.billingId)}}" (click)="switchDisplayMeseOffTable(activeOffer.billingId)">PROMO MESEOFF</button>
          </span>
        </div>

        <div class="row">
          <div class="col-xl-12 col-md-12 hidden-xs" *ngIf="displayTariffTable">
            <ng-container *ngTemplateOutlet="tariffTable"></ng-container>
          </div>
        </div>

        <ng-template #tariffTable>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">
            <div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-header"><b>Servizi</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Tariffe</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header"><b>Tariffe scontate</b></div>
              </div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><u
                  [matMenuTriggerFor]="tariffarioMenu">Piano Tariffario</u></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">-</div>
              </div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding" *ngIf="activeOffer.adsl">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Internet</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Illimitato</div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">Prezzo &euro;/Mese</div>
              </div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                  <b>Locali e nazionali Flat </b> <b> Mobile a consumo</b>
                </div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label inline-grid">
                  <p>Prezzo &euro;/Mese </p>
                  <p style="text-underline: #36749C">Piano tariffario </p>
                </div>
              </div>
              <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
                <div class="col-lg-4 col-md-4 col-xs-12 stabilizato-label"><b>Telefonia Fissa</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label"><b>30 Smc</b></div>
                <div class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label" style="text-underline: #36749C">
                  Piano tariffario
                </div>
              </div>
            </div>
          </div>
        </ng-template>

<!-- Previous version of promo mese off and conferma btn -->

<!--        <div class="row">-->
<!--          <div class="col-xl-12 col-md-12 hidden-xs"-->
<!--               *ngIf="promoMeseOff?.has(activeOffer.billingId) && displayMeseOffTableMap.get(activeOffer.billingId)">-->
<!--            <ng-container *ngTemplateOutlet="meseOffTable"></ng-container>-->
<!--          </div>-->
<!--        </div>-->

<!--        <ng-template #meseOffTable>-->
<!--          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">-->


<!--            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">-->
<!--              <div *ngFor="let item of promoMeseOff.get(activeOffer.billingId); index as i" class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-header">-->
<!--                <b>Mese {{i+1}}</b>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">-->
<!--              <div *ngFor="let item of promoMeseOff.get(activeOffer.billingId)" class="col-lg-4 col-md-4 col-xs-12 col-sm-12 stabilizato-label">-->
<!--                <b>{{getMonthNameByDate(item.mese.dataInizioValidita)}}</b>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </ng-template>-->

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 stabilizatto-button-container"
             *ngIf="promoMeseOff?.has(activeOffer.billingId) && displayMeseOffTableMap.get(activeOffer.billingId)">
          <p>
            La promozione MeseOff prevede lo sconto del canone mensile.<br>
            Qui in basso puoi consultare e modificare il {{promoMeseOff.get(activeOffer.billingId).length > 1 ? 'mesi' : 'mese'}} di erogazione dello sconto.
          </p>
          <b>Hai {{promoMeseOff.get(activeOffer.billingId).length}} {{promoMeseOff.get(activeOffer.billingId).length > 1 ? 'mesi' : 'mese'}} a cui è applicato lo sconto Promo MeseOff.</b>
        </div>

        <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding"
             *ngIf="promoMeseOff?.has(activeOffer.billingId) && displayMeseOffTableMap.get(activeOffer.billingId)">
          <ng-container *ngTemplateOutlet="meseOffTable"></ng-container>

          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 stabilizatto-button-container">
            <button class="stabilizatto-button-success" (click)="promoMeseOffChangeStatus ? confirmareButtonHandler(activeOffer.billingId) : null"
                    [ngClass]="{'disabled': !promoMeseOffChangeStatus}">
              CONFERMA LA VARIAZIONE
            </button>
          </div>
        </div>

        <ng-template #meseOffTable>
          <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 bordered service-block no-padding">
            <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 summary-row no-padding">
              <div *ngFor="let item of promoMeseOff.get(activeOffer.billingId); index as i" class="col-lg-4 col-md-4 col-xs-12 col-sm-12 mese-stabilizato-header">
                <b>Mese {{i+1}}</b>
                <br>

                <b>{{getMonthNameByDate(item.mese.dataInizioValidita)}}</b>
                <span *ngIf="!checkIfMeseIsModifiable(item.mese.dataInizioValidita); else meseModify" placement="right" triggers="click"
                      [popover]="mesePopTemplate">
                                        <em class="info-circle">i</em>
                                    </span>
                <ng-template #meseModify>
                  <span class="fa fa-pencil edit-button" tooltip="Modifica"
                        (click)="openMeseModal(activeOffer.billingId, item.mese.promoAttributes.dataLimiteInferiore, item.mese.promoAttributes.dataLimiteSuperiore,
                        item.mese.dataInizioValidita, i)">
                  </span>
                </ng-template>

              </div>
            </div>
          </div>
        </ng-template>

      </div>

    </div>
  </div>
  <app-modal class="tariff-info-modal">
    <modal-title *ngIf="tariffInfo">
      <div [innerHTML]="tariffInfo | safeHtml"></div>
    </modal-title>
  </app-modal>

  <mat-menu #tariffarioMenu="matMenu" class="m-menu" xPosition="before" yPosition="below">
    <div class="mat-menu-style">
      <label class="mat-menu-label">Piano tariffario</label>
      <button class="menu-button odds-bg" mat-menu-item>
        <span class="icon-pdf-load"> </span>
        <a target='_blank'></a>
      </button>
    </div>
  </mat-menu>

  <mat-menu #offertaSpecialMenu="matMenu" class="m-menu" xPosition="before" yPosition="below">
    <div class="mat-menu-style">
      <label class="mat-menu-label">Optima 30 Special</label>
      <label class="m-menu-optima-special-label"> 1000 Minuti verso tutti</label>
      <label class="m-menu-optima-special-label"> 1500 Minuti verso Optima</label>
      <label class="m-menu-optima-special-label"> 100 SMS</label>
      <label class="m-menu-optima-special-label"> 30 GB in 4G</label>
    </div>
  </mat-menu>
</div>
<ng-template #mesePopTemplate>
  Per questa mensilità lo sconto risulta già emesso o in emissione nei prossimi giorni
</ng-template>
</div>
