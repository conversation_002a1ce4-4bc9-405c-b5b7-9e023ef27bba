import { Component, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { select } from '@angular-redux/store';
import { Subscription } from 'rxjs/Subscription';
import { Observable } from 'rxjs/Observable';
import { ObservableUtils } from '../../../../common/utils/ObservableUtils';
import { UserServicesService } from '../../userServices/userServices.service';
import { ServiceCard } from '../../../../common/components/service-card/ServiceCard';
import { ServicesActions } from '../../../../redux/services/actions';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import ServiceCardUtils from '../../../../common/components/service-card/service-card.utils';


@Component({
  selector: 'app-historical-utilities',
  templateUrl: './historical-utilities.component.html',
  styleUrls: ['./historical-utilities.component.scss']
})

export class HistoricalUtilitiesComponent implements OnDestroy {

  @select(['services'])
  services: Observable<ServiceStateModel>;

  servicesSubscription: Subscription;

  servicesCards: Array<ServiceCard>;

  constructor(private userServicesService: UserServicesService, private serviceActions: ServicesActions) {
    this.servicesSubscription = this.services.subscribe(services => {
      if (services.servicesLoaded) {
        Object.keys(services.inactiveServices).forEach(key => this.serviceActions.loadServiceDetails(key));
        this.servicesCards = this.buildServiceCards(services.inactiveServices, services);
      }
    });

  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.servicesSubscription]);
  }

  buildServiceCards(services, servicesState): ServiceCard[] {
    const cards = [];
    Object.keys(services).forEach(key => {
      const card = ServiceCardUtils.getCardGenerator(key)(services[key], servicesState);
      if (card.utilities && card.utilities.length) {
        if (card.serviceName === 'AMAZON') {
          card.serviceName = 'amazonprime'
        }
        cards.push(card);
      }
    });
    return cards;
  }

}
