import { IncidentEventCategory } from '../../../common/enum/IncidentEventCategory';

export const changeOfficeAddressIncidentEventAnnotation = (newAddress, oldAddress) => `Richiesta di modifica Sede legale da ${oldAddress} a ${newAddress}`;
export const changeBillingAddressIncidentEventAnnotation = (newAddress, oldAddress) => `Richiesta di modifica Indirizzo di fatturazione da ${oldAddress} a ${newAddress}`;


export const changeProfileIncidentEventAnnotationFactory = {
  [IncidentEventCategory.CHANGE_OFFICE_ADDRESS]: changeOfficeAddressIncidentEventAnnotation,
  [IncidentEventCategory.CHANGE_BILLING_ADDRESS]: changeBillingAddressIncidentEventAnnotation
};

export const incidentEventAnnotationBuilder = (incidentEventCategory: IncidentEventCategory) => changeProfileIncidentEventAnnotationFactory[incidentEventCategory];
