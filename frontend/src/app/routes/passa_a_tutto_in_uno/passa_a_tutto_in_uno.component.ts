import {Component, OnD<PERSON>roy} from '@angular/core';
import {EmailService} from '../../common/services/email/email.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import {UserData} from '../../common/model/userData.model';
import EmailMessage from '../../common/model/EmailMessage';
import {ObservableUtils} from '../../common/utils/ObservableUtils';
import {IncidentEventResponse} from '../autolettura/model/IncidentEventResponse';
import {IncidentEventService} from '../../common/services/incedentEvent/incident-event.service';
import {IncidentCategory} from '../../common/enum/IncidentCategory';
import {NotificationService} from '../../common/services/notification/notification.service';
import {messages, recapitiConsulenteAnnotation} from './config/config';


@Component({
  selector: 'app-passa',
  templateUrl: './passa_a_tutto_in_uno.component.html',
  styleUrls: ['./passa_a_tutto_in_uno.component.scss']
})

export class PassaComponent implements OnDestroy {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  userData: UserData;

  userDataSubscription: Subscription;

  constructor(private mailService: EmailService, private incidentEventService: IncidentEventService,
              private notificationService: NotificationService) {
    this.userDataSubscription = this.userInfo.subscribe(userInfo => {
      this.userData = userInfo;
    });
  }


  sendIntegratedSolutionRequest() {
    const {userData} = this;
    this.mailService.sendTuttoInUnoRequest({
      message: `Ciao, il cliente ${localStorage.getItem('clientId')} richiede da selfcare ricontatto per
      conversione a tutto in uno al ${userData && userData.phoneNumber ? userData.phoneNumber : 'suo nuomero personale'}.
      Grazie e buon lavoro!!`, subject: 'Richiesta conversione da Sciolto ad integrato'
    } as EmailMessage).flatMap(() => this.createIncidentEventRequest())
      .subscribe(response =>
          this.incidentEventService.createIncidentEventResponseNotification(response, messages.successMessage),
        error => {
          this.notificationService.errorMessage(messages.failedMessage);
        });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userDataSubscription]);
  }

  createIncidentEventRequest(): Observable<IncidentEventResponse> {
    return this.incidentEventService.commercialInformationIncidentEvent(localStorage.getItem('clientId'),
      IncidentCategory.RECAPITI_CONSULENTE, recapitiConsulenteAnnotation);
  }
}
