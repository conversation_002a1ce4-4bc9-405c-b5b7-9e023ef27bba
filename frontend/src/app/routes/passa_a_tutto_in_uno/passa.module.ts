import {NgModule} from '@angular/core';
import {PassaComponent} from './passa_a_tutto_in_uno.component';
import {CommonModule} from '../../common/common.module';
import {HttpClientModule} from '@angular/common/http';
import {SharedModule} from '../../shared/shared.module';
import {RouterModule, Routes} from '@angular/router';


/* Use this routes definition in case you want to make them lazy-loaded */
const routes: Routes = [
  {
    path: '',
    component: PassaComponent },
];

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    SharedModule,
    RouterModule.forChild(routes),
  ],
  providers: [],
  declarations: [PassaComponent],
  exports: [PassaComponent]
})
export class PassaModule {
}
