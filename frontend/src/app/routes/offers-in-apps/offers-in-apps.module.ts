import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Offer5gComponent } from './components/offer-5g/offer-5g.component';
import {RouterModule, Routes} from '@angular/router';
import {SharedModule} from "../../shared/shared.module";


const routes: Routes = [
  {path: '5G', component: Offer5gComponent}
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    SharedModule
  ],
  declarations: [Offer5gComponent],
  exports: [
    RouterModule
  ],
})
export class OffersInAppsModule { }
