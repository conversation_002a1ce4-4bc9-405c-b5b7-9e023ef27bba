import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Offer5gComponent } from './components/offer-5g/offer-5g.component';
import {RouterModule, Routes} from '@angular/router';
import {SharedModule} from "../../shared/shared.module";
import { OffersInAppService } from './service/offers-in-app.service';
import { MobileService } from '../../common/services/mobile/mobile.service';
import {CommonModule} from "../../common/common.module";


const routes: Routes = [
  {path: '5G', component: Offer5gComponent}
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    FormsModule,
    SharedModule,
    CommonModule
  ],
  declarations: [Offer5gComponent],
  providers: [OffersInAppService/*, MobileService*/],
  exports: [
    RouterModule
  ],
})
export class OffersInAppsModule { }
