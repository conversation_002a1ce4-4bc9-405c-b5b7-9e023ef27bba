:host {
  background-color: white;
  display: block;
  min-height: 100%;
  color: black;
}

.header {
  background-color: #05C6F2;
  color: white;
  height: 80px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.sim-selection, .activation-info, .confirmation-info {
  padding-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.title {
  color: #05C6F2;
  font-size: 20px;
  margin: 20px 0;
  font-weight: bold;
  max-width: 85vw;
}

.title-conformation {
  color: black;
  font-size: 22px;
  font-weight: bold;
  max-width: 85vw;
  margin-top: 40px;
}

.sim-dropdown {
  width: 100%;
  max-width: 400px;
  margin: 20px 0;
}

.label-sim {
  text-align: left;
  color: #9A9A9A;
  display: block;
  max-width: 80vw;
  padding: 10px 15px;
  font-size: 15px;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
  border: 0;
  width: 370px;
  max-width: 90vw;
  padding: 5px 15px;
  font-weight: normal;
  font-size: 20px;
  border-bottom: 1px solid #d8d8d8;
}

.icon-container {
  margin: 20px 0;
}

.icon-5g {
  width: 70px;
  height: 70px;
}

.icon-ok {
  width: 140px;
  height: 140px;
}

.info-text {
  margin-bottom: 20px;
}

.sim-number, .renewal-date, .already-activated-info {
  font-size: 20px;
  margin: 15px 0;
}

.cost {
  margin: 10px 0;
  font-size: 15px;
}

.additional-info {
  margin: 20px 0;
  line-height: 1.5;
  font-size: 15px;
  max-width: 80vw;
}

hr {
  border: none;
  border-top: 1px solid #c8c8c8;
  margin: 10px 0;
  width: 410px;
  max-width: 85vw;
}

.proceed-button {
  background: linear-gradient(270deg, #00D1FF 0%, #00B9E3 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 5px 40px;
  font-size: 15px;
  cursor: pointer;
  margin: 20px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(11px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 30px 30px 0 0;
  border: 0;
  position: relative;
  width: 100%;
  max-width: 100vw;
  height: 80vh; /* Збільшена висота до 80% екрану */
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  box-shadow: 0px -3px 6px #00000029;
  animation: slideUp 0.5s ease-out 0.1s forwards;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-content.closing {
  animation: slideDown 0.5s ease-in forwards;
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 40px;
  top: 15px;
  color: #00b9e2;
  cursor: pointer;
  z-index: 1001;
}

.modal-body {
  padding: 80px 30px 40px 30px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  overflow: hidden;
}

.modal-title {
  font-size: 17px;
  font-weight: 400;
  margin-bottom: 20px;
  line-height: 1.4;
  max-width: 400px;
  font-style: italic;
  flex-shrink: 0;
}

.contract-content {
  flex: 1;
  overflow-y: auto;
  max-width: 100%;
  width: 100%;
  margin-bottom: 20px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 15px;
  font-size: 14px;
  line-height: 1.6;
  text-align: left;
  border: 1px solid #e0e0e0;
}

.contract-content h3 {
  color: #00b9e2;
  font-size: 16px;
  margin: 20px 0 10px 0;
  font-weight: 600;
}

.contract-content p {
  margin: 10px 0;
  color: #333;
}

.contract-content strong {
  color: #00b9e2;
}

.modal-text {
  font-size: 16px;
  color: #606060;
  margin-bottom: 20px;
  line-height: 1.5;
  max-width: 300px;
  flex-shrink: 0;
}

.activate-button {
  background: linear-gradient(270deg, #00D1FF 0%, #00B9E3 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 35px;
  font-size: 15px;
  cursor: pointer;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

/* Стилізація скролбару */
.contract-content::-webkit-scrollbar {
  width: 8px;
}

.contract-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.contract-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00D1FF 0%, #00B9E3 100%);
  border-radius: 10px;
}

.contract-content::-webkit-scrollbar-thumb:hover {
  background: #00a0c7;
}

.hidden {
  display: none;
}

/* Респонсив для мобільних */
@media (max-width: 768px) {
  .modal-content {
    height: 85vh;
    max-height: 85vh;
  }

  .modal-body {
    padding: 60px 20px 30px 20px;
  }

  .contract-content {
    padding: 15px;
    font-size: 13px;
  }
}
