:host {
  background-color: white;
  display: block;
  min-height: 100%;
  color: black;
}


.header {
  background-color: #05C6F2;
  color: white;
  height: 100px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.sim-selection, .activation-info {
  padding-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}


.title {
  color: #05C6F2;
  font-size: 20px;
  margin: 20px 0;
  font-weight: bold;
  max-width: 85vw;
}

.sim-dropdown {
  width: 100%;
  max-width: 400px;
  margin: 20px 0;
}

.label-sim {
  text-align: left;
  color: #9A9A9A;
  display: block;
  max-width: 80vw;
  padding: 10px 15px;
  font-size: 15px;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
  border: 0;
  width: 370px;
  max-width: 90vw;
  //color: black;
  padding: 5px 15px;
  font-weight: normal;
  font-size: 20px;
  border-bottom: 1px solid #d8d8d8;
}

.icon-container {
  margin: 20px 0;
}

.icon-5g {
  width: 80px;
  height: 80px;
}

.info-text {
  margin-bottom: 20px;
}

.sim-number, .renewal-date {
  font-weight: bold;
  font-size: 20px;
  margin: 15px 0;
}

.cost {
  margin: 10px 0;
  font-size: 15px;
}

.additional-info {
  margin: 20px 0;
  line-height: 1.5;
  font-size: 15px;
  max-width: 80vw;
}

hr {
  border: none;
  border-top: 1px solid #c8c8c8;
  margin: 15px 0;
  width: 410px;
  max-width: 85vw;
}

.proceed-button {
  background: linear-gradient(270deg, #00D1FF 0%, #00B9E3 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 5px 40px;
  font-size: 15px;
  cursor: pointer;
  margin: 20px;
}

@media (max-width: 431px) {
/*  .label-sim {
    padding: 0;
  }*/
}
