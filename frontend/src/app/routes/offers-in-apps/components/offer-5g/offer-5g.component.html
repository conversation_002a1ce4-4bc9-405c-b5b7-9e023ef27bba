<div class="header">
  Aggiungi 5G
</div>

<!-- Selecting a SIM card -->
<div class="sim-selection" *ngIf="viewMode === ViewModeLayout.SELECT_NUMBER">
  <div class="title">Per procedere seleziona una delle tue SIM</div>
  <div class="sim-dropdown">
    <div class="label-sim">SIM selezionata</div>
    <select [(ngModel)]="selectedSim" class="sim-select">
      <option *ngFor="let sim of simList" [ngValue]="sim">{{ sim.msisdnId }}</option>
    </select>
  </div>
  <button class="proceed-button" (click)="proceed()">PROCEDI</button>
</div>

<!-- Information about delayed activation -->
<div class="activation-info" *ngIf="viewMode === ViewModeLayout.ACTIVATION_WITH_DELAY">
  <div class="icon-container">
    <img src="assets/img/offers-in-app/5G.png" alt="5G" class="icon-5g">
  </div>
  <div class="info-text">
    <div class="title">Stai attivando l'opzione 5G sulla tua SIM</div>
    <div class="sim-number font-weight-bold">{{ selectedSim?.msisdnId }}</div>
    <div class="cost">al costo mensile di {{ current5GAddOn?.canoneMese }} €</div>
  </div>
  <hr>
  <div class="additional-info">
    <p>A decorrere dal prossimo rinnovo mensile di Giga, Minuti e SMS previsto per il</p>
    <p class="renewal-date font-weight-bold">{{ selectedSim?.dataProssimoRinnovo | date : 'dd/MM/yyyy' }}</p>
    <p>riceverai l'attivazione dell'opzione e il relativo addebito</p>
  </div>
  <hr>
  <button class="proceed-button" (click)="openModal()">PROCEDI</button>
</div>

<!-- Information on immediate activation -->
<div class="activation-info" *ngIf="viewMode === ViewModeLayout.ACTIVATION_IMMEDIATE">
  <div class="icon-container">
    <img src="assets/img/offers-in-app/5G.png" alt="5G" class="icon-5g">
  </div>
  <div class="info-text">
    <div class="title">Stai attivando l'opzione 5G sulla tua SIM</div>
    <div class="sim-number font-weight-bold">{{ selectedSim?.msisdnId }}</div>
    <div class="cost">al costo mensile di {{ current5GAddOn?.canoneMese }} €</div>
  </div>
  <hr>
  <div class="additional-info">
    <p>L’opzione sarà attiva entro le prossime 24 ore.</p>
    <p>Riceverai il primo addebito del costo dell’opzione a decorrere dal prossimo rinnovo mensile di Giga, Minuti e SMS
      previsto per il</p>
    <p class="renewal-date font-weight-bold">{{ selectedSim?.dataProssimoRinnovo | date : 'dd/MM/yyyy' }}</p>
    <p>Fino a tale data, il servizio sarà completamente gratuito</p>
  </div>
  <hr>
  <button class="proceed-button" (click)="openModal()">PROCEDI</button>
</div>

<!--Confirmation of activation -->
<div class="confirmation-info" *ngIf="viewMode === ViewModeLayout.ACTIVATION_CONFIRMATION">
  <div class="icon-container">
    <img src="/assets/img/icons/ok.png" alt="OK" class="icon-ok">
  </div>
  <div class="info-text">
    <div class="title-conformation">La richiesta di disattivazione 5G per la SIM {{ selectedSim?.msisdnId }} è stata
      presa in carico
    </div>
  </div>
  <div class="additional-info">
    <p class="renewal-date">Il servizio resterà disponibile fino al
      <b>{{ selectedSim?.dataProssimoRinnovo | date : 'dd/MM/yyyy' }}</b></p>
  </div>
</div>

<!--Activation already in progress -->
<div class="confirmation-info" *ngIf="viewMode === ViewModeLayout.ACTIVATION_ALREADY_IN_PROGRESS">
  <div class="icon-container">
    <img src="/assets/img/icons/Alert.png" alt="Alert" class="icon-ok">
  </div>
  <div class="info-text">
    <div class="title-conformation">La richiesta di attivazione del 5G sulla SIM {{ selectedSim?.msisdnId }} è già in
      corso
    </div>
  </div>
  <div class="additional-info">
    <p class="already-activated-info">Il servizio sarà attivo entro le prossime 24 ore</p>
    <p class="already-activated-info">Per facilitare il corretto aggiornamento del servizio, ti consigliamo di attivare e
      disattivare la modalità aereo sul tuo dispositivo</p>
  </div>
</div>

<!-- Modal -->
<div class="modal-overlay" *ngIf="showModal" (click)="closeModal()">
  <div class="modal-content" [class.closing]="isClosing">
    <i class="fa fa-times" aria-hidden="true" (click)="closeModal()"></i>
    <div class="modal-body">
      <div class="modal-title">
        [TAG_il contenuto della sintesi contrattuale]
      </div>
      <div class="modal-text">
        Procedendo, dichiari di aver preso visione della sintesi contrattuale
      </div>
      <button class="activate-button" (click)="activateNow()">
        ATTIVA ORA
      </button>
    </div>
  </div>
</div>

<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
<app-error-window *ngIf="showGeneralErrorWindow" [onClose]="reloadPage"></app-error-window>
