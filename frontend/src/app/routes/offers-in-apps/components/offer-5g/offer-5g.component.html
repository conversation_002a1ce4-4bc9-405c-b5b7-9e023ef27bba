<div class="header">
  Aggiungi 5G
</div>

<!-- Selecting a SIM card -->
<div class="sim-selection" *ngIf="viewMode === 'selection'">
  <div class="title">Per procedere seleziona una delle tue SIM</div>
  <div class="sim-dropdown">
    <div class="label-sim">SIM selezionata</div>
    <select [(ngModel)]="selectedContract" class="sim-select">
      <option *ngFor="let sim of simList" [ngValue]="sim">{{ sim.msisdnId }}</option>
    </select>
  </div>
  <button class="proceed-button" (click)="selectSim()">PROCEDI</button>
</div>

<!-- Інформація про активацію (варіант 1) -->
<div class="activation-info" *ngIf="viewMode === 'activation1'">
  <div class="icon-container">
    <img src="assets/img/offers-in-app/5G.png" alt="5G" class="icon-5g">
  </div>
  <div class="info-text">
    <div class="title">Stai attivando l'opzione 5G sulla tua SIM</div>
    <div class="sim-number">{{ simNumber }}</div>
    <div class="cost">al costo mensile di {{ cost }} €</div>
  </div>
  <hr>
  <div class="additional-info">
    <p>A decorrere dal prossimo rinnovo mensile di Giga, Minuti e SMS previsto per il</p>
    <p class="renewal-date">{{ renewalDate }}</p>
    <p>riceverai l'attivazione dell'opzione e il relativo addebito</p>
  </div>
  <hr>
  <button class="proceed-button" (click)="confirmActivation()">PROCEDI</button>
</div>

<!-- Інформація про активацію (варіант 2) -->
<div class="activation-info" *ngIf="viewMode === 'activation2'">
  <div class="icon-container">
    <img src="assets/img/offers-in-app/5G.png" alt="5G" class="icon-5g">
  </div>
  <div class="info-text">
    <div class="title">Stai attivando l'opzione 5G sulla tua SIM</div>
    <div class="sim-number">{{ simNumber }}</div>
    <div class="cost">al costo mensile di {{ cost }} €</div>
  </div>
  <hr>
  <div class="additional-info">
    <p>L’opzione sarà attiva entro le prossime 24 ore.</p>
    <p>Riceverai il primo addebito del costo dell’opzione a decorrere dal prossimo rinnovo mensile di Giga, Minuti e SMS
      previsto per il</p>
    <p class="renewal-date">{{ renewalDate }}</p>
    <p>Fino a tale data, il servizio sarà completamente gratuito</p>
  </div>
  <hr>
  <button class="proceed-button" (click)="confirmActivation()">PROCEDI</button>
</div>

<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
