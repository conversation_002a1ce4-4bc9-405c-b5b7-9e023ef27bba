import { Component, OnInit } from '@angular/core';
import { OffersInAppService } from '../../service/offers-in-app.service';
import { Offer5GResponse, Sim, AddOn } from '../../models/Offer5GModels';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";


@Component({
  selector: 'app-offer-5g',
  templateUrl: './offer-5g.component.html',
  styleUrls: ['./offer-5g.component.scss']
})
export class Offer5gComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  // Режим відображення: 'selection', 'activation1', 'activation2'
  viewMode: string;

  // Дані для відображення
  selectedSim: Sim;
  // selectedContract: ContractRecord;
  simNumber: string = '[Tag_Numero SIM]';
  cost: string = '[TAG_Costo]';
  renewalDate: string = '[TAG_DataProssimoRinnovo]';

  // Список SIM-карт з API
  simList: Sim[] = [];
  offer5GData: Offer5GResponse;
  currentAddOn: AddOn;
  // contractRecords: ContractRecord[] = [];

  constructor(
    private readonly offerService: OffersInAppService,
    // private mobileService: MobileService
  ) { }

  ngOnInit() {
    this.loadOffer5GData('667269', '21');
  }

  // Завантаження контрактів користувача
 /* loadContractRecords() {
    const clientId = localStorage.getItem('clientId');

    if (clientId) {
      this.mobileService.loadContractRecords(clientId).subscribe(
        (contracts: ContractRecord[]) => {
          this.contractRecords = contracts;

          // Якщо є контракти
          if (contracts.length > 0) {
            // Якщо один контракт - одразу завантажуємо дані
            if (contracts.length === 1) {
              this.loadOffer5GData(clientId, contracts[0].id.toString());
            } else {
              // Якщо кілька контрактів - показуємо вибір контрактів
              this.viewMode = 'selection';
            }
          }
        },
        (error) => {
          console.error('Error loading contract records:', error);
        }
      );
    }
  }*/

  // Завантаження даних з API
  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe(
      (data: Offer5GResponse) => {
        this.offer5GData = data;
        this.simList = data.sim;

        // Логіка відображення: якщо один номер - одразу показуємо активацію
        if (this.simList.length === 1) {
          this.selectSim(this.simList[0]);
        } else if (this.simList.length > 1) {
          this.viewMode = 'selection';
        }
      },
      (error) => {
        console.error('Error loading 5G offer data:', error);
      }
    );
  }

  // Вибір SIM карти
  selectSim(sim: Sim) {
    this.selectedSim = sim;
    this.simNumber = sim.msisdnId;
    this.renewalDate = sim.dataProssimoRinnovo;

    // Знаходимо 5G AddOn
    this.currentAddOn = sim.addOnAviable.find(addon => addon.codice === '5G');

    if (this.currentAddOn) {
      this.cost = this.currentAddOn.canoneMese.toString();

      // Логіка вибору activation1 або activation2 на основі numRichiesteAttivazioni
      if (this.currentAddOn.numRichiesteAttivazioni === 0) {
        this.viewMode = 'activation2';
      } else {
        this.viewMode = 'activation1';
      }
    }
  }

  // Перехід до наступного кроку після вибору контракту
  selectSim() {
/*    if (this.selectedContract) {
      const clientId = localStorage.getItem('clientId');
      this.loadOffer5GData(clientId, this.selectedContract.id.toString());
    }*/
  }

  // Підтвердження активації
  confirmActivation() {
    // Логіка для підтвердження активації
    console.log('Activation confirmed for SIM:', this.simNumber);
    console.log('AddOn details:', this.currentAddOn);
    // Тут можна викликати сервіс для активації
  }
}
