import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-offer-5g',
  templateUrl: './offer-5g.component.html',
  styleUrls: ['./offer-5g.component.scss']
})
export class Offer5gComponent implements OnInit {
  // Режим відображення: 'selection', 'activation1', 'activation2'
  viewMode: string = 'activation2';

  // Дані для відображення
  selectedSim: string;
  simNumber: string = '[Tag_Numero SIM]';
  cost: string = '[TAG_Costo]';
  renewalDate: string = '[TAG_DataProssimoRinnovo]';

  // Список SIM-карт (приклад)
  simList = [
    { number: '3391970188' },
    { number: '3391970189' },
    { number: '3391970190' }
  ];

  constructor() { }

  ngOnInit() {
    // Тут можна отримати дані з сервісу
  }

  // Перехід до наступного кроку після вибору SIM
  proceed() {
    if (this.selectedSim) {
      this.simNumber = this.selectedSim;

      // Тут можна визначити, який варіант показати на основі даних
      // Наприклад, на основі відповіді від API
      const randomView = Math.random() > 0.5 ? 'activation1' : 'activation2';
      this.viewMode = randomView;
    }
  }

  // Підтвердження активації
  confirmActivation() {
    // Логіка для підтвердження активації
    console.log('Activation confirmed for SIM:', this.simNumber);
    // Тут можна викликати сервіс для активації
  }
}
