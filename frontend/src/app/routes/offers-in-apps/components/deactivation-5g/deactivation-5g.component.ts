import { Component, OnInit } from '@angular/core';
import { OffersInAppService } from '../../service/offers-in-app.service';
import { Offer5GResponse, Sim, AddOn } from '../../models/Offer5GModels';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";

@Component({
  selector: 'app-deactivation-5g',
  templateUrl: './deactivation-5g.component.html',
  styleUrls: ['./deactivation-5g.component.scss']
})
export class Deactivation5gComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  // Режими відображення: 'info', 'survey', 'confirmation', 'success'
  viewMode: string = 'info';

  // Дані для відображення
  selectedSim: Sim;
  simList: Sim[] = [];
  offer5GData: Offer5GResponse;

  // Дані опитування
  selectedReason: string = '';
  deactivationReasons = [
    { value: 'expensive', label: 'Troppo costoso' },
    { value: 'connectivity', label: 'Problemi di connettività' },
    { value: 'other', label: 'Altro' }
  ];

  constructor(
    private readonly offerService: OffersInAppService
  ) { }

  ngOnInit() {
    // this.loadOffer5GData('667269', '21');
  }

  // Завантаження даних з API
  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe(
      (data: Offer5GResponse) => {
        this.offer5GData = data;
        this.simList = data.sim;

        // Знаходимо SIM з активним 5G
        this.selectedSim = this.simList.find(sim =>
          sim.addOnActive && sim.addOnActive.some(addon => addon.codice === '5G')
        ) || this.simList[0];
      },
      (error) => {
        console.error('Error loading 5G offer data:', error);
      }
    );
  }

  // Перехід до опитування
  startDeactivation() {
    this.viewMode = 'survey';
  }

  // Вибір причини деактивації
  selectReason(reason: string) {
    this.selectedReason = reason;
  }

  // Перехід до підтвердження
  proceedToConfirmation() {
    if (this.selectedReason) {
      this.viewMode = 'confirmation';
    }
  }

  // Підтвердження деактивації
  confirmDeactivation() {
    // Логіка для деактивації
    // console.log('Deactivation confirmed for SIM:', this.selectedSim.msisdnId);
    console.log('Reason:', this.selectedReason);
    this.viewMode = 'success';
  }

  // Скасування
  cancel() {
    // Повернення до попереднього стану або закриття
    if (this.viewMode === 'survey') {
      this.viewMode = 'info';
    } else if (this.viewMode === 'confirmation') {
      this.viewMode = 'survey';
    }
  }

  // Getter для отримання активного 5G AddOn
  get active5GAddOn(): AddOn | undefined {
    return this.selectedSim.addOnActive.find(addon => addon.codice === '5G');
  }

  // Перевірка чи кнопка PROCEDI активна
  get isProceedEnabled(): boolean {
    return this.selectedReason !== '';
  }
}
