import { Component, OnInit } from '@angular/core';
import { OffersInAppService } from '../../service/offers-in-app.service';
import { Offer5GResponse, Sim, AddOn } from '../../models/Offer5GModels';
import {select} from "@angular-redux/store";
import {Observable} from "rxjs/Observable";

enum DeactivationViewMode {
  INFO = 'info',
  SURVEY = 'survey',
  CONFIRMATION = 'confirmation',
  SUCCESS = 'success'
}

@Component({
  selector: 'app-deactivation-5g',
  templateUrl: './deactivation-5g.component.html',
  styleUrls: ['./deactivation-5g.component.scss']
})
export class Deactivation5gComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  viewMode: DeactivationViewMode = DeactivationViewMode.INFO;
  ViewModeLayout = DeactivationViewMode;

  selectedSim: Sim;
  simList: Sim[] = [];
  offer5GData: Offer5GResponse;

  // Дані опитування
  selectedReason: string = '';
  deactivationReasons = [
    { value: 'expensive', label: 'Troppo costoso' },
    { value: 'connectivity', label: 'Problemi di connettività' },
    { value: 'other', label: 'Altro' }
  ];

  constructor(
    private readonly offerService: OffersInAppService
  ) { }

  ngOnInit() {
    // this.loadOffer5GData('667269', '21');
  }

  // Завантаження даних з API
  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe(
      (data: Offer5GResponse) => {
        this.offer5GData = data;
        this.simList = data.sim;

        // Знаходимо SIM з активним 5G
        this.selectedSim = this.simList.find(sim =>
          sim.addOnActive && sim.addOnActive.some(addon => addon.codice === '5G')
        ) || this.simList[0];
      },
      (error) => {
        console.error('Error loading 5G offer data:', error);
      }
    );
  }

  // Перехід до опитування
  startDeactivation() {
    this.viewMode = DeactivationViewMode.SURVEY;
  }

  // Вибір причини деактивації
  selectReason(reason: string) {
    this.selectedReason = reason;
  }

  // Перехід до підтвердження
  proceedToConfirmation() {
    if (this.selectedReason) {
      this.viewMode = DeactivationViewMode.CONFIRMATION;
    }
  }

  // Підтвердження деактивації
  confirmDeactivation() {
    console.log('Reason:', this.selectedReason);
    this.viewMode = DeactivationViewMode.SUCCESS;
  }

  // Скасування
  cancel() {
    // Повернення до попереднього стану або закриття
    if (this.viewMode === DeactivationViewMode.SURVEY) {
      this.viewMode = DeactivationViewMode.INFO;
    } else if (this.viewMode === DeactivationViewMode.CONFIRMATION) {
      this.viewMode = DeactivationViewMode.SURVEY;
    }
  }

  // Getter для отримання активного 5G AddOn
  get active5GAddOn(): AddOn | undefined {
    return this.selectedSim.addOnActive.find(addon => addon.codice === '5G');
  }

  // Перевірка чи кнопка PROCEDI активна
  get isProceedEnabled(): boolean {
    return this.selectedReason !== '';
  }

  // Getter для заголовка сторінки
  get pageTitle(): string {
    switch (this.viewMode) {
      case DeactivationViewMode.INFO:
        return '5G';
      case DeactivationViewMode.SURVEY:
      case DeactivationViewMode.CONFIRMATION:
        return 'Disattiva 5G';
      case DeactivationViewMode.SUCCESS:
        return 'Aggiungi 5G';
      default:
        return '5G';
    }
  }
}
