import { Component, OnInit, ViewChild } from '@angular/core';
import * as ASSICURAZIONI from '../../questions/answer-questions-json/Servizi_Assicurazioni.json';
import * as DEVICE from '../../questions/answer-questions-json/Servizi_Device.json';
import * as EE from '../../questions/answer-questions-json/Servizi_EE.json';
import * as GAS from '../../questions/answer-questions-json/Servizi_Gas.json';
import * as CONTO_RELAX from '../../questions/answer-questions-json/Servizi_Mobile.json';
import * as PI from '../../questions/answer-questions-json/Servizi_TuttoInUno.json';
import * as VOCE_ADSL from '../../questions/answer-questions-json/Servizi_VoceADSL.json';
import * as FATTURA from '../../questions/answer-questions-json/Info_Fattura.json';
import * as INFO_GENERICHE from '../../questions/answer-questions-json/Info_Altro.json';
import * as FAQ from '../../questions/answer-questions-json/Faq.json';
import * as AMAZON_PRIME from '../../questions/answer-questions-json/Servizi_Amazon_Prime.json';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';

@Component({
  selector: 'app-answers-questions-result',
  templateUrl: './answers-questions-result.component.html',
  styleUrls: ['./answers-questions-result.component.scss']
})
export class AnswersQuestionsResultComponent implements OnInit {
  @ViewChild('myTable') myTable: any;
  settingActive = 0;
  datatable1_rows = new Array<any>();
  searchValue: any;
  services: Array<any> = [
    { name: 'ASSICURAZIONI', activeNumber: 0 },
    { name: 'DEVICE', activeNumber: 1 },
    { name: 'EE', activeNumber: 2 },
    { name: 'GAS', activeNumber: 3 },
    { name: 'CONTO RELAX', activeNumber: 4 },
    { name: 'PI', activeNumber: 5 },
    { name: 'INTERNET', activeNumber: 6 },
    { name: 'FATTURA', activeNumber: 7 },
    { name: 'INFO GENERICHE', activeNumber: 8 },
    { name: 'FAQ', activeNumber: 9 },
    { name: 'AMAZON PRIME', activeNumber: 10 }

  ];

  routesData: object = {
    assicurazioni: 0, device: 1, ee: 2, gas: 3, 'conto-relax': 4,
    pi: 5, 'voce-adsl': 6, fattura: 7, 'info-generice': 8, faq: 9, amazonprime: 10
  };
  data: Array<any> = [ASSICURAZIONI, DEVICE, EE, GAS, CONTO_RELAX, PI, VOCE_ADSL, FATTURA, INFO_GENERICHE, FAQ, AMAZON_PRIME];
  categories: Array<Array<String>> = [];
  selected: Array<String> = [];

  constructor(private route: ActivatedRoute) {
    this.data.forEach(data => {
      const array = [];
      data.forEach(row => {
        if (row['FIELD2']) {
          if (array.indexOf(row['FIELD2']) === -1) {
            array.push(row['FIELD2']);
          }
        }
      });
      this.selected.push("");
      this.categories.push(array);
    });
  }



  ngOnInit(): void {
    this.settingActive = this.routesData[(<BehaviorSubject<any>>this.route.parent.firstChild.url).value[0].path];
    const test = this.data[this.settingActive];
    if (this.settingActive === 1 || this.settingActive === 10 ) {
      const result = test.filter(value => !this.selected[this.settingActive] || value['FIELD2'] === this.selected[this.settingActive]);
      this.datatable1_rows = result;
    }
  }


  toggleExpandRow(row) {
    this.myTable.rowDetail.toggleExpandRow(row);
  }

  filterData() {
    let result = this.data[this.settingActive].filter(value => !this.selected[this.settingActive] || value['FIELD2'] === this.selected[this.settingActive]);
    if (this.searchValue) {
      const lowerCaseSearchValue = this.searchValue.toLowerCase();
      result = result.filter(value =>
        value['FIELD5'] && value['FIELD5'].toLowerCase().includes(lowerCaseSearchValue) ||
        value['FIELD6'] && value['FIELD6'].toLowerCase().includes(lowerCaseSearchValue)
      );
    }
    this.datatable1_rows = result;
  }

  onDetailToggle(event) {
    if (event.srcElement.classList.contains('question')) {
      event.srcElement.parentElement.classList.contains('show') ?
        event.srcElement.parentElement.classList.remove('show') :
        event.srcElement.parentElement.classList.add('show');
    } else {
      event.srcElement.parentElement.parentElement.classList.contains('show') ?
        event.srcElement.parentElement.parentElement.classList.remove('show') :
        event.srcElement.parentElement.parentElement.classList.add('show');
    }
  }
}
