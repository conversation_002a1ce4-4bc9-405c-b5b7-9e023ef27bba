@import "~app/shared/styles/colors";
@import "~app/shared/styles/app-menu";

.container-fluid {
  font-family: 'Lato-Regular';
  margin-top: 7%;
  width: 100%;
}

@media only screen and (max-width: 1600px) {
  .container-fluid {
    font-size: 12px;
  }
}

@media only screen and (max-width: 1200px) {
  .container-fluid {
    font-size: 10px;
  }
}

@media only screen and (max-width: 800px) {
  .container-fluid {
    width: 100%;
    float: left;
    padding: 0;
  }
}

@media only screen and (max-width: 500px) {
  .container-fluid {
  }
}

@media only screen and (max-width: 1024px) {
  .container-fluid {
    margin-top: 8%;
  }
}

.sidebar-container {
  width: 20%;
}

.main {
  border: 1px solid $menu-border;
  border-radius: 5px;
  background-color: $menu-background;
  margin-bottom: 20px;
}

.button-group {
  padding: 10px;
}

.service-title {
  background-color: white;
  border-radius: 5px 5px 0 0;
  border-bottom: 1px solid $menu-border;
  padding: 10px 5px;
  text-align: center;

  .servizio {
    font-size: 18px;
    font-weight: 600;
    color: #36749d;
  }
}

.text {
  padding-left: 25px;
  display: inline-block;
}

.priceBlock {
  border-left: 1px solid #b6cce3;
  margin-left: 12px;
  width: 39%;
}

.service {
  text-align: center;
  width: 100%;
  color: #36749d;
  font-size: 10px;
  margin-top: 11px;
  line-height: 10px;
}

.price {
  text-align: center;
  width: 100%;
  font-weight: bold;
  font-size: 15px;
  line-height: 15px;
  color: #2d2d2d;
}

.col-md-5 {
  padding: 0;
}

@media only screen and (max-width: 1600px) {

  .service {
    font-size: 8.5px;
    margin-top: 13px;
    line-height: 8px;
  }

  .link {
    font-size: 16px;
    padding: 5px 20px 5px 5px;
  }

  .text {
    padding-left: 10px;
  }

  .button {
    color: #36749d;
    padding: 4px 7px;
    border: 1px solid #36749d;
    border-radius: 5px;
    position: absolute;
    right: 30px;
    font-size: 11px;
  }


}

@media only screen and (max-width: 1400px) {
  .priceBlock {
    margin-left: 5px;
    width: 40%;
  }
  .service {
    font-size: 8.5px;
    margin-top: 13px;
    line-height: 8px;
  }
  ul > li > .link {
    padding: 5px;
  }
  .price {
    font-size: 12.5px;
    line-height: 10px;
  }
}

@media only screen and (max-width: 1100px) {
  .matryoshka {

    margin: 0 7px;
  }
  .priceBlock {
    width: 39%;
    margin-left: 0;
  }
  .service {
    font-size: 8px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }
  .text {
    padding-left: 10px;
  }
  .button {

    padding: 4px 5px;
    right: 24px;
    font-size: 9px;
  }
}

@media only screen and (max-width: 991px) {
  .priceBlock {
    border-left: none;
    margin-left: 5px;
    width: 100%;
  }
  .link {
    height: 41px;
  }
  .service {
    font-size: 10px;
    margin-top: 13px;
    line-height: 8px;
  }

  .price {
    font-size: 14px;
    line-height: 10px;
  }

  .text {
    padding-left: 30px;
  }

  .arrow {
    padding-right: 0;
    margin-top: 0;
    transform: rotate(90deg);
  }
  .button {

    padding: 2px 2px;
    right: 24px;
    font-size: 8.5px;
  }
}

@media only screen and (max-width: 767px) {
  .matryoshka {
    float: left;
    margin: 0 20px;
    padding: 0 25px 0 0;
  }
  .priceBlock {
    border-left: 1px solid #b6cce3;
    float: left;
    width: 39%;

  }
  .service {
    font-size: 14px;
    line-height: 10px;
  }

  .price {
    font-size: 19px;
    line-height: 13px;
  }

  .text {
    padding-left: 30px;
  }
  .button {
    padding: 5px 5px;
    right: 30px;
    font-size: 11px;
  }
}

@media only screen and (max-width: 400px) {
  .matryoshka {
    height: 50px;
    float: left;
    margin: 0 7px;
    width: 90%;
  }
  .priceBlock {
    border-left: none;
    float: left;
    width: 45%;

  }
  .service {
    font-size: 9px;
    line-height: 8px;
  }

  .price {
    font-size: 12px;
    line-height: 10px;
  }

}

.addition {
  color: #a2a2a2;
  padding-right: 100px;
  line-height: 16px;
}

.show {
  .menuBlock {
    display: block;
  }
}

.menuBlock {
  display: none;
  background: white;
  border-bottom: 1px solid #b6cce3;
}

.view {
  background-color: #f0f5f9;

  .detail {
    display: block;
  }
}

.detail {
  padding: 15px 7px;
  display: none;
  background: #f0f5f9;
  border-top: 1px solid #b6cce3;
}

.mainText {
  display: inline-block;
  font-size: 14px;
  color: #000;
  line-height: 10px;
  padding: 10px;
  width: 100%;
}

.nascondi {
  background: white;
}

.icon {
  float: left;
  width: 65px;
  height: 65px;
  margin: 10px 15px 0 auto;
}

.autolettura {
  background: url("../../../../assets/img/optimaIcons/autolettura_color.png") no-repeat center;
  background-size: contain;
}

.modifica {
  background: url("../../../../assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

.icons {
  margin-left: 15px;
}

.url {
  line-height: 18px;
  margin-top: 15px;

  a {
    padding: 8px 0;
    color: #000;
  }

  a:hover {
    text-decoration: underline;
  }
}
