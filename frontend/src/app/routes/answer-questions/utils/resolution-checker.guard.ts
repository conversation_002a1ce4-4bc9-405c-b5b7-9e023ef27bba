import {Injectable} from '@angular/core';
import {CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {ConstantUtil} from '../../../utils/ConstantUtil';

@Injectable()
export class ResolutionCheckerGuard implements CanActivate {
  constructor(private router: Router) {
  }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    return ConstantUtil.isMobile || (!this.router.navigate(['/support/questions/assicurazioni']));
  }
}
