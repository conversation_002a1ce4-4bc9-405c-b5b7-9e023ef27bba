import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MenuService } from '../core/menu/menu.service';
import { SharedModule } from '../shared/shared.module';
import { PagesModule } from './pages/pages.module';

import { menu } from './top_menu';
import { sideMenu } from './side_menu';
import { menuWithSubMenu } from './navigation';
import { serviceMenu } from './services';
import { routes } from './routes';

import { AuthGuard } from '../services/auth/auth-guard.service';

@NgModule({
  imports: [
    SharedModule,
    RouterModule.forRoot(routes),
    PagesModule
  ],
  declarations: [],
  exports: [
    RouterModule
  ],
  providers: [
    AuthGuard
  ]
})

export class RoutesModule {
  constructor(public menuService: MenuService) {
    menuService.addMenu(menu);
    menuService.addSideMenu(sideMenu);
    menuService.addMenuWithSubMenu(menuWithSubMenu);
    menuService.addServiceMenu(serviceMenu);
  }
}
