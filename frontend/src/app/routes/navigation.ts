import {FaiDaTeLayoutRoutes, ProfileLayoutRoutes} from '../configuration/routes';
import {ConstantUtil} from '../utils/ConstantUtil';


const Profile = {
  text: 'PROFILO CLIENTE',
  // link: ConstantUtil.isMobile ? '/profile' : ProfileLayoutRoutes.USER_DATA,
  link: '/profile',
  submenu: [
    {
      text: 'I tuoi dati',
      link: ProfileLayoutRoutes.USER_DATA
    },
    {
      text: 'I tuoi contratti',
      link: ProfileLayoutRoutes.YOUR_CONTRACTS
    },
    {
      text: 'Il tuo tutto-in-uno',
      link: ProfileLayoutRoutes.ALL_IN_ONE
    },
    {
      text: 'Le tue spedizioni',
      link: ProfileLayoutRoutes.SHIPMENTS
    },
    {
      text: 'Le tue comunicazioni',
      link: ProfileLayoutRoutes.COMMUNICATION
    }
  ]
};

const Invoices = {
  text: 'FATTURE/PAGAMENTI',
  link: '/invoices/all',
};
const Support = {
  text: 'DOMANDE E RISPOSTE',
  // link: ConstantUtil.isMobile ? '/support/questions' : '/support/questions/assicurazioni',
  link: '/support/questions',
};
const Services = {
    text: 'FAI DA TE',
    // link: ConstantUtil.isMobile ? '/faidate' : FaiDaTeLayoutRoutes.YOUR_SERVICES,
    link: '/faidate',
    submenu: [
      {
        text: 'I TUOI SERVIZI',
        link: FaiDaTeLayoutRoutes.YOUR_SERVICES,
        order: 10
      },
      {
        text: 'LE TUE SEGNALAZIONI',
        link: FaiDaTeLayoutRoutes.YOUR_REPORTS,
        order: 20
      },
      {
        text: 'AUTOLETTURA',
        link: FaiDaTeLayoutRoutes.AUTO_READING,
        order: 30
      }
    ]
  }
;

const Moduli = {
  text: 'MODULI',
  link: '/moduli'
};

const Assistant = {
  text: 'ASSISTENZA',
  link: '/assistant'
};


export const menuWithSubMenu = [
  Profile,
  Invoices,
  Services,
  Support,
  Moduli,
  Assistant
];
