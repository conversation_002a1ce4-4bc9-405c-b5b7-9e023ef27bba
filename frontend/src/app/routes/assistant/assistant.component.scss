@import "~app/shared/styles/colors";

.assistant-layout {
  margin-top: 5%;

  .assistant-header {
    color: $dark-blue;
    font-weight: bold;
    font-size: 22px;
    padding-left: 0;
    margin-top: 2%;
  }

  .appeal-block {
    margin-top: 2%;

    .appeal-title {
      color: $dark-blue;
      margin-top: 2%;
      margin-right: 1%;

      .appeal-client {
        font-weight: bold;
      }
    }

    .appeal-image {
      min-height: 250px;
      position: relative;
      padding: 0;
    }

    .optima-info-block {
      margin-top: 2%;
      margin-bottom: 2%;
    }

    .optima-info-block {
      display: flex;
      align-items: center;
    }

    .ask-alan-block {

      .alan-label {
        text-align: left;

        .info-description {
          color: $dark-blue;
        }
      }
    }

    .info-block {
      display: flex;
      flex-wrap: wrap;

      .info-label {
        margin-bottom: 0.2%;
        margin-right: 5px;
        padding: 5px 0;
        cursor: pointer;
        float: left;
        width: 30%;
        -webkit-box-flex: 1;
        -ms-flex: 1 0 21%;
        /* flex: 1 0 21%; */


        div {
          margin-left: 5%;
          float: left;
        }

        //&:last-child {
        //  margin-bottom: 0;
        //}

        .info-title, .info-description {
          width: 80%;
        }
      }
    }

    .info-title {
      color: $dark-blue;
      font-weight: bold;
      font-size: 18px;
    }

  }

  .bordered {
    border: 1px solid $light-blue;
    border-radius: 5px;
  }

  .dark-border {
    border: 1px solid #36749C;
    border-radius: 5px;
  }
  .assistant-footer {
    text-align: left;
    margin-top: 2%;
    color: $dark-blue;

    button {
      color: inherit;
      margin-right: 3%;
      border: none;
      text-decoration: underline;
      background-color: transparent;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.alan-image {
  background: url("../../../assets/img/assistant/alan.png") no-repeat center;
  background-size: contain;
  height: 225px;
  position: absolute;
  bottom: 0;
  width: 350px;

}

.alan-ellen-image {
  background: url("../../../assets/img/agents/esperto_assistenza.png") no-repeat left;
  background-size: contain;
  height: 225px;
  position: absolute;
  bottom: 0;
  width: 750px;

}

.app-telegram {
  width: 8%;
  margin-left: 5%;
  height: 45px;
  background: url("../../../assets/img/optimaIcons/telegram.png") no-repeat center;
  background-size: contain;
}

.app-google-assistant {
  width: 8%;
  margin-left: 5%;
  height: 45px;
  background: url("../../../assets/img/optima/Set_Icone_AreaClienti_Google.png") no-repeat center;
  background-size: contain;
}

.app-facebook {
  width: 8%;
  margin-left: 5%;
  height: 45px;
  background: url("../../../assets/img/optimaIcons/facebook.png") no-repeat center;
  background-size: contain;
}
.app-amazon-alexa {
  width: 10%;
  margin-left: 5%;
  height: 45px;
  background: url("../../../assets/img/optima/alexa_pulsante_assistenza.png") no-repeat center;
  background-size: contain;
}

.colibri-icon {
  width: 8%;
  margin-left: 5%;
  height: 45px;
  background: url("../../../assets/img/optima/Set_Icone_AreaClienti_Colibri.png") no-repeat center;
  background-size: contain;
}

@media screen and(min-width: 992px) and(max-width: 1400px) {
  .alan-label{
    margin-left: 50px;
  }
}


@media screen and(max-width: 991px) {
  .assistant-layout {
    .assistant-header {
      text-align: center;
      margin-top: 2%;
    }

    .appeal-block {
      border: none;

      .appeal-title {
        text-align: center;
      }
    }

    .appeal-block {
      .alan-image {
        position: relative;
        width: auto;
        margin-top: 2%;
      }

      .alan-ellen-image {
        position: relative;
        width: auto;
        margin-top: 2%;
      }

      .optima-info-block {
        display: block;

        .alan-label {
          border: none;
        }

        .info-label {
          border: none;
          text-align: center;
          margin-bottom: 7%;

          .app-icon {
            width: 18%;
          }

          div {
            float: none;
            margin: auto;
          }
        }
      }

      .optima-info-block {
        padding: 0;
      }

      .alan-label {
        margin-bottom: 2%;
      }
    }
  }
}

@media screen and(max-width: 550px) {
  .assistant-layout {
    .assistant-header {
      margin-top: 3%;
    }
  }
  .info-block {
    .info-description {
      font-size: 10px;
      display: contents
    }
  }
}

@media screen and(max-width: 460px) {
  .assistant-layout {
    .assistant-header {
      margin-top: 5%;
    }
  }
  .info-block {
    .info-description {
      font-size: 9px;
      display: contents;
    }
  }
}

@media screen and(max-width: 350px) {
  .assistant-layout {
    .assistant-header {
      margin-top: 8%;
    }
  }
}
