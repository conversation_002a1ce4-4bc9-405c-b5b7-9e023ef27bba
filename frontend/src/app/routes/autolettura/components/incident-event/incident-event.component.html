<div class="row app--autolettura">
  <div class="col-lg-12 col-md-12">
    <div class="col-lg-12 col-md-12 page-title">
      <div class="title-image"></div>
      <div class="title-text">AUTOLETTURA</div>
    </div>
  </div>
  <div class="col-md-6 autoletura-card" id="luce">
    <div class="panel app--panel-block">
      <div class="panel-heading">
        Autolettura LUCE
      </div>
      <div class="panel-body app--panel-body">
        <div *ngIf="!isHaveEEService">Questo servizio non è disponibile.</div>
        <div [formGroup]="eeFormGroup" *ngIf="isHaveEEService">
          <div class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <select class="app-select" formControlName="pdr" (change)="checkIsPDR2G()">
                <option value=""></option>
                <option *ngFor="let item of luceContatoreList" [attr.value]="item.utNumber">
                  {{item.utNumber}}
                </option>
              </select>
              <span class="input-group-addon app--input-group-addon-hide">
                                        <em class="fa fa-info-circle"></em>
                                    </span>
            </div>
            <span class="text-danger"
                  *ngIf="eeFormGroup.get('pdr').hasError('required') && eeFormGroup.get('pdr').touched">
                                    Campo obbligatorio.
                                </span>
          </div>
          <div class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <label>Quale tipo di contatore possiedi?</label>
              <select class="app-select contract-type-select"
                      formControlName="contatoreType">
                <option value=""></option>
                <option *ngFor="let item of luceContatoreTypeList"
                        [attr.value]="item.value">
                  {{item.title}}
                </option>
              </select>
              <span class="input-group-addon autolettura-info">
                                    <span #lucePopup placement="left" triggers="mouseenter:mouseleave"
                                          [popover]="lucePopTemplate">
                                        <em class="info-circle">i</em>
                                    </span>
                                    </span>
            </div>
            <span class="text-danger"
                  *ngIf="eeFormGroup.get('contatoreType').hasError('required') && eeFormGroup.get('contatoreType').touched">
                                    Campo obbligatorio.
                                </span>
          </div>
          <div class="form-group app--form-group app--form-group-popup"
               *ngFor="let item of getConsumptions(eeFormGroup); let i= index">
            <div
              class="input-group {{eeFormGroup.get('contatoreType').value==='Contatore meccanico'&&i>0&&'d-none'}}">
              <label>Lettura F{{i + 1}}</label>
              <app-input style="width: 100%" name="consumption" [formGroup]="item" [required]="false"
                         type="number"></app-input>
              <span class="input-group-addon app--input-group-addon-hide">
                                        <em class="fa fa-info-circle"></em>
                                    </span>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-footer app--panel-footer">
        <button [disabled]="!isHaveEEService || eeFormGroup.invalid
                || eeFormGroup.pristine
                || eeIndexesOfFilesWithWrongSize.length
                || eeIndexesOfFilesWithWrongExtension.length" class="app--btn-inserisci" (click)="onSubmitEE()">
          <span> INSERISCI AUTOLETTURA </span>
        </button>
        <div class="form-group app--form-group app--form-group-popup" *ngIf="isHaveEEService">
          <div class="title-image-upload">
            <label for="ee-image-upload" class="ee-image-upload">
              <img class="upload-image" src="../../../../../assets/img/autolettura/fai_una_foto.png"/>
            </label>
            <input type="file"
                   id="ee-image-upload"
                   (change)="onEEImageChange($event)"
                   accept="application/pdf,image/jpeg,image/png"
                   #eeImagesInput>

            <div *ngFor="let file of eeFilesBeforeValidation; let i = index">
            <div class="file-name" *ngIf="!eeIndexesOfFilesWithWrongSize.includes(i)
                      && !eeIndexesOfFilesWithWrongExtension.includes(i)">
              {{eeFilesBeforeValidation[i].name}}
            </div>
              <div *ngIf="eeIndexesOfFilesWithWrongSize.includes(i)" class="file-wrong">
                La dimensione del file deve essere inferiore a 5 MB
              </div>
              <div *ngIf="eeIndexesOfFilesWithWrongExtension.includes(i)" class="file-wrong">
                L’estensione del file deve essere PDF, PNG o JPG
              </div>
              <button *ngIf="!eeIndexesOfFilesWithWrongSize.includes(i)
                             && !eeIndexesOfFilesWithWrongExtension.includes(i)" class="remove-file-button"
                      (click)="removeImageFromEEForm(i, eeFilesAfterValidation.indexOf(file))">🞫
              </button>
              <button *ngIf="eeIndexesOfFilesWithWrongSize.includes(i)
                        || eeIndexesOfFilesWithWrongExtension.includes(i)"
                      class="remove-file-button" (click)="resetWarningInEEForm(i)">🞫
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 autoletura-card" id="gas">
    <div class="panel app--panel-block">
      <div class="panel-heading">
        <div class="app--panel-title">Autolettura GAS</div>
      </div>
      <div class="panel-body app--panel-body">
        <div *ngIf="!isHaveGASService">Questo servizio non è disponibile.</div>
        <div [formGroup]="gasFormGroup" *ngIf="isHaveGASService">
          <div class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <select class="app-select" formControlName="pdr">
                <option value=""></option>
                <option *ngFor="let item of gasContatoreList" [attr.value]="item.utNumber">
                  {{item.utNumber}}
                </option>
              </select>
              <span class="input-group-addon app--input-group-addon-hide">
                                        <em class="fa fa-info-circle"></em>
                                    </span>
            </div>
            <span class="text-danger"
                  *ngIf="gasFormGroup.get('pdr').hasError('required') && gasFormGroup.get('pdr').touched">
                                    Campo obbligatorio.
                                </span>
          </div>
          <div class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <label>Quale tipo di contatore possiedi?</label>
              <select class="app-select contract-type-select"
                      formControlName="contatoreType">
                <option value=""></option>
                <option *ngFor="let item of gasContatoreTypeList" [attr.value]="item.value">
                  {{item.title}}
                </option>
              </select>
              <span class="input-group-addon autolettura-info">
                                    <span #gasPopup placement="left"
                                          triggers="mouseenter:mouseleave" [popover]="gasPopTemplate">
                                        <em class="info-circle">i</em>
                                    </span>
                                  </span>
            </div>
            <span class="text-danger"
                  *ngIf="gasFormGroup.get('contatoreType').hasError('required') && gasFormGroup.get('contatoreType').touched">
                                    Campo obbligatorio.
                                </span>
          </div>
          <div class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <label>Lettura contatore</label>
              <app-input name="letturaContatore" [formGroup]="gasFormGroup.controls['gas']" [required]="false"
                         type="number">
              </app-input>
              <span class="input-group-addon app--input-group-addon-hide">
                                        <em class="fa fa-info-circle"></em>
                                    </span>
            </div>
          </div>
          <div *ngIf="gasFormGroup.controls['contatoreType'].value !== 'Contatore con correttore NO'"
               class="form-group app--form-group app--form-group-popup">
            <div class="input-group">
              <label>Lettura correttore</label>
              <app-input name="letturaCorrettore" [formGroup]="gasFormGroup.controls['gas']" [required]="false"
                         type="number">
              </app-input>
              <span class="input-group-addon app--input-group-addon-hide">
                                        <em class="fa fa-info-circle"></em>
                                    </span>
            </div>
          </div>

        </div>
      </div>
      <div class="panel-footer app--panel-footer">
        <button [disabled]="!isHaveGASService || gasFormGroup.invalid
                || gasFormGroup.pristine
                || gasIndexesOfFilesWithWrongSize.length
                || gasIndexesOfFilesWithWrongExtension.length" class="app--btn-inserisci" (click)="onSubmitGAS()">
          <span> INSERISCI AUTOLETTURA </span>
        </button>
        <div class="form-group app--form-group app--form-group-popup" *ngIf="isHaveGASService">
          <div class="title-image-upload">
            <label for="gas-image-upload" class="gas-image-upload">
              <img class="upload-image" src="../../../../../assets/img/autolettura/fai_una_foto.png"/>
            </label>
            <input type="file"
                   id="gas-image-upload"
                   (change)="onGASImageChange($event)"
                   accept="application/pdf,image/jpeg,image/png"
                   #gasImagesInput>

            <div *ngFor="let file of gasFilesBeforeValidation; let i = index">
              <div class="file-name" *ngIf="!gasIndexesOfFilesWithWrongSize.includes(i)
                      && !gasIndexesOfFilesWithWrongExtension.includes(i)">
                {{gasFilesBeforeValidation[i].name}}
              </div>
              <div *ngIf="gasIndexesOfFilesWithWrongSize.includes(i)" class="file-wrong">
                La dimensione del file deve essere inferiore a 5 MB
              </div>
              <div *ngIf="gasIndexesOfFilesWithWrongExtension.includes(i)" class="file-wrong">
                L’estensione del file deve essere PDF, PNG o JPG
              </div>
              <button *ngIf="!gasIndexesOfFilesWithWrongSize.includes(i)
                             && !gasIndexesOfFilesWithWrongExtension.includes(i)" class="remove-file-button"
                      (click)="removeImageFromGASForm(i, gasFilesAfterValidation.indexOf(file))">🞫
              </button>
              <button *ngIf="gasIndexesOfFilesWithWrongSize.includes(i)
                        || gasIndexesOfFilesWithWrongExtension.includes(i)"
                      class="remove-file-button" (click)="resetWarningInGASForm(i)">🞫
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #lucePopTemplate>
  <ng-container *ngIf="selectedLuceContatoreType == null || selectedLuceContatoreType == undefine">
    il valore non è selezionato!!!
  </ng-container>
  <ng-container *ngIf="selectedLuceContatoreType =='Contatore elettronico'">
    <div class="app--popup-contatore-type">
      <div>
        <div class="app--popup-icon app--icon app--icon-aut1"></div>
        <div class="app--popup-title">
          <h4>Contatore luce elettronico</h4>
          <span>Il consumo è visualizzabile per fasce orarie premendo l’apposito pulsante.</span>
        </div>
      </div>
      <div class="app--popup-description">
        <p>Premendo il pulsante appaiono in ordine: ID Cliente, poi la fascia oraria in atto, la potenza istantanea
          e la lettura dei totalizzatori di energia e potenza relativi per ogni fascia oraria.</p>
        <p>Premere in sequenza il pulsante del contatore per cinque volte, ﬁnché compareranno sul display i consumi
          e la potenza.</p>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="selectedLuceContatoreType =='Contatore meccanico'">
    <div class="app--popup-contatore-type">
      <div>
        <div class="app--popup-icon app--icon app--icon-aut2"></div>
        <div class="app--popup-title">
          <h4>Contatore luce meccanico</h4>
          La lettura compare in modo automatico sul display trasparente.
        </div>
      </div>
      <div class="app--popup-description">
        Il valore da registrare è esclusivamente il numero segnato su fondo nero. I decimali su fondo rosso non devono
        essere comunicati.
      </div>
    </div>
  </ng-container>
</ng-template>


<ng-template #gasPopTemplate>
  <ng-container *ngIf="selectedGasContatoreType == null || selectedGasContatoreType == undefine">
    il valore non è selezionato!!!
  </ng-container>
  <ng-container *ngIf="selectedGasContatoreType =='Contatore con correttore NO'">
    <div class="app--popup-contatore-type">
      <div>
        <div class="app--popup-icon app--icon app--icon-contatoreGas"></div>
        <div class="app--popup-title">
          <h4>Contatore gas senza correttore</h4>
          <span>Il contatore rivela il numero di metri cubi consumati dall’utente.</span>
        </div>
      </div>
      <div class="app--popup-description">
        <p>Il contatore misura il volume di gas che passa per lo stesso apparecchio, comunicando il numero di metri
          cubi consumati dall’utente.</p>
        <p>Il valore da registrare è esclusivamente il numero segnato su fondo nero. I decimali su fondo rosso non
          devono
          essere comunicati.</p>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="selectedGasContatoreType =='Contatore con correttore SI'">
    <div class="app--popup-contatore-type">
      <div>
        <div class="app--popup-icon app--icon app--icon-contatoreGasDigit"></div>
        <div class="app--popup-title">
          <h4>Contatore gas con correttore</h4>
        </div>
      </div>
      <div class="app--popup-description">
        Il correttore volumetrico converte i consumi in metro cubo standard.
      </div>
    </div>
  </ng-container>
</ng-template>
<div class="modal-div show" *ngIf="isDisplayModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="closeModalWindow()"></i>
    <img class="modal-image" src="/assets/img/icons/ok.png" alt="Ok">
    <div class="modal-text">
      Grazie alla tecnologia 2G del tuo contatore, i consumi vengono aggiornati automaticamente dal Distributore,
      pertanto non è necessario inserire l’autolettura.
    </div>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
