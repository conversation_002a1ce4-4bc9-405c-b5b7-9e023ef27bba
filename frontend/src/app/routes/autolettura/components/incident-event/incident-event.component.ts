import {After<PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, Element<PERSON>ef, Inject, OnInit, ViewChild} from '@angular/core';
import {Form<PERSON>rray, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {AutoLetturaForm} from '../../model/AutoLetturaForm';
import {IncidentEventResponse} from '../../model/IncidentEventResponse';
import {FormUtils} from '../../utils/FormUtils';
import Validator from '../../../../common/utils/Validator';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material';
import {messages} from '../../config/config';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ActivatedRoute} from '@angular/router';
import {AttachmentService} from '../../../fai-da-te/services/attachment/attachment.service';
import {EnergiaService} from '../../../../common/services/energia/energia-service.service';
import {InfoPod2G} from '../../../../common/model/services/PodDetail';

@Component({
  selector: 'app-incident-event',
  templateUrl: './incident-event.component.html',
  styleUrls: ['./incident-event.component.scss']
})

export class IncidentEventComponent implements OnInit, AfterViewChecked {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  selectedLuceContatoreType: any;
  selectedGasContatoreType: any;

  isHaveGASService = false;
  isHaveEEService = false;
  // Luce
  luceContatoreList: any[] = [];
  luceContatoreTypeList: any[] = [];
  public eeFormGroup: FormGroup;
  eeFilesBeforeValidation: Array<File> = [];
  eeFilesAfterValidation: Array<File> = [];
  eeIndexesOfFilesWithWrongSize = [];
  eeIndexesOfFilesWithWrongExtension = [];
  @ViewChild('eeImagesInput')
  eeImagesInput: ElementRef;
  // Gas
  gasContatoreList: any[] = [];
  gasContatoreTypeList: any[] = [];
  public gasFormGroup: FormGroup;
  gasFilesBeforeValidation: Array<File> = [];
  gasFilesAfterValidation: Array<File> = [];
  gasIndexesOfFilesWithWrongSize = [];
  gasIndexesOfFilesWithWrongExtension = [];
  @ViewChild('gasImagesInput')
  gasImagesInput: ElementRef;
  ////
  activeServices: object;
  typeAutolettura: string;
  isMobile = window.innerWidth <= 991;
  isDisplayModalWindow: boolean;
  podInfo2G: Array<InfoPod2G>;
  ////
  constructor(
    public dialog: MatDialog,
    private fb: FormBuilder,
    private incidentEventService: IncidentEventService,
    private route: ActivatedRoute,
    private attachmentService: AttachmentService,
    private energiaService: EnergiaService
  ) {
    this.formBuilderENERGIA();
  }

  ngOnInit(): void {
    this.loadActiveServices();
    this.initComponent();
    this.formBuilderGAS();
    this.typeAutolettura = this.route.snapshot.params['type'];
  }

  ngAfterViewChecked(): void {
    this.scrollToAutoletturaByType();
  }

  scrollToAutoletturaByType() : void {
    if (this.isMobile && this.typeAutolettura) {
      let el = document.getElementById(this.typeAutolettura);
      el.scrollIntoView();
    }
  }

  formBuilderGAS() {
    this.gasFormGroup = this.fb.group({
      pdr: [null, Validators.required],
      contatoreType: [null, Validators.required],
      gas: this.fb.group({
        letturaContatore: [null, [Validators.required, Validators.min(0), Validators.max(999999999999)]],
        letturaCorrettore: [null, [Validators.required, Validators.min(0), Validators.max(999999999999)]],
      }),
      type: ['GAS', Validators.required],
      files: [null]
    });

    // subscribers
    this.gasSubscribeOnChanges();
  }

  private gasSubscribeOnChanges() {
    const gasContatoreType = this.gasFormGroup.controls['contatoreType'] as FormArray;
    gasContatoreType.valueChanges.subscribe((data: any) => {
      this.selectedGasContatoreType = data !== '' ? data : null;
      const gas = this.gasFormGroup.controls['gas'] as FormGroup;
      if (data === 'Contatore con correttore NO') {
        gas.controls['letturaCorrettore'].disable();
        gas.controls['letturaCorrettore'].reset(null);
      } else {
        gas.controls['letturaCorrettore'].enable();
      }
    });
  }


  formBuilderENERGIA() {
    this.eeFormGroup = this.fb.group({
      pdr: [null, Validators.required],
      is2GInfoMissing: [null, Validators.requiredTrue],
      contatoreType: [null, Validators.required],
      consumptions: this.fb.array([
        this.fb.group({ consumption: [null, [Validators.required, Validators.min(0), Validators.max(999999999999)]] }),
        this.fb.group({ consumption: [null, [Validator.requiredIfFieldIs('contatoreType', 'Contatore elettronico'), Validators.min(0), Validators.max(999999999999)]] }),
        this.fb.group({ consumption: [null, [Validator.requiredIfFieldIs('contatoreType', 'Contatore elettronico'), Validators.min(0), Validators.max(999999999999)]] })
      ]),
      type: ['ENERGIA', Validators.required],
      files: [null]
    });

    // subscribers
    this.eeSubscribeOnChanges();
    Validator.listenOtherInputAndValidateWithoutUpdateAndТestedАields(this.eeFormGroup, 'contatoreType', ['consumptions']);
  }

  private eeSubscribeOnChanges() {
    // luce
    const luceContatoreType = this.eeFormGroup.controls['contatoreType'] as FormArray;
    luceContatoreType.valueChanges.subscribe((data: any) => {
      this.selectedLuceContatoreType = data !== '' ? data : null;
    });
  }

  loadActiveServices() {
    this.incidentEventService.getUserServices(localStorage.getItem('clientId')).subscribe((data) => {

      this.activeServices = this.incidentEventService.getActiveServices(data);
      if (Object.keys(this.activeServices).length === 0) {
        this.gasFormGroup.controls.type.setErrors({error: 'You don\'t have any active service.'});
        this.gasFormGroup.controls.pdr.disable();
        FormUtils.disableForm(this.gasFormGroup.controls.consumptions as FormArray);
      }
      this.gasContatoreList = this.activeServices['GAS'] && this.activeServices['GAS'].utilities;
      this.luceContatoreList = this.activeServices['ENERGIA'] && this.activeServices['ENERGIA'].utilities;
      this.energiaService.load2GPodDetails(this.luceContatoreList.map(item => item.utNumber)).subscribe(response => this.podInfo2G = response);
      this.isHaveEEService = this.activeServices['ENERGIA'];
      this.isHaveGASService = this.activeServices['GAS'];
    });
  }

  onSubmitGAS() {
    if (this.gasFormGroup.valid) {
      const letturaContatore = 'lettura contatore - ' + this.gasFormGroup.value.gas.letturaContatore;
      const letturaCorrettore = this.gasFormGroup.value.gas.letturaCorrettore ? ('lettura correttore - ' + this.gasFormGroup.value.gas.letturaCorrettore) : '';
      const text = 'I dati di autolettura da te inseriti: ' + letturaContatore + '   ' + letturaCorrettore + '. Clicca su Conferma per procedere o su Annulla per reinserire i dati corretti.';
      this.openDialog(text).subscribe((result: boolean) => {
        if (result) {
          const request = this.incidentEventService.prepareDataForIncidentEventRequest(this.gasFormGroup.value as AutoLetturaForm);
          this.incidentEventService.incidentEvent(request).subscribe((data: IncidentEventResponse) => {
            if (data && data.status === 'OK') {
              if (this.gasFormGroup.get('files').value) {
                const formData = new FormData();
                for (let i = 0; i < this.gasFormGroup.get('files').value.length; i++) {
                  formData.append('files', this.gasFormGroup.get('files').value[i]);
                }
                formData.append('incidentId', data.incidentId);
                formData.append('message', 'allegato da selfcare');
                this.attachmentService.sendMultiAttachment(formData).subscribe();
              }
              this.gasImagesInput.nativeElement.value = '';
              this.gasFormGroup.reset();
              this.resetFilesVariablesForGAS();
              this.formBuilderGAS();
            }
            this.incidentEventService.createIncidentEventResponseNotification(data, messages.successMessage, messages.failedMessage);
          });
        } else {
          this.formBuilderGAS();
        }
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.gasFormGroup);
    }
  }

  onSubmitEE() {
    if (this.eeFormGroup.valid) {
      const consumption1 = this.eeFormGroup.value.consumptions[0].consumption;
      const consumption2 = this.eeFormGroup.value.consumptions[1].consumption;
      const consumption3 = this.eeFormGroup.value.consumptions[2].consumption;
      const text = 'I dati di autolettura da te inseriti risultano: F1 - ' + consumption1 + ' / F2 - ' + consumption2 + '/ F3 - ' + consumption3 + '. Clicca su Conferma per procedere o su Annulla per reinserire i dati corretti.';
      this.openDialog(text).subscribe((result: boolean) => {
        if (result) {
          const request = this.incidentEventService.prepareDataForIncidentEventRequest(this.eeFormGroup.value as AutoLetturaForm);
          this.incidentEventService.incidentEvent(request).subscribe((data: IncidentEventResponse) => {
            if (data && data.status === 'OK') {
              if (this.eeFormGroup.get('files').value) {
                const formData = new FormData();
                for (let i = 0; i < this.eeFormGroup.get('files').value.length; i++) {
                  formData.append('files', this.eeFormGroup.get('files').value[i]);
                }
                formData.append('incidentId', data.incidentId);
                formData.append('message', 'allegato da selfcare');
                this.attachmentService.sendMultiAttachment(formData).subscribe();
              }
              this.eeImagesInput.nativeElement.value = '';
              this.eeFormGroup.reset();
              this.resetFilesVariablesForEE();
              this.formBuilderENERGIA();
            }
            this.incidentEventService.createIncidentEventResponseNotification(data, messages.successMessage, messages.failedMessage);
          });
        } else {
          this.formBuilderENERGIA();
        }
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.eeFormGroup);
    }
  }

  checkIsPDR2G() {
    const podInfo = this.podInfo2G.find(item => item.pod === this.eeFormGroup.value.pdr);
    if (podInfo && podInfo.typeCounter === '2G') {
      this.isDisplayModalWindow = true;
      this.eeFormGroup.get('is2GInfoMissing').patchValue(false);
    } else {
      this.eeFormGroup.get('is2GInfoMissing').patchValue(true);
    }
  }

  private initComponent() {
    this.luceContatoreTypeList = [
      {title: 'Elettronico', value: 'Contatore elettronico'},
      {title: 'Meccanico', value: 'Contatore meccanico'}
    ];
    this.gasContatoreTypeList = [
      {title: 'Senza correttore', value: 'Contatore con correttore NO'},
      {title: 'Con correttore', value: 'Contatore con correttore SI'}
    ];
  }

  getConsumptions(formGroup: FormGroup) {
    if (formGroup) {
      return (<FormArray>this.eeFormGroup.controls.consumptions).controls;
    }
  }

  openDialog(data): any {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {message: data}
    });
    return dialogRef.afterClosed();
  }

  onEEImageChange(event) {
    if (event.target.files && event.target.files.length
      && this.eeFilesAfterValidation.length < 5) {
      const currentFile = event.target.files[0];
      for (let i = 0; i < this.eeFilesAfterValidation.length; i++) {
        if (this.eeFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.eeFilesBeforeValidation.push(currentFile);
      this.eeFilesAfterValidation.push(currentFile);

      if (currentFile.size > 5242880) {
        this.eeIndexesOfFilesWithWrongSize.push(this.eeFilesBeforeValidation.indexOf(currentFile));
        this.eeFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateImage(currentFile.name)) {
        this.eeIndexesOfFilesWithWrongExtension.push(this.eeFilesBeforeValidation.indexOf(currentFile));
        this.eeFilesAfterValidation.splice(-1, 1);
      }

      const reader = new FileReader();
      if (this.eeFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
      reader.onload = () => {
        this.eeFormGroup.get('files').patchValue(this.eeFilesAfterValidation);
      };
    }
    event.target.value = '';
  }

  onGASImageChange(event) {
    if (event.target.files && event.target.files.length
      && this.gasFilesAfterValidation.length < 5) {
      const currentFile = event.target.files[0];
      for (let i = 0; i < this.gasFilesAfterValidation.length; i++) {
        if (this.gasFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.gasFilesBeforeValidation.push(currentFile);
      this.gasFilesAfterValidation.push(currentFile);

      if (currentFile.size > 5242880) {
        this.gasIndexesOfFilesWithWrongSize.push(this.gasFilesBeforeValidation.indexOf(currentFile));
        this.gasFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateImage(currentFile.name)) {
        this.gasIndexesOfFilesWithWrongExtension.push(this.gasFilesBeforeValidation.indexOf(currentFile));
        this.gasFilesAfterValidation.splice(-1, 1);
      }

      const reader = new FileReader();
      if (this.gasFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
      reader.onload = () => {
        this.gasFormGroup.get('files').patchValue(this.gasFilesAfterValidation);
      };
    }
    event.target.value = '';
  }

  removeImageFromEEForm(beforeValIndex: number, afterValIndex: number) {
    this.eeFilesBeforeValidation.splice(beforeValIndex, 1);
    this.eeFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.eeIndexesOfFilesWithWrongSize.length; i++) {
      if (this.eeIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.eeIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.eeIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.eeIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.eeIndexesOfFilesWithWrongExtension[i]--;
      }
    }
    this.eeFormGroup.get('files').patchValue(this.eeFilesAfterValidation);
  }

  removeImageFromGASForm(beforeValIndex: number, afterValIndex: number) {
    this.gasFilesBeforeValidation.splice(beforeValIndex, 1);
    this.gasFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.gasIndexesOfFilesWithWrongSize.length; i++) {
      if (this.gasIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.gasIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.gasIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.gasIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.gasIndexesOfFilesWithWrongExtension[i]--;
      }
    }
    this.gasFormGroup.get('files').patchValue(this.gasFilesAfterValidation);
  }

  resetFilesVariablesForEE () {
    this.eeIndexesOfFilesWithWrongSize = [];
    this.eeIndexesOfFilesWithWrongExtension = [];
    this.eeFilesBeforeValidation = [];
    this.eeFilesAfterValidation = [];
  }

  resetFilesVariablesForGAS () {
    this.gasIndexesOfFilesWithWrongSize = [];
    this.gasIndexesOfFilesWithWrongExtension = [];
    this.gasFilesBeforeValidation = [];
    this.gasFilesAfterValidation = [];
  }

  resetWarningInEEForm(index: number) {
    this.eeFilesBeforeValidation.splice(index, 1);
    this.eeIndexesOfFilesWithWrongSize = this.eeIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.eeIndexesOfFilesWithWrongExtension = this.eeIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  resetWarningInGASForm(index: number) {
    this.gasFilesBeforeValidation.splice(index, 1);
    this.gasIndexesOfFilesWithWrongSize = this.gasIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.gasIndexesOfFilesWithWrongExtension = this.gasIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  validateImage(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf' || ext.toLowerCase() === 'jpg' || ext.toLowerCase() === 'jpeg' || ext.toLowerCase() === 'png';
  }

  closeModalWindow() {
    this.isDisplayModalWindow = false;
  }
}


@Component({
  selector: 'app-login-alert',
  template: `
    <div>
      <p style="color: red;">{{data.message}}</p>
    </div>
    <div mat-dialog-actions>
      <button style="float: right;" mat-button [mat-dialog-close]="true" cdkFocusInitial>CONFERMA</button>
      <button style="float: right;" mat-button [mat-dialog-close]="false" cdkFocusInitial>ANNULLA</button>
    </div>
  `,
})

export class ConfirmDialogComponent {

  constructor(
    public dialogRef: MatDialogRef<ConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData) {
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

}

export interface ConfirmDialogData {
  message: string;
  extra: any;
}
