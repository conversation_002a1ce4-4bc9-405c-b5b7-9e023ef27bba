
import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {Observable} from 'rxjs/Observable';
import 'rxjs/add/operator/do';


@Component({
  selector: 'app-user-guide',
  templateUrl: './user-guide.component.html',
  styleUrls: ['./user-guide.component.scss']
})
export class UserGuideAutoletturaComponent implements OnInit {

  @ViewChild('userGuide') userGuide: ElementRef;
  @ViewChild('ee') ee: ElementRef;
  @ViewChild('eeTooltip') eeTooltip: ElementRef;
  @ViewChild('eeCheckbox') eeCheckbox: ElementRef;
  @ViewChild('eeMainPOD') eeMainPod: ElementRef;
  @ViewChild('eePOD') eePOD: ElementRef;
  @ViewChild('eePODSelect') eePODSelect: ElementRef;
  @ViewChild('eeMainF1') eeMainF1: ElementRef;
  @ViewChild('eeMainF2') eeMainF2: ElementRef;
  @ViewChild('eeMainF3') eeMainF3: ElementRef;
  @ViewChild('eeMainSend') eeMainSend: ElementRef;
  @ViewChild('podTooltip') podTooltip: ElementRef;
  @ViewChild('f1Tooltip') f1Tooltip: ElementRef;
  @ViewChild('f2Tooltip') f2Tooltip: ElementRef;
  @ViewChild('f3Tooltip') f3Tooltip: ElementRef;
  @ViewChild('eeSendTooltip') eeSendTooltip: ElementRef;
  @ViewChild('eeF1') eeF1: ElementRef;
  @ViewChild('eeF2') eeF2: ElementRef;
  @ViewChild('eeF3') eeF3: ElementRef;
  @ViewChild('eeSendButton') eeSendButton: ElementRef;


  constructor() {
  }

  onClose() {
    this.userGuide.nativeElement.style.display = 'none';
  }

  viewEE() {
    this.ee.nativeElement.style.zIndex = 2650;
    return Observable.timer(2000);
  }

  viewEETooltip() {
    this.eeTooltip.nativeElement.style.visibility = 'visible';
    return Observable.timer(1000);
  }

  viewEEMain() {
    this.eeMainPod.nativeElement.style.display = 'block';
    this.eeMainF3.nativeElement.style.display = 'block';
    this.eeMainF2.nativeElement.style.display = 'block';
    this.eeMainF1.nativeElement.style.display = 'block';
    this.eeMainSend.nativeElement.style.display = 'block';
    return Observable.timer(1000);
  }

  hideEETooltip() {
    this.eeTooltip.nativeElement.style.visibility = 'hidden';
    return Observable.timer(1000);
  }

  viewEEpod() {
    this.eeMainPod.nativeElement.style.zIndex = 2650;
    this.viewTooltip(this.podTooltip.nativeElement);
    return Observable.timer(1000);
  }

  view(element) {
    element.style.zIndex = 2650;
    return Observable.timer(1000);
  }

  clickEEpod() {
    this.podTooltip.nativeElement.style.visibility = 'hidden';
    this.eePODSelect.nativeElement.setAttribute('selected', 'selected');
    return Observable.timer(1000);
  }

  hide(element) {
    element.style.zIndex = 2550;
    return Observable.timer(1000);
  }

  viewTooltip(element) {
    element.style.visibility = 'visible';
    return Observable.timer(1000);
  }

  heidTooltip(element) {
    element.style.visibility = 'hidden';
    return Observable.timer(1000);
  }

  ngOnInit(): void {
    this.viewEE()
      .flatMap(() => this.viewEETooltip())
      .flatMap(() => this.hideEETooltip())
      .flatMap(() => this.check(this.eeCheckbox.nativeElement))
      .flatMap(() => this.viewEEMain())
      .flatMap(() => this.viewEEpod())
      .flatMap(() => this.clickEEpod())
      .flatMap(() => this.hide(this.eeMainPod.nativeElement))
      .flatMap(() => this.view(this.eeMainF1.nativeElement))
      .flatMap(() => this.viewTooltip(this.f1Tooltip.nativeElement))
      .flatMap(() => this.type('123123', this.eeF1.nativeElement))
      .flatMap(() => this.heidTooltip(this.f1Tooltip.nativeElement))
      .flatMap(() => this.hide(this.eeMainF1.nativeElement))
      .flatMap(() => this.view(this.eeMainF2.nativeElement))
      .flatMap(() => this.viewTooltip(this.f2Tooltip.nativeElement))
      .flatMap(() => this.type('123123', this.eeF2.nativeElement))
      .flatMap(() => this.heidTooltip(this.f2Tooltip.nativeElement))
      .flatMap(() => this.hide(this.eeMainF2.nativeElement))
      .flatMap(() => this.view(this.eeMainF3.nativeElement))
      .flatMap(() => this.viewTooltip(this.f3Tooltip.nativeElement))
      .flatMap(() => this.type('123123', this.eeF3.nativeElement))
      .flatMap(() => this.heidTooltip(this.f3Tooltip.nativeElement))
      .flatMap(() => this.hide(this.eeMainF3.nativeElement))
      .flatMap(() => this.view(this.eeMainSend.nativeElement))
      .flatMap(() => this.viewTooltip(this.eeSendTooltip.nativeElement))
      .flatMap(() => this.check(this.eeSendButton.nativeElement))
      .flatMap(() => this.changeColor(this.eeSendButton.nativeElement, '#0c7fda'))
      .flatMap(() => this.changeColor(this.eeSendButton.nativeElement, '#0e8df2'))
      .flatMap(() => this.heidTooltip(this.eeSendTooltip.nativeElement))
      .flatMap(() => {this.onClose(); return Observable.timer(50); })
      .subscribe();

  }


  check(element) {
    element.click();
    return Observable.timer(1000);
  }

  type(text, element) {
    element.value = text;
    return Observable.timer(500);
  }

  changeColor(element, color) {
    element.style.background = color;
    return Observable.timer(500);
  }
}
