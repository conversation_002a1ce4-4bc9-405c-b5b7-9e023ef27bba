import {AfterViewInit, Component} from '@angular/core';
import {ActivatedRoute} from '@angular/router';

declare global {
  interface Window { imageMapResize: any; }
  function imageMapResize(): void;
}

@Component({
  selector: 'app-myoptima-layout',
  templateUrl: './myoptima-layout.component.html',
  styleUrls: ['./myoptima-layout.component.scss']
})
export class MyoptimaLayoutComponent implements AfterViewInit {

  isMobile = window.innerWidth <= 991;
  isBusiness = false;

  constructor(private router: ActivatedRoute) {
    this.isBusiness = this.router.snapshot.params['clusterType'] === 'business';
  }

  ngAfterViewInit(): void {
    window.imageMapResize();
  }
}
