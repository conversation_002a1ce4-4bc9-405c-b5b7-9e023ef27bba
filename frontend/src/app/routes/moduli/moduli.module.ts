import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { SharedModule } from '../../shared/shared.module';

import { ModuliComponent } from './moduli.component';


import { DocumentFormsResultComponent } from './document-forms-result/document-forms-result.component';
import { DocumentFormsResultService } from './document-forms-result/document-forms-result.service';
import { CommonModule } from '../../common/common.module';



const routes: Routes = [
    {
        path: 'moduli',
        component: ModuliComponent,
        children: [
            {
                path: '',
                component: DocumentFormsResultComponent,
            },
        ]
    }
];

@NgModule({
    imports: [
        CommonModule,
        SharedModule,
        RouterModule.forChild(routes)
    ],
    declarations: [
        ModuliComponent,
        DocumentFormsResultComponent
    ],
    providers: [
        DocumentFormsResultService
    ],
    exports: [
        RouterModule
    ]
})
export class ModuliModule {
}
