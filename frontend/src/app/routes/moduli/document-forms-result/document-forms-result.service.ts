import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { FormDocument } from './form-document.model';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class DocumentFormsResultService {

  private http: HttpClient;

  documentTypes: Array<string> = ['Business', 'Consumer'];


  constructor(http: HttpClient) {
    this.http = http;
  }

  loadDocumentsFormsData(): Observable<FormDocument[]> {
    return this.http.post<FormDocument[]>('/api/questions/answers', {});
  }

  public groupDocumentsData(documentsData: Array<FormDocument>) {
    const result = {};
    this.documentTypes.forEach(documentType => {
      result[documentType] = {};
      documentsData.filter(value => value.metadata.cluster && value.metadata.cluster.indexOf(documentType) > -1)
        .forEach(document => {
          document.metadata.servizio.split(',').forEach(service => {
            if (!result[documentType][service]) {
              result[documentType][service] = [document];
            } else {
              result[documentType][service].push(document);
            }
          });
        });
    });
    return result;
  }

  public filterDocuments(documentsData: Array<FormDocument>, filter: string) {
    const result = {};
    if (!filter) {
      return documentsData;
    }
    if (!documentsData) {
      return null;
    }
    this.documentTypes.forEach(documentType => {
      result[documentType] = {};
      documentsData.filter(value => value.metadata.cluster
        && value.metadata.cluster.indexOf(documentType) > -1
        && value.title && value.title.toLowerCase().indexOf(filter.toLowerCase()) > -1)
        .forEach(document => {
          document.metadata.servizio.split(',').forEach(service => {
            if (!result[documentType][service]) {
              result[documentType][service] = [document];
            } else {
              result[documentType][service].push(document);
            }
          });
        });
    });
    return result;
  }

}
