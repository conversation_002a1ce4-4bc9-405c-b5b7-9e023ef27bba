import {<PERSON><PERSON><PERSON>, <PERSON>ementR<PERSON>, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {SegnalazioniService} from './segnalazioni.service';
import {Signal} from './segnalazioni.model';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {EmailService} from '../../../../common/services/email/email.service';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {DomSanitizer} from '@angular/platform-browser';
import {AttachmentService} from '../../services/attachment/attachment.service';
import {OtpService} from '../../../../common/services/otp/otp.service';
import {modalWithNoPhoneAndEmail, openOTPModal} from '../../config/config';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {ModalInputEmitActions} from '../../../../redux/dialogModalInputEmit/actions';
import EmailMessage from '../../../../common/model/EmailMessage';
import {date} from "ng2-validation/dist/date";
import {DatePipe} from "@angular/common";
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';

@Component({
  selector: 'app-segnalazioni',
  templateUrl: './segnalazioni.component.html',
  styleUrls: ['./segnalazioni.component.scss']
})
export class SegnalazioniComponent implements OnInit, OnDestroy {

  datePipeString :string;
  sorted:Array<any>;

  // riapri block
  formGroupRiapri: FormGroup;
  riapriFilesBeforeValidation: Array<File> = [];
  riapriFilesAfterValidation: Array<File> = [];
  riapriIndexesOfFilesWithWrongSize = [];
  riapriIndexesOfFilesWithWrongExtension = [];
  isRiapriBlockShown: boolean;
  ticketNumberForRiapri: string;
  @ViewChild('riapri')
  riapriBlock: ElementRef;
  @ViewChild('riapriFilesInput')
  riapriFilesInput: ElementRef;

  // inserisci block
  formGroupInserisci: FormGroup;
  isInserisciFileSizeWrong = false;
  isInserisciFileExtensionWrong = false;
  isInserisciBlockShown: boolean;
  ticketNumberForInserisci: string;
  @ViewChild('inserisci')
  inserisciBlock: ElementRef;
  @ViewChild('inserisciFileInput')
  inserisciFileInput: ElementRef;

  // other variables
  @ViewChild('notification')
  notification: ElementRef;
  segnalazioniData: Signal[];
  msg: string;
  @select(['user', 'userInfo'])
  $userInfo: Observable<UserData>;
  @select(['emitInput', 'isInputFieldCorrect'])
  $enteredOtpCorrectness: Observable<boolean>;
  userNumber: string;
  userEmail: string;
  sendingType: number;

  enteredOTPSubscription: Subscription;

  constructor(private sanitizer: DomSanitizer, private attachmentService: AttachmentService,
              private segnalazioniService: SegnalazioniService, private formBuilder: FormBuilder,
              private emailService: EmailService, private otpService: OtpService,
              private dialogModalActions: DialogModalActions,
              private modalInputEmitActions: ModalInputEmitActions,
              private datePipe:DatePipe) {
    this.formGroupRiapri = formBuilder.group({
      message: ['', [Validators.required, Validators.minLength(5)]],
      subject: ['', [Validators.required, Validators.minLength(2)]],
      files: [null],
      incidentId: [null]
    });
    this.formGroupInserisci = formBuilder.group({
      message: ['', [Validators.required, Validators.minLength(5)]],
      subject: ['', [Validators.required, Validators.minLength(2)]],
      file: [null],
      incidentId: [null]
    });
    this.segnalazioniService.getSignalData().subscribe(data => {
      data.forEach(item => {
        if (item.Opt_CaratterizzazioneDescrizione === 'Dash Button') {
          item.DescrizioneTripletta = 'Richiesta Ricontatto';
        }
      });
      this.segnalazioniData = data;
    });

    this.$userInfo.subscribe(user => {
      if (user) {
        this.userNumber =
          user.mobileNumber && (user.mobileNumber.startsWith('3') || user.mobileNumber.startsWith('00393'))
            ? user.mobileNumber
            : user.phoneNumber && (user.phoneNumber.startsWith('3') || user.phoneNumber.startsWith('00393'))
            ? user.phoneNumber
            : null;
        this.userEmail = user.email;
      }
    });
    this.enteredOTPSubscription = this.$enteredOtpCorrectness.subscribe((value: boolean) => {
      if (value) {
        this.modalInputEmitActions.emitInputFieldValue(false);
        // if (this.sendingType === 1) {
        //   this.sendEmail('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
        // }
        if (this.sendingType === 2) {
          this.sendRiapri('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
        }
        if (this.sendingType === 3) {
          this.sendInserisci('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
        }
      }
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }

  openRiapriForm(ticketNumberForRiapri, incidentId: string) {
    this.closeInserisciForm();
    this.formGroupRiapri.controls['message'].reset();
    this.resetFilesVariablesForRiapri();
    this.modalInputEmitActions.emitInputFieldValue(false);
    this.formGroupRiapri.patchValue({
      subject: 'Riapertura caso ' + ticketNumberForRiapri + ' da area clienti' + ' dal cliente - ' + localStorage.getItem('clientId'),
      message: null,
      incidentId: incidentId
    });
    this.ticketNumberForRiapri = ticketNumberForRiapri;
    this.isRiapriBlockShown = true;
  }

  openInserisciForm(ticketNumberForInserisci, incidentId: string) {
    this.closeRiapriForm();
    this.formGroupInserisci.controls['message'].reset();
    this.resetWarningInInserisciForm();
    this.modalInputEmitActions.emitInputFieldValue(false);
    this.formGroupInserisci.patchValue({
      subject: 'Riapertura caso ' + ticketNumberForInserisci + ' da area clienti' + ' dal cliente - ' + localStorage.getItem('clientId'),
      message: null,
      incidentId: incidentId
    });
    this.ticketNumberForInserisci = ticketNumberForInserisci;
    this.isInserisciBlockShown = true;
  }

  onRiapriFileChange(event) {
    if (event.target.files && event.target.files.length
      && this.riapriFilesAfterValidation.length < 5) {
      const currentFile = event.target.files[0];
      for (let i = 0; i < this.riapriFilesAfterValidation.length; i++) {
        if (this.riapriFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.riapriFilesBeforeValidation.push(currentFile);
      this.riapriFilesAfterValidation.push(currentFile);

      if (currentFile.size > 5242880) {
        this.riapriIndexesOfFilesWithWrongSize.push(this.riapriFilesBeforeValidation.indexOf(currentFile));
        this.riapriFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateFile(currentFile.name)) {
        this.riapriIndexesOfFilesWithWrongExtension.push(this.riapriFilesBeforeValidation.indexOf(currentFile));
        this.riapriFilesAfterValidation.splice(-1, 1);
      }

      const reader = new FileReader();
      if (this.riapriFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
      reader.onload = () => {
        this.formGroupRiapri.get('files').patchValue(this.riapriFilesAfterValidation);
      };
    }
    event.target.value = '';
  }

  onInserisciFileChange(event) {
    this.isInserisciFileSizeWrong = false;
    this.isInserisciFileExtensionWrong = false;
    if (event.target.files && event.target.files.length) {
      const file = event.target.files[0] as File;
      if (file.size > 5242880) {
        this.removeFileFromInserisciForm();
        this.isInserisciFileSizeWrong = true;
      } else if (!this.validateFile(file.name)) {
        this.removeFileFromInserisciForm();
        this.isInserisciFileExtensionWrong = true;
      }

      if (!this.isInserisciFileSizeWrong && !this.isInserisciFileExtensionWrong) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          this.formGroupInserisci.get('file').patchValue(file);
        };
      }
    }
  }

  sendRiapri(msg) {
    if (!this.formGroupRiapri.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupRiapri);
    } else {
      if (this.formGroupRiapri.get('files').value) {
        const formData = new FormData();
        for (let i = 0; i < this.formGroupRiapri.get('files').value.length; i++) {
          formData.append('files', this.formGroupRiapri.get('files').value[i]);
        }
        formData.append('subject', this.formGroupRiapri.get('subject').value);
        formData.append('message', this.formGroupRiapri.get('message').value);
        this.emailService.sendEmail(formData).subscribe(() => {
          this.notification.nativeElement.style.display = 'block';
          this.msg = msg;
        });
      } else {
        const emailMessage = new EmailMessage();
        emailMessage.message = this.formGroupRiapri.get('message').value;
        emailMessage.subject = this.formGroupRiapri.get('subject').value;
        this.emailService.sendEmail(emailMessage).subscribe(() => {
          this.notification.nativeElement.style.display = 'block';
          this.msg = msg;
        });
      }
      this.riapriFilesInput.nativeElement.value = '';
      this.formGroupRiapri.reset();
      this.closeRiapriForm();
    }
  }

  sendInserisci(msg) {
    if (!this.formGroupInserisci.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupInserisci);
    } else {
      if (this.formGroupInserisci.get('file').value) {
        const formData = new FormData();
        formData.append('file', this.formGroupInserisci.get('file').value);
        formData.append('incidentId', this.formGroupInserisci.get('incidentId').value);
        formData.append('subject', this.formGroupInserisci.get('subject').value);
        formData.append('message', this.formGroupInserisci.get('message').value);
        this.attachmentService.sendAttachment(formData).subscribe(() => {
          this.notification.nativeElement.style.display = 'block';
          this.msg = msg;
        });
      } else {
        const formData = new FormData();
        formData.append('incidentId', this.formGroupInserisci.get('incidentId').value);
        formData.append('subject', this.formGroupInserisci.get('subject').value);
        formData.append('message', this.formGroupInserisci.get('message').value);
        this.attachmentService.sendIncident(formData).subscribe(() => {
          this.notification.nativeElement.style.display = 'block';
          this.msg = msg;
        });
      }
      this.inserisciFileInput.nativeElement.value = '';
      this.formGroupInserisci.reset();
      this.closeInserisciForm();
      this.resetWarningInInserisciForm();
    }
  }

  preSendRiapri(sendRiapriByEmail?: boolean) {
    if (!this.userEmail && !this.userNumber) {
      this.dialogModalActions.showDialogModal(modalWithNoPhoneAndEmail);
    } else {
      if (this.formGroupRiapri.get('files').value) {
        this.otpService.sendOTP().subscribe();
        this.dialogModalActions.showDialogModal(openOTPModal(this.otpService, this.userNumber, this.userEmail));
        this.sendingType = 2;
        return;
      }
      this.sendRiapri('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
    }
  }

  preSendInserisci() {
    if (!this.userEmail && !this.userNumber) {
      this.dialogModalActions.showDialogModal(modalWithNoPhoneAndEmail);
    } else {
      if (this.formGroupInserisci.get('file').value) {
        this.otpService.sendOTP().subscribe();
        this.dialogModalActions.showDialogModal(openOTPModal(this.otpService, this.userNumber, this.userEmail));
        this.sendingType = 3;
        return;
      }
      this.sendInserisci('Il tuo messaggio per il Servizio Clienti è stato registrato.\n A breve riceverai riscontro in merito. Grazie');
    }
  }

  removeFileFromRiapriForm(beforeValIndex: number, afterValIndex: number) {
    this.riapriFilesBeforeValidation.splice(beforeValIndex, 1);
    this.riapriFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.riapriIndexesOfFilesWithWrongSize.length; i++) {
      if (this.riapriIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.riapriIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.riapriIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.riapriIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.riapriIndexesOfFilesWithWrongExtension[i]--;
      }
    }
    this.formGroupRiapri.get('files').patchValue(this.riapriFilesAfterValidation);
  }

  removeFileFromInserisciForm() {
    this.formGroupInserisci.get('file').patchValue(null);
    this.inserisciFileInput.nativeElement.value = '';
  }

  resetFilesVariablesForRiapri() {
    this.riapriFilesBeforeValidation = [];
    this.riapriFilesAfterValidation = [];
    this.riapriIndexesOfFilesWithWrongExtension = [];
    this.riapriIndexesOfFilesWithWrongSize = [];
  }

  validateFile(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf' || ext.toLowerCase() === 'jpg' || ext.toLowerCase() === 'jpeg';
  }

  closeRiapriForm() {
    this.formGroupRiapri.controls['message'].reset();
    this.resetFilesVariablesForRiapri();
    this.isRiapriBlockShown = false;
  }

  closeInserisciForm() {
    this.formGroupInserisci.controls['message'].reset();
    this.resetWarningInInserisciForm();
    this.isInserisciBlockShown = false;
  }

  closeWarningInRiapriForm(index: number) {
    this.riapriFilesBeforeValidation.splice(index, 1);
    this.riapriIndexesOfFilesWithWrongSize = this.riapriIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.riapriIndexesOfFilesWithWrongExtension = this.riapriIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  resetWarningInInserisciForm() {
    this.isInserisciFileExtensionWrong = false;
    this.isInserisciFileSizeWrong = false;
  }

  closeNotification() {
    this.notification.nativeElement.style.display = 'none';
  }

  getNotes(row: any) {
    const dataArray = [];
    if (row) {
      this.sorted = row.Annotation.sort((a, b) => +new Date(b.CreatedOn) - +new Date(a.CreatedOn));
      for (let i = 0; i < this.sorted.length; i++) {
        this.datePipeString = this.datePipe.transform(this.sorted[i].CreatedOn,'dd/MM/yyyy hh:mm:ss');
        dataArray.push('-'+this.datePipeString+' - '+this.sorted[i].NoteText);
      }
    }
    return dataArray;
  }

  getAttachments(row: any) {
    let dataArray = [];
    if (row) {
      for (let i = 0; i < row.Annotation.length; i++) {
        const data = row.Annotation[i].attachments.filter((item: any) => item.relUrl);
        dataArray = dataArray.concat(data);
      }
    }
    return dataArray;
  }

  createUrl(row, url) {
    return `/api/segnalazione/pdf/${localStorage.getItem('clientId')}?file=${url}&access_token=${localStorage.getItem('access_token')}`;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.enteredOTPSubscription]);
  }

}
