<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 app-segnalazioni">
  <div class="col-lg-12 col-md-12 page-title">
    <div class="title-image"></div>
    <div class="title-text">LE TUE SEGNALAZIONI</div>
  </div>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 signal-data-table" *ngIf="segnalazioniData?.length">
    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 table-header">Segnalazione</div>
    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 table-header">Data apertura</div>
    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 table-header">Stato</div>
    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-2 table-header">Note</div>
    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-2 table-header">Scarica</div>

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12  line"></div>

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 signal-row no-padding"
         *ngFor="let item of segnalazioniData">
      <div class="col-xs-12 mobile-header">Segnalazione:</div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12"><b>{{item.DescrizioneTripletta}}</b></div>
      <div class="col-xs-12 mobile-header">Data apertura:</div>
      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"> {{item.CreatedOn| date : "dd/MM/y" }}</div>
      <div class="col-xs-12 mobile-header">Stato:</div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <span *ngIf="item.StatusCode === 1 && item.StateCode === 0">
        <span>In lavorazione</span>

        </span>
        <span *ngIf="item.StatusCode === 6 && item.StateCode === 2">Annullato</span>
        <span *ngIf="item.StatusCode === 5 && item.StateCode === 1">
          <span>Risolto</span>
          <!--<button class="default-app-button"
                  (click)="openRiapriForm(item.TicketNumber, item.IncidentId)">Riapri segnalazione</button>-->
        </span>
      </div>
      <div class="col-xs-12 mobile-header">Note:</div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
        <div *ngIf="getNotes(item).length > 0">
              <span *ngFor="let note of getNotes(item)">
                <div *ngIf="note">{{note}}</div>
              </span>
        </div>
        <span *ngIf="item.StatusCode === 1 && item.StateCode === 0">
          <button class="default-app-button"
                  (click)="openInserisciForm(item.TicketNumber, item.IncidentId)">Inserisci Nota</button>
        </span>
        <!--<span *ngIf="!getNotes(item).length > 0">-</span>-->
      </div>
      <div class="col-xs-12 mobile-header">Scarica:</div>
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12">
        <div *ngIf="getAttachments(item).length > 0">
              <span *ngFor="let attachment of getAttachments(item)">
                <a *ngIf="attachment.relUrl" class="icon-pdf-load pull-left" target="_blank"
                   [attr.href]="createUrl(item, attachment.relUrl)">test</a>
                <span *ngIf="!attachment.relUrl">-</span>
              </span>
        </div>
        <span *ngIf="!getAttachments(item).length > 0">-</span>
      </div>
    </div>
  </div>

  <button class="default-app-button invia-segnalazioni" routerLink="/faidate/segnalazioni/invia-segnalazioni"> Invia una segnalazione > </button>

  <div class="notification" #notification>
    <span class="close" (click)="closeNotification()"> X </span>
    {{msg}}
  </div>

  <div #riapri class="riapri" [hidden]="!isRiapriBlockShown">
    <span class="title">Esponici il motivo della riapertura</span>
    <span class="close" (click)="closeRiapriForm()">X</span>
    <div class="form" [formGroup]="formGroupRiapri">
      <div class="form-group">
        <textarea class="field mes" title="" [formControlName]="'message'"></textarea>
        <span class="text-danger" *ngIf="formGroupRiapri.controls['message'].hasError('required')
          && (formGroupRiapri.controls['message'].dirty || formGroupRiapri.controls['message'].touched)">
        Campo obbligatorio.
        </span>
        <span class="text-danger" *ngIf="formGroupRiapri.controls['message'].hasError('minlength') &&
       (formGroupRiapri.controls['message'].dirty || formGroupRiapri.controls['message'].touched)">
        La lunghezza minima del messaggio deve essere {{formGroupRiapri.controls['message']
          .getError('minlength').requiredLength}} caratteri.
        </span>
        <div style="display: flex">
          <div style="width: 80%">
            <div style="margin-top: 20px">
              <div style="display: flex">
                <!--<div class="flex-width-left-part">
                  <label class="default-app-button label-left" for="files-upload-riapri">Allegato</label>
                </div>-->
                <div class="flex-width-right-part">
                  <div *ngFor="let file of riapriFilesBeforeValidation; let i = index">
                    <div class="file-name" *ngIf="!riapriIndexesOfFilesWithWrongSize.includes(i)
                      && !riapriIndexesOfFilesWithWrongExtension.includes(i)">
                      {{riapriFilesBeforeValidation[i].name}}
                    </div>
                    <div *ngIf="riapriIndexesOfFilesWithWrongSize.includes(i)" class="file-wrong">
                      La dimensione del file deve essere inferiore a 5 MB
                    </div>
                    <div *ngIf="riapriIndexesOfFilesWithWrongExtension.includes(i)" class="file-wrong">
                      L’estensione del file deve essere PDF o JPG
                    </div>
                    <button *ngIf="!riapriIndexesOfFilesWithWrongSize.includes(i)
                             && !riapriIndexesOfFilesWithWrongExtension.includes(i)" class="remove-file-button"
                            (click)="removeFileFromRiapriForm(i, riapriFilesAfterValidation.indexOf(file))">🞫
                    </button>
                    <button *ngIf="riapriIndexesOfFilesWithWrongSize.includes(i)
                        || riapriIndexesOfFilesWithWrongExtension.includes(i)"
                            class="remove-file-button" (click)="closeWarningInRiapriForm(i)">🞫
                    </button>
                  </div>

                  <input type="file"
                         id="files-upload-riapri"
                         (change)="onRiapriFileChange($event)"
                         accept="application/pdf,image/jpeg"
                         #riapriFilesInput>
                </div>
              </div>
            </div>
          </div>
          <div style="flex: 1">
            <button class="default-app-button right" style="margin-top: 20px"
                    [disabled]="formGroupRiapri.invalid
                || formGroupRiapri.pristine
                || riapriIndexesOfFilesWithWrongSize.length
                || riapriIndexesOfFilesWithWrongExtension.length"
                    (click)="preSendRiapri()">
              INVIA
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div #inserisci class="inserisci" [hidden]="!isInserisciBlockShown">
    <span class="title">Inserisci il tuo messaggio</span>
    <span class="close" (click)="closeInserisciForm()">X</span>
    <div class="form" [formGroup]="formGroupInserisci">
      <div class="form-group">
        <textarea class="field mes" title="" [formControlName]="'message'"></textarea>
        <span class="text-danger" *ngIf="formGroupInserisci.controls['message'].hasError('required')
          && (formGroupInserisci.controls['message'].dirty || formGroupInserisci.controls['message'].touched)">
        Campo obbligatorio.
        </span>
        <span class="text-danger" *ngIf="formGroupInserisci.controls['message'].hasError('minlength') &&
       (formGroupInserisci.controls['message'].dirty || formGroupInserisci.controls['message'].touched)">
        La lunghezza minima del messaggio deve essere {{formGroupInserisci.controls['message']
          .getError('minlength').requiredLength}} caratteri.
        </span>
        <div style="display: flex">
          <div style="width: 80%">
            <div style="margin-top: 20px">
              <div style="display: flex">
                <!--<div class="flex-width-left-part">
                  <label class="default-app-button label-left" for="file-upload-inserisci">Allegato</label>
                </div>-->
                <div class="flex-width-right-part">
                  <div class="file-name">
                    {{formGroupInserisci.get('file')?.value ? formGroupInserisci.get('file').value?.name : ''}}
                  </div>
                  <div *ngIf="isInserisciFileSizeWrong" class="file-wrong">
                    La dimensione del file deve essere inferiore a 5 MB
                  </div>
                  <div *ngIf="isInserisciFileExtensionWrong" class="file-wrong">
                    L’estensione del file deve essere PDF o JPG
                  </div>
                  <button *ngIf="formGroupInserisci.get('file')?.value" class="remove-file-button"
                          (click)="removeFileFromInserisciForm()">🞫
                  </button>
                  <button *ngIf="isInserisciFileSizeWrong || isInserisciFileExtensionWrong" class="remove-file-button"
                          (click)="resetWarningInInserisciForm()">🞫
                  </button>
                  <input type="file"
                         id="file-upload-inserisci"
                         (change)="onInserisciFileChange($event)"
                         accept="application/pdf,image/jpeg"
                         #inserisciFileInput>
                </div>
              </div>
            </div>
          </div>
          <div style="flex: 1">
            <button class="default-app-button right" style="margin-top: 20px"
                    [disabled]="formGroupInserisci.invalid
                || formGroupInserisci.pristine
                || isInserisciFileSizeWrong
                || isInserisciFileExtensionWrong"
                    (click)="preSendInserisci()">
              INVIA
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
