@import "~app/shared/styles/colors";

.container-fluid {
  padding: 40px 0 0 0;
}

.panel.panel-default {
  width: 90%;
  margin: 0 auto 5% auto;
}

.active-services-layout {
  .card-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid $menu-border;
    padding-left: 4%;
  }

  .page-title {
    background: #ffffff;
    border: 2px solid $menu-border;
    border-radius: 5px;
    height: 45px;

    .title-image {
      background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_ItuoiServizi.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 58px;
      float: left;
    }

    .title-text {
      color: $dark-blue;
      margin-top: 11px;
      float: left;
    }
  }

  .service-cards {
    margin-top: 2%;
  }
  .no-result {
    margin-top: 2%;
  }
  .service-card {
    &:nth-child(odd) {
      padding-left: 0;
    }

    &:nth-child(even) {
      padding-right: 0;
    }
  }
}
.row {
  margin: auto;
}
.panel{
  box-shadow: none;
  border: 1px solid $menu-border;
}
.panel .b {
  margin:10px auto;
  background-color: white;
  border-radius: 15px;
  border: 1px solid $menu-border;
  box-shadow: none;
}
.panel .panel-heading{
  padding: 0;
}
.topBlock{
  display: flex;
}

/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: $menu-border;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #ffffff;
}


.service-icon{
  font-size: 60px;
  padding: 10px 20px ;
}
.text{
  line-height: 80px;
  font-size: 20px;
}

.able .service-icon, .able .text, .able .status {
  color: $dark-blue;
}

.disable .service-icon, .disable .text, .disable .status {
  color: $dark-blue;
}
.empty{
  .table > thead > tr > th{
    color: #b3b3b3;
  }
  .service-icon, .text, .status{
    color: #ccd1d5;
  }
}


.detail {
  border-radius: 0 5px 5px 0;
  background: $menu-background;
  padding: 25px;
  span{
    display: block;
  }
}

.active:nth-child(odd) {
  background: $menu-background;
}

.subBlock {
  padding: 5px 19px;
  .title {
    color: $dark-blue;
    font-weight: bold;
    font-size: 20px;
  }
}

.addition {
  color: $dark-blue;
}

.plainBlock {
  font-weight: 500;
  padding-top: 10px;
  font-size: 16px;
  line-height: 14px;
  color: $dark-blue;
  > p { padding-right: 80px; }
}
.button{
  right: 40px;
  width: 80px;
  text-align: center;
  float: right;
  margin-top: -50px;
  border: 1px solid $green;
  color: white;
  background-color: $green;
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.button-servizi{
  right: 40px;
  width: 80px;
  text-align: center;
  float: right;
  margin-top: -33px;
  border: 1px solid $green;
  color: white;
  background-color: $green;
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.logo-prime {
  width: 150px;
  height: 80px;
}

//.button:hover{
//  background: #f0f5f9;
//}

/*::ng-deep .mat-menu-panel {
  min-width: 280px;
  background: white;
}*/

button {
  padding-left: 0;
}
button a{
  margin-left: 15px;
}

.total {
  overflow-x: auto;
  height: 180px;
}

@media only screen and (max-width: 1600px) {
  .panel.panel-default {
    width: 110%;
    margin: -5% auto 5% auto;
  }
  .plainBlock{
    font-size: 13px;
  }
  .button{
    right: 30px;
    width: 70px;
    text-align: center;
    float: right;
    margin-top: -50px;
    border: 1px solid $green;
    color: white;
    background-color: $green;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .button-servizi{
    right: 30px;
    width: 70px;
    text-align: center;
    float: right;
    margin-top: -33px;
    border: 1px solid $green;
    color: white;
    background-color: $green;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

}
@media only screen and (max-width: 1360px) {
  .table > thead > tr > th, .table  > tbody > tr > td {
    padding:5px 10px;
  }
  .service-icon{
    font-size: 50px;
    padding: 5px 10px ;
  }
  .text{
    line-height: 60px;
    font-size: 15px;
  }
  .table {
    font-size: 10px;
  }
}

@media only screen and (max-width: 991px) {
  .block {
    width: 100%;
    margin: auto;
    padding: 0;
  }

  .block-md-3{
    width:25%;
    float: left;
  }
  .block-md-9{
    width:75%;
    float: left;
  }
  .container-fluid{
    width: 100%;
    padding: 0;
  }
}
@media only screen and (max-width: 991px) {
  .menu{
    width: 80%;
    padding: 60px 0 0 0;
    margin: auto;
  }
  .panel.panel-default {
    width: 90%;padding: 0;
    background:none;
    box-shadow: none;

  }
  .container-fluid{
    width: 160%;
    padding: 0;
    margin-left: -30%;
    background:none;
    margin-top: 60px;
  }
}
@media only screen and (max-width: 991px){
  .active-services-layout{
    padding: 0;
    .page-title{
      display: none;
    }
  }
  .container-fluid {
    margin-top: 160px;
  }
  .panel-body {
    padding: 0;
  }
  .menu{
    padding: 0;
  }
}
@media only screen and (max-width: 800px) {
  .container-fluid {
    padding: 40px 0 0 0;
    width:90%;
    margin: auto;
  }
}

@media only screen and (max-width: 767px) {
  .active-services-layout {
    .service-cards {
      .service-card {
        padding: 0;
      }
    }
  }
}

.app--header-block {
  .topBlock {
    float: none;
    display: inline-block;
    height: 50px;
    padding-left: 5px;
  }
  .serviceName {
    display: inline-block;
    vertical-align: top;
    padding-top: 5px;
    padding-left: 25px;
  }
}

.app--topBlock-icon {
  display: inline-block;
  line-height: 45px;
}
.app--topBlock-title {
  margin-left: 5%;
  display: inline-block;
  vertical-align: top;
}
