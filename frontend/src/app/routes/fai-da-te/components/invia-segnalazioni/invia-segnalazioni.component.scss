@import "../segnalazioni/segnalazioni.component";
@import "~app/shared/styles/colors";

.combobox-group{
  margin-bottom: 3px;
  .app-select {
    width: 230px;
  }

  .dropdown-label {
    display: block;
  }
  .invoice-table {
    border: 1px solid $menu-border;
    border-radius: 5px;
    margin-bottom: 5px;
    .table-header {
      font-weight: 600;
      margin-bottom: 5px;
      display: flex;
      border-bottom: 1px solid $menu-border;
      padding: 4px;
      .header {
        width: 30%;
        text-align: center;
      }
    }
    .table-body {
      .table-row {
        display: flex;
        .table-value {
          width: 30%;
          text-align: center;
        }
      }
    }
  }

}
.invia-btn {
  border: 1px solid $green;
  color: white;
  background: $green;
}
.invia-btn:disabled{
  border: 1px solid $green;
  color: $green;
  background: white;
}
::placeholder {
  color: $dark-blue;
}
