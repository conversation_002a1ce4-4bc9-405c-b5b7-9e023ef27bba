import {<PERSON><PERSON>nent, <PERSON>ementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {FormArray, FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {AttachmentService} from '../../services/attachment/attachment.service';
import {SegnalazioniService} from '../segnalazioni/segnalazioni.service';
import {EmailService} from '../../../../common/services/email/email.service';
import {OtpService} from '../../../../common/services/otp/otp.service';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {ModalInputEmitActions} from '../../../../redux/dialogModalInputEmit/actions';
import {FormUtils} from '../../../../common/utils/FormUtils';
import EmailMessage from '../../../../common/model/EmailMessage';
import {modalWithNoPhoneAndEmail, openOTPModal} from '../../config/config';
import {UserServicesService} from '../../../profilePage/userServices/userServices.service';
import {InvoiceService} from '../../../invoices/invoice.service';
import {Invoice} from '../../../invoices/invoice.model';
import {DatePipe} from '@angular/common';
import {FissoType} from '../../../../common/enum/FissoType';
import {ServiceType} from '../../../../common/enum/Service';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';

@Component({
  selector: 'app-invia-segnalazioni',
  templateUrl: './invia-segnalazioni.component.html',
  styleUrls: ['./invia-segnalazioni.component.scss']
})
export class InviaSegnalazioniComponent implements OnInit, OnDestroy {

  // new signal block (new email)
  formGroupEmail: FormGroup;
  emailFilesBeforeValidation: Array<File> = [];
  emailFilesAfterValidation: Array<File> = [];
  emailIndexesOfFilesWithWrongSize = [];
  emailIndexesOfFilesWithWrongExtension = [];
  @ViewChild('emailFilesInput')
  emailFilesInput: ElementRef;

  // other variables
  @ViewChild('notification')
  notification: ElementRef;
  msg: string;
  @select(['user', 'userInfo'])
  $userInfo: Observable<UserData>;
  @select(['emitInput', 'isInputFieldCorrect'])
  $enteredOtpCorrectness: Observable<boolean>;
  userNumber: string;
  userEmail: string;
  sendingType: number;

  // business part
  activeServices: Object;
  activeServicesNames: Array<String>;
  tempActiveServicesNames: Array<String>;
  tecnicoServicesNames: Array<String>;
  tempTecnicoServicesNames: Array<String>;
  @select(['services', 'services'])
  services: Observable<Array<any>>;
  showLatestBillsTable = false;
  showActiveServicesDropdown = false;
  showTecnicoServicesDropdown = false;
  showUtenzaList = false;
  latestBills: Array<Invoice> = [];
  billMap: Map<any, any> = new Map();
  selectedBills: Array<any> = [];
  serviceUtenzaList: Array<String> = [];
  noInvoices = false;
  showNoInvoicesMsg = false;

  voceAlreadyExists: boolean;
  voceTecnAlreadyExists: boolean;

  otpSubscription: Subscription;

  constructor(private attachmentService: AttachmentService,
              private segnalazioniService: SegnalazioniService, private formBuilder: FormBuilder,
              private emailService: EmailService, private otpService: OtpService,
              private dialogModalActions: DialogModalActions,
              private modalInputEmitActions: ModalInputEmitActions,
              private _userService: UserServicesService, private invoiceService: InvoiceService,
              public datepipe: DatePipe) {

    this.services.subscribe(services => {
      this.activeServices = _userService.getActiveServices(services);
      this.activeServicesNames = Object.keys(this.activeServices);
      this.activeServicesNames.forEach((value, index) => {
        if (value === ServiceType.AMAZON) {
          this.activeServicesNames = this.activeServicesNames.filter(f => f !== value);
        }
      });
      this.tempActiveServicesNames = this.activeServicesNames.filter((element, index) => {
        if (element === 'VOCE' || element === 'VOIP' || element === 'WLR') {
          if (!this.voceAlreadyExists) {
            this.voceAlreadyExists = true;
            return true;
          }
          return false;
        }
        return true;
      });

      this.tecnicoServicesNames = this.activeServicesNames.filter(value => value !== 'ENERGIA' && value !== 'AMAZON' && value !== 'GAS');
      this.tempTecnicoServicesNames = this.tecnicoServicesNames.filter((element, index) => {
        if (element === 'VOCE' || element === 'VOIP' || element === 'WLR') {
          if (!this.voceTecnAlreadyExists) {
            this.voceTecnAlreadyExists = true;
            return true;
          }
          return false;
        }
        return true;
      });
    });
    this.invoiceService.getInvoiceData().subscribe(invoices => {
      if (invoices && invoices.length) {
        for (let i = invoices.length - 1; i > invoices.length - 11; i--) {
          this.latestBills.push(invoices[i]);
          this.billMap.set(invoices[i].id, {
            fattura: invoices[i].numeroFattura,
            dataFattura: invoices[i].startDate,
            totale: invoices[i].total
          });
        }
      } else {
        this.noInvoices = true;
      }
    });
    this.formGroupEmail = this.formBuilder.group({
      message: ['', [Validators.required, Validators.minLength(5)]],
      subject: ['', [Validators.required, Validators.minLength(2)]],
      category: ['', [Validators.required]],
      utNumber: ['', [Validators.required]],
      files: [null]
    });

    this.$userInfo.subscribe(user => {
      if (user) {
        this.userNumber =
          user.mobileNumber && (user.mobileNumber.startsWith('3') || user.mobileNumber.startsWith('00393'))
            ? user.mobileNumber
            : user.phoneNumber && (user.phoneNumber.startsWith('3') || user.phoneNumber.startsWith('00393'))
            ? user.phoneNumber
            : null;
        this.userEmail = user.email;
      }
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }

  onCheckChange(event) {
    const formArray: FormArray = this.formGroupEmail.get('myChoices') as FormArray;
    /* Selected */
    if (event.target.checked) {
      // Add a new control in the arrayForm
      formArray.push(new FormControl(event.target.value));
      this.selectedBills.push(this.billMap.get(parseInt(event.target.value)));

    }
    /* unselected */
    else {
      // find the unselected element
      let i = 0;

      formArray.controls.forEach((ctrl: FormControl) => {
        if (ctrl.value == event.target.value) {
          // Remove the unselected element from the arrayForm
          formArray.removeAt(i);
          this.selectedBills.splice(i, 1);
          return;
        }
        i++;
      });
    }
  }

  onEmailFileChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length
      && this.emailFilesAfterValidation.length < 5) {
      currentFile = event.target.files[0];
      for (let i = 0; i < this.emailFilesAfterValidation.length; i++) {
        if (this.emailFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.emailFilesBeforeValidation.push(currentFile);
      this.emailFilesAfterValidation.push(currentFile);

      if (currentFile.size > 5242880) {
        this.emailIndexesOfFilesWithWrongSize.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateFile(currentFile.name)) {
        this.emailIndexesOfFilesWithWrongExtension.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      }

      const reader = new FileReader();
      if (this.emailFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
      reader.onload = () => {
        this.formGroupEmail.get('files').patchValue(this.emailFilesAfterValidation);
      };
    }
    event.target.value = '';
  }


  sendEmail(msg) {
    let billString = '';
    if (!this.formGroupEmail.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupEmail);
    } else {
      if (this.formGroupEmail.get('files').value) {
        const formData = new FormData();
        for (let i = 0; i < this.formGroupEmail.get('files').value.length; i++) {
          formData.append('files', this.formGroupEmail.get('files').value[i]);
        }
        formData.append('subjectHeader', 'Segnalazione da area clienti del cliente ' + localStorage.getItem('clientId'));
        formData.append('subject', 'Tipo segnalazione -> ' + this.formGroupEmail.get('subject').value);
        formData.append('message', this.formGroupEmail.get('message').value);
        if (this.selectedBills && this.selectedBills.length) {
          this.selectedBills.forEach(bill => {
            billString += 'Fattura: ' + bill.fattura + ' ' + 'Data fattura: '
              + this.datepipe.transform(bill.dataFattura, 'yyyy-MM-dd') + ' ' + 'Totale: ' + bill.totale + '\n';
          });
          formData.append('bills', billString);
        }
        if (!this.showLatestBillsTable) {
          formData.append('category', 'Servizio -> ' + this.formGroupEmail.get('category').value);
          formData.append('utNumber', 'Utenza -> ' + this.formGroupEmail.get('utNumber').value);
        }
        formData.append('userType', localStorage.getItem('sottotipoCluster'));
        this.emailService.sendEmail(formData).subscribe(() => {
            this.notification.nativeElement.style.display = 'block';
            this.msg = msg;
          },
          error1 => {
            this.notification.nativeElement.style.display = 'block';
            this.notification.nativeElement.style.color = 'red';
            this.msg = 'si è verificato un problema con l\'invio della mail si prega di riprovare più tardi';
          });
      } else {
        const emailMessage = new EmailMessage();
        emailMessage.message = this.formGroupEmail.get('message').value;
        emailMessage.subjectHeader = 'Segnalazione da area clienti del cliente ' + localStorage.getItem('clientId');
        emailMessage.subject = 'Tipo segnalazione -> ' + this.formGroupEmail.get('subject').value;
        if (this.selectedBills && this.selectedBills.length) {
          this.selectedBills.forEach(bill => {
            billString += 'Fattura: ' + bill.fattura + ' ' + 'Data fattura: '
              + this.datepipe.transform(bill.dataFattura, 'yyyy-MM-dd') + ' ' + 'Totale: ' + bill.totale + '\n';
          });
          emailMessage.bills = billString;
        }
        if (!this.showLatestBillsTable) {
          emailMessage.category = this.formGroupEmail.get('category').value;
          emailMessage.utNumber = this.formGroupEmail.get('utNumber').value;
        }
        emailMessage.userType = localStorage.getItem('sottotipoCluster');
        this.emailService.sendEmail(emailMessage).subscribe(() => {
            this.notification.nativeElement.style.display = 'block';
            this.msg = msg;
          },
          error1 => {
            this.notification.nativeElement.style.display = 'block';
            this.notification.nativeElement.style.color = 'red';
            this.msg = 'si è verificato un problema con l\'invio della mail si prega di riprovare più tardi';
          });
      }
      this.emailFilesInput.nativeElement.value = '';
      this.formGroupEmail.reset();
      this.resetFilesVariablesForEmail();
    }
  }

  preSendEmail() {
    if (!this.userEmail && !this.userNumber) {
      this.dialogModalActions.showDialogModal(modalWithNoPhoneAndEmail);
    } else {
      if (this.formGroupEmail.get('files').value) {
        this.otpService.sendOTP().subscribe();
        this.dialogModalActions.showDialogModal(openOTPModal(this.otpService, this.userNumber, this.userEmail));
        this.otpSubscription = this.$enteredOtpCorrectness.subscribe(
          value => {
            if (value) {
              this.modalInputEmitActions.emitInputFieldValue(false);
              this.sendEmail('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
            }
          },
          error1 => {
            this.otpSubscription.unsubscribe();
            this.notification.nativeElement.style.display = 'block';
            this.notification.nativeElement.style.color = 'red';
            this.msg = 'si è verificato un problema con l\'invio della mail si prega di riprovare più tardi';
          },
          () => {
            this.otpSubscription.unsubscribe();
          });
        return;
      }
      this.sendEmail('Grazie per averci contattato.\n Il servizio clienti Optima risponderà il prima possibile');
    }
  }


  removeFileFromEmailForm(beforeValIndex: number, afterValIndex: number) {
    this.emailFilesBeforeValidation.splice(beforeValIndex, 1);
    this.emailFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongSize.length; i++) {
      if (this.emailIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.emailIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongExtension[i]--;
      }
    }
    this.formGroupEmail.get('files').patchValue(this.emailFilesAfterValidation);
  }


  resetFilesVariablesForEmail() {
    this.emailFilesBeforeValidation = [];
    this.emailFilesAfterValidation = [];
    this.emailIndexesOfFilesWithWrongExtension = [];
    this.emailIndexesOfFilesWithWrongSize = [];
    this.showLatestBillsTable = false;
    this.showActiveServicesDropdown = false;
    this.showTecnicoServicesDropdown = false;
  }

  validateFile(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf' || ext.toLowerCase() === 'jpg' || ext.toLowerCase() === 'jpeg';
  }

  closeWarningInEmailForm(index: number) {
    this.emailFilesBeforeValidation.splice(index, 1);
    this.emailIndexesOfFilesWithWrongSize = this.emailIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.emailIndexesOfFilesWithWrongExtension = this.emailIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  closeNotification() {
    this.notification.nativeElement.style.display = 'none';
  }

  // business part
  showBySelected(selected) {
    switch (selected) {
      case 'Amministrativa' : {
        this.showLatestBillsTable = true;
        this.showActiveServicesDropdown = false;
        this.showTecnicoServicesDropdown = false;
        this.showUtenzaList = false;
        this.formGroupEmail.removeControl('utNumber');
        this.formGroupEmail.removeControl('category');
        this.formGroupEmail.addControl('myChoices', new FormArray([], [Validators.required]));
        if (this.noInvoices) {
          this.showNoInvoicesMsg = true;
          this.formGroupEmail.removeControl('myChoices');
        }
        break;
      }
      case 'Commerciale' : {
        this.showLatestBillsTable = false;
        this.showActiveServicesDropdown = true;
        this.showTecnicoServicesDropdown = false;
        this.showUtenzaList = false;
        this.selectedBills = [];
        this.formGroupEmail.addControl('utNumber', new FormControl('', Validators.required));
        this.formGroupEmail.addControl('category', new FormControl('', Validators.required));
        this.formGroupEmail.removeControl('myChoices');
        break;
      }
      case 'Tecnico' : {
        this.showLatestBillsTable = false;
        this.showActiveServicesDropdown = false;
        this.showTecnicoServicesDropdown = true;
        this.showUtenzaList = false;
        this.selectedBills = [];
        this.formGroupEmail.addControl('utNumber', new FormControl('', Validators.required));
        this.formGroupEmail.addControl('category', new FormControl('', Validators.required));
        this.formGroupEmail.removeControl('myChoices');
        break;
      }
      default: {
        this.showLatestBillsTable = false;
        this.showActiveServicesDropdown = false;
        this.showTecnicoServicesDropdown = false;
        this.showUtenzaList = false;
        this.selectedBills = [];
        this.formGroupEmail.removeControl('myChoices');
        this.formGroupEmail.removeControl('category');
        this.formGroupEmail.removeControl('utNumber');
        break;
      }

    }
  }

  showUtenza(serviceName) {
    this.serviceUtenzaList = [];
    if (serviceName === 'VOCE' || serviceName === 'VOIP' || serviceName === 'WLR') {
      if (this.activeServicesNames.some(e => e.valueOf() === 'VOCE')) {
        this.activeServices[FissoType.VOCE].utilities.forEach(item => {
          this.serviceUtenzaList.push(item.utNumber);
        });
      }
      if (this.activeServicesNames.some(e => e.valueOf() === 'WLR')) {
        this.activeServices[FissoType.WLR].utilities.forEach(item => {
          this.serviceUtenzaList.push(item.utNumber);
        });
      }
      if (this.activeServicesNames.some(e => e.valueOf() === 'VOIP')) {
        this.activeServices[FissoType.VOIP].utilities.forEach(item => {
          this.serviceUtenzaList.push(item.utNumber);
        });
      }
      this.serviceUtenzaList = this.serviceUtenzaList.filter((element, i) => i === this.serviceUtenzaList.indexOf(element));
      this.showUtenzaList = true;
    } else {
      this.activeServices[serviceName].utilities.forEach(item => {
        this.serviceUtenzaList.push(item.utNumber);
      });
      this.showUtenzaList = true;
    }
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.otpSubscription]);
  }

}
