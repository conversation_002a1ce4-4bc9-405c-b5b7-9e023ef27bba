import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReContactLayoutComponent } from './re-contact-layout.component';

describe('ReContactLayoutComponent', () => {
  let component: ReContactLayoutComponent;
  let fixture: ComponentFixture<ReContactLayoutComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReContactLayoutComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReContactLayoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
