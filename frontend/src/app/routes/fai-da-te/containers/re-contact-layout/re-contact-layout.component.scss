@import "~app/shared/styles/colors";

.page-title {
  .title-image {
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_Telefono.png") no-repeat center;
    background-size: contain;
    width: 68px;
    height: 58px;
    float: left;
  }
}


.recontact-layout {

  .recontact-wrapper {
    margin-top: 1%;
    border: 1px solid $light-blue;
    border-radius: 5px;
  }

  .aux-top-margin{
    margin-top: 10px;
  }

  .no-side-padding {
    padding-left: 0;
    padding-right: 0;
  }

  .no-side-margin{
    margin-left: 0;
    margin-right: 0;
  }

  img {
    width: calc(100% - 7px);
    height: calc(100% - 1px);
    display: block;
    margin-left: 5px;
  }

  .expert-image {
    background: url('../../../../../assets/img/agents/esperta.jpg') no-repeat center;
    background-size: contain;
    height: 265px;
  }


  .input-row {
    margin-top: 1%;
  }

  ::ng-deep select {
    -webkit-appearance: none; /*Removes default chrome and safari style*/
    -moz-appearance: none;
    background: url(../../../../../assets/img/icons/arrow-down_16x16.png) no-repeat right white;
    background-position-x: 95%;
  }

  .unActiveText {
    color: $dark-blue;
    text-align: center;
    font-size: 20px;
  }

  .ask-for-call {
    border: 1px solid $light-blue;
    border-radius: 5px;
    min-height: 40px;
    color: $dark-blue;
    text-align: center;

    .request-title {
      margin-top: 8px;
    }
  }

  .recontact-input {
    float: left;
    margin-left: 2%;
  }

  .button-group {
    margin-bottom: 2%;

    .checkbox-container {
      float: left;
    }

    .accept-button-default {
      float: left;
    }
  }

  .accept-button-default {
    cursor: pointer;
    //margin-left: 2%;
    border-color: dimgrey;
    background: white;
    color: dimgrey;
    min-width: 72px;
  }

  & .send {
    margin-left: 2%;
    background-color: $green;
    color: white;
    border: 1px solid $green;
  }

  & .send:disabled {
    background-color: white;
    border-color: $green;
    color: $green;
  }


  .input-label {
    color: $dark-blue;
    margin-top: 5px;
    width: auto;
    float: left;
  }

  .contract-time {
    float: left;
  }

  .contact-time-info {
    position: relative;
    float: left;
    margin-left: 2%;
    display: inline-block;

    .info-circle {
      border-radius: 50%;
      border: 1px solid;
      height: 17px;
      width: 17px;
      line-height: 15px;
      font-weight: 700;
      font-size: 13px;
    }
  }

  ::ng-deep.form-control {
    color: $dark-blue;
    border-color: $light-blue;
    border-radius: 5px;
  }

  .callback-icon {
    height: 50px;
    float: left;
    width: 50px;
    fill: $dark-blue;
  }

  .time-title {
    color: $dark-blue;
    margin-bottom: 1%;
  }

  .reason-block {
    margin-top: 2%;
    border: 1px solid $light-blue;
    border-radius: 5px;
    margin-bottom: 10px;
    margin-left: 5px;

  }

  .margin-top-2 {
    margin-top: 2%;
  }

  .confirm-message-block {
    margin-top: 2%;

    p {
      text-align: center;
    }
  }
}

@media screen and (min-width: 991px) {
  .contract-time {
    width: 80%;
  }
}

@media screen and (max-width: 991px) {
  .recontact-wrapper{
    background-color: white;
  }
  .col-sm-4 {
    display: none;
  }
  .page-title {
    display: none;
  }
  .contract-time {
    width: 90%;
  }
  .no-padding-mobile {
    padding: 0;
  }
  .input-label {
    padding: 0 0 0 5px;
  }
  .contact-time-info {
    ::ng-deep.description {
      transform: translate(-82%, -100%);

      .description-arrow {
        left: 82%;
      }
    }
  }
}

@media screen and (max-width: 500px){
  .no-expert-mobile{
    display: none;
  }

  .recontact-wrapper{
    padding-right: 10px;
    padding-left: 5px;
  }

  .mobile-block{
    padding-left: 0;
  }
}
