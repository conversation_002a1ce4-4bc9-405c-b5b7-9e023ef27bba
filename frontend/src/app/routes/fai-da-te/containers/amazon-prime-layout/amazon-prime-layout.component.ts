import {Component, OnInit} from '@angular/core';
import {OptimaIconUtils} from '../../../../common/utils/OptimaIconUtils';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {DialogModalEntity} from '../../../../common/model/dialogModal/DialogModalEntity';
import {AmazonPrimeService} from '../../../../common/services/amazonprime/amazon-prime.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';

@Component({
  selector: 'app-amazon-prime-layout',
  templateUrl: './amazon-prime-layout.component.html',
  styleUrls: ['./amazon-prime-layout.component.scss']
})
export class AmazonPrimeLayoutComponent implements OnInit {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  amazonStatus: string;
  dataActivation: Date;
  dataExpiry: Date;
  costoRinnovoOffertaText: string;

  constructor(private optimaIconUtils: OptimaIconUtils,
              private dialogModalActions: DialogModalActions,
              private modalEntity: DialogModalEntity,
              private service: AmazonPrimeService) {
  }

  ngOnInit() {
    this.serviceData.subscribe(data => {
      data.services.forEach(service => {
        if (service.serviceName === 'AMAZON') {
          this.amazonStatus = service.utilities[0].status;
          this.dataActivation = new Date(service.utilities[0].startDate);
          this.dataExpiry = new Date(this.dataActivation.getFullYear() + 1, this.dataActivation.getMonth(), this.dataActivation.getDate());
        }
      });
    });

    this.service.getAmazonPrimeData(localStorage.getItem('clientId')).subscribe(result => {
      this.costoRinnovoOffertaText = result.presenzaPromozione ? 'Incluso per un anno' : '36€/anno';
    });
  }

  showAttivaModal() {
    this.modalEntity.title = 'Disattiva Amazon Prime';
    this.modalEntity.text =
      'Procedendo annullerai l\'iscrizione e rinuncerai a tutti i benefici Prime. \n' +
      '' +
      'La disattivazione avverà entro 48 ore dalla richiesta.';

    this.modalEntity.hasButtons = true;

    this.modalEntity.callbackToExecuteOnSubmit = () => {
      return this.service.unsubscribe(localStorage.getItem('clientId'));
    };

    this.modalEntity.nextEntities = [{
      img: '/assets/img/icons/ok.png',
      text: 'La tua richiesta è stata presa in carico e sarà gestita entro 48 ore. Controlla lo stato della lavorazione nella sezione “Le tue segnalazioni“',
      marker: 'amazon'
    } as DialogModalEntity];

    this.dialogModalActions.showDialogModal(this.modalEntity);
  }
}
