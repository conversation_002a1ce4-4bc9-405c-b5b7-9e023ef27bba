<div class="col-lg-12 col-md-12 page-title-style">
  <div class="title-image total-security-picture"></div>
  <div class="title-text">TOTAL SECURITY</div>
</div>

<div class="mainBlock">
  <div class="option-block">
    <label for="simList" class="no-margin">SIM associata:</label>
    <select id="simList" class="form-control">
      <option>
        SIM {{totalSecurityInfo?.additionalInfo.msisdn}}
      </option>
    </select>
  </div>
  <div class="row">
    <div class="col-lg-12">
      <p><b>Stato:</b> {{totalSecurityInfo?.status}}</p>
    </div>
  </div>
  <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
    <div class="col-lg-9">
      <p><b>Data Attivazione:</b> {{totalSecurityInfo?.additionalInfo.dateActivation | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
  <div class="row" *ngIf="totalSecurityInfo.status === 'Disattivo'">
    <div class="col-lg-9">
      <p><b>Data disattivazione:</b> {{totalSecurityInfo?.additionalInfo.dateDeactivation | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
  <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
    <div class="col-lg-12">
      <p><b>Costo:</b> {{totalSecurityInfo.additionalInfo.price}} €</p>
    </div>
  </div>
  <div class="row" *ngIf="totalSecurityInfo.status === 'Attivo'">
    <div class="col-lg-9">
      <p><b>Data rinnovo:</b> {{totalSecurityInfo?.additionalInfo.dateRenewal | date : 'dd/MM/yyyy'}}</p>
    </div>
  </div>
</div>

<button routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</button>
