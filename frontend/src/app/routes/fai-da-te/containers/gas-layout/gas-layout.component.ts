import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';
import {Subscription} from 'rxjs/Subscription';
import {select} from '@angular-redux/store';

import {ServiziAttiviService} from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import {OptimaIconUtils} from '../../../../common/utils/OptimaIconUtils';
import {HomeService} from '../../../home/<USER>/home/<USER>';
import {Utility} from '../../../../common/model/services/userServices.model';
import {GasService} from '../../../../common/services/gas/gas.service';
import {PodDetail} from '../../../../common/model/gas/PodDetail';
import {UserData} from '../../../../common/model/userData.model';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {ServicesActions} from '../../../../redux/services/actions';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';

@Component({
  selector: 'app-gas-layout',
  templateUrl: './gas-layout.component.html',
  styleUrls: ['./gas-layout.component.scss']
})
export class GasLayoutComponent implements OnInit, OnDestroy {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;

  @select(['services', 'gasPodDetails'])
  additionalPodDetails: Observable<object>;

  podDetails: Array<PodDetail>;
  userCluster: string;

  pdrAdditionalDetails: object;

  pod = new BehaviorSubject(null);

  pdf: Array<any>;

  utenza: Utility = {} as Utility;

  additionalPodDetailsSubscription: Subscription;

  serviceDataSubscription: Subscription;

  constructor(private route: ActivatedRoute, private gasService: GasService,
              private serviziAttiviService: ServiziAttiviService,
              private servicesActions: ServicesActions,
              private optimaIconUtils: OptimaIconUtils, private homeServices: HomeService) {
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
        this.pdf = this.setPDFList('GAS');
      }
    });
    this.additionalPodDetailsSubscription = this.additionalPodDetails.subscribe(details => {
      this.pdrAdditionalDetails = details;
    });
    this.route.params.subscribe(params => {
      if (params.pod) {
        this.pod.next(params.pod);
      }
    });
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      const {activeServices, servicesLoaded} = serviceState;
      if (servicesLoaded) {
        this.servicesActions.loadGasAdditionalDetailsIfNotExist('GAS');
      }
      Object.keys(activeServices).forEach(key => {
        activeServices[key].utilities.forEach(utility => {
          if (utility.utNumber === this.pod.getValue()) {
            this.utenza = utility;
          }
        });
      });
    });

    this.pod.flatMap((pod) => {
      return this.gasService.getPodDetails(pod, localStorage.getItem('clientId'));
    }).subscribe(podDetails => {
      this.podDetails = podDetails;
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }

  serviceIcon(icon: string): string {
    return this.optimaIconUtils.getServiceIconByName(icon);
  }

  getName(name) {
    return this.serviziAttiviService.getNumberName(name);
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.serviceDataSubscription,
      this.additionalPodDetailsSubscription]);
  }


}
