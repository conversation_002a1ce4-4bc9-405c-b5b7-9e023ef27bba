import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';

@Component({
  selector: 'app-medical-teleconsultation-layout',
  templateUrl: './medical-teleconsultation-layout.component.html',
  styleUrls: ['./medical-teleconsultation-layout.component.scss']
})
export class MedicalTeleconsultationLayoutComponent implements OnInit {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  status: string;
  dataActivation: Date;

  constructor() {
    this.serviceData.subscribe(data => {
      data.services.forEach(service => {
        if (service.serviceName === 'Teleconsulto medico') {
          this.status = service.utilities[0].status;
          this.dataActivation = new Date(service.utilities[0].startDate);
        }
      });
    });
  }

  ngOnInit() {
  }
}
