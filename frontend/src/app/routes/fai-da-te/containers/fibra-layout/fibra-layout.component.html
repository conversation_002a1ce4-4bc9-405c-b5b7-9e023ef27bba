<div class="col-md-12 block-md-12 outBox">
  <div class="row app--header-block">
    <div class=" block-md-3 topBlock head" [ngClass]="[serviceIcon(serviceName), 'service-icon']"></div>
    <div *ngIf="serviceName!=='ADSL'" class="serviceName">{{serviceName}}</div>
    <div *ngIf="serviceName==='ADSL'" class="serviceName">INTERNET</div>

  </div>
  <div class="mainBlock">
    <div class="row">
      <div class="col-lg-12">
        <p>
          <b>Stato:</b> {{utenza.status}}</p>
        <p>
          <b>{{getName(serviceName)}} </b> {{utenza.utNumber}}</p>
      </div>
    </div>
    <div class="row ">
      <div class="col-lg-9">
        <p *ngIf="utenza.status==='ATTIVATO'">
          <b>Data Attivazione:</b> {{utenza.startDate | date : 'dd/MM/yyyy'}}</p>
        <p *ngIf="utenza.status==='IN_ATTIVAZIONE'">
          <b>Data Prevista Attivazione:</b> {{utenza.firstActivationDate | date : 'dd/MM/yyyy'}}</p>
        <div *ngIf="podDetails && podDetails[utenza.utNumber]">
          <p><b>Tipologia linea: </b>{{podDetails[utenza.utNumber].descrizioneTipoLinea}}</p>
          <p *ngIf="podDetails[utenza.utNumber]?.sede"><b>Indirizzo fornitura:
          </b>{{podDetails[utenza.utNumber].sede.cap}}, {{podDetails[utenza.utNumber].sede.descrizioneSede}}</p>
          <p *ngIf="podDetails[utenza.utNumber].codiceMigrazione"><b>Codice
            migrazione: </b>{{podDetails[utenza.utNumber].codiceMigrazione}}</p>
          <!--<p *ngIf="podDetails[utenza.utNumber].tipoxDSL"><b>Tipologia servizio: </b>
            {{podDetails[utenza.utNumber].tipoxDSL}}</p>-->
          <p *ngIf="podDetails[utenza.utNumber].tipoxDSL">
            <b>Tipologia servizio: </b>{{decodeType(podDetails[utenza.utNumber].profiloProdottoAttivo)}}</p>
          <!-- <p *ngIf="podDetails[utenza.utNumber].profiloProdottoAttivo">
             <b>Profilo Internet: </b>{{decodeProfile(podDetails[utenza.utNumber].profiloProdottoAttivo)}}</p>-->
        </div>
      </div>
    </div>

    <div *ngIf="pdf" class="app-drop-down">
      <a mat-button class="dropdown dontShow" [matMenuTriggerFor]="menu" [ngClass]="{ 'disabled' :condition }">
        <div class="icon modifica"></div>
      </a>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below" [overlapTrigger]="false">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button mat-menu-item class="menu-button odds-bg" *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">
            {{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
    </div>
  </div>
  <div class="serviziButton">
    <a class="elenco" routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</a>
  </div>
</div>
