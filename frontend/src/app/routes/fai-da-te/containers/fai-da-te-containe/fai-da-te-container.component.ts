import {AfterViewChecked, ChangeDetectorRef, Component} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ConstantUtil} from '../../../../utils/ConstantUtil';
@Component({
  selector: 'app-fai-da-te-container',
  templateUrl: 'fai-da-te-container.component.html',
  styleUrls: ['fai-da-te-container.component.scss']
})
export class FaiDaTeContainerComponent implements AfterViewChecked {

  isMobile = ConstantUtil.isMobile;

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  constructor(private cdRef: ChangeDetectorRef) {
  }

  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }

}
