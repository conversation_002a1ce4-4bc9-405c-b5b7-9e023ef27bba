<div class="col-md-12 block-md-12 outBox">
  <div class="row app--header-block">
    <div class=" block-md-3 topBlock head" [ngClass]="[serviceIcon('ENERGIA'), 'service-icon']"></div>
    <div class="serviceName">LUCE</div>
  </div>
  <div class="mainBlock">
    <div class="row">
      <div class="col-lg-12">
        <p>
          <b>Stato:</b> {{utenza.status}}</p>
        <p>
          <b>{{getName('ENERGIA')}} </b> {{utenza.utNumber}}</p>
      </div>
    </div>
    <div class="row ">
      <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12">
        <p *ngIf="utenza.status==='ATTIVATO'">
          <b>Data Attivazione:</b> {{utenza.startDate | date : 'dd/MM/yyyy'}}</p>
        <!--<p *ngIf="utenza.status==='IN_ATTIVAZIONE' && utenza.firstActivationDate">
          <b>Data Prevista Attivazione:</b> {{utenza.firstActivationDate | date : 'dd/MM/yyyy'}}</p>-->
        <div *ngIf="podDetails&&podDetails[utenza.utNumber]">
          <!--<p><b> Tipologia contratto: </b>{{podDetails[0].tipoUso}}</p>-->
          <p><b> Potenza contatore:</b> {{podDetails[utenza.utNumber].potDisp}} Kw</p>
          <p *ngIf="podDetails[utenza.utNumber].tipoUso"><b> Tipologia
            d'uso: </b>{{podDetails[utenza.utNumber].tipoUso}}
          </p>
          <p><b> Indirizzo fornitura: </b>{{ podDetails[utenza.utNumber].sedeOperativa }}</p>
          <!--<p><b> Offerta*:</b> TAG_NOME OFFERTA</p>-->
          <!--<p><b>Data inizio validità offerta: </b>{{podDetails[0].inizioValidita | dotNetDate | date :
            'dd/MM/yyyy'}} </p>-->
          <p>
            <b>Tipo Contatore: </b>{{podDetails[utenza.utNumber].tipo}}</p>
        </div>
      </div>
    </div>
    <div *ngIf="pdf" class="app-drop-down">
      <a mat-button class="dropdown dontShow" [matMenuTriggerFor]="menu" [ngClass]="{ 'disabled' :condition }">
        <div class="icon modifica"></div>
      </a>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below" [overlapTrigger]="false">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button mat-menu-item class="menu-button odds-bg" *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">
            {{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
    </div>

  </div>
  <div class="serviziButton">
    <a class="elenco" routerLink='/faidate/servizi-attivi'>VAI ALL’ELENCO DEI TUOI SERVIZI</a>
  </div>
</div>
