import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { select } from '@angular-redux/store';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { Utility } from '../../../../common/model/services/userServices.model';
import { ServiziAttiviService } from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import { OptimaIconUtils } from '../../../../common/utils/OptimaIconUtils';
import { HomeService } from '../../../home/<USER>/home/<USER>';
import { UserData } from '../../../../common/model/userData.model';
import ServiceStateModel from "../../../../redux/model/ServiceStateModel";
import { ServicesActions } from "../../../../redux/services/actions";

@Component({
  selector: 'app-voice-layout',
  templateUrl: './voice-layout.component.html',
  styleUrls: ['./voice-layout.component.scss']
})
export class VoiceLayoutComponent implements OnInit, OnDestroy {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;

  @select(['services', 'fissoPodDetails'])
  fissoPodDetails: Observable<object>;

  fissoPodDetailsSubscription: Subscription;

  serviceDataSubscription: Subscription;

  podDetails: object;

  pod: string;

  pdf: Array<any>;

  userCluster: string;

  utenza: Utility = {} as Utility;

  serviceName = 'VOCE';

  constructor(private route: ActivatedRoute, private serviziAttiviService: ServiziAttiviService,
              private optimaIconUtils: OptimaIconUtils, private homeServices: HomeService,
              private servicesActions: ServicesActions) {
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
        this.pdf = this.setPDFList('WLR');
      }
    });
    this.fissoPodDetailsSubscription = this.fissoPodDetails.subscribe(details => {
      this.podDetails = details;
    });
    this.route.params.subscribe(params => {
      if (params.pod) {
        this.pod = params.pod;
      }
    });
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      const { activeServices, servicesLoaded } = serviceState;
      if (servicesLoaded) {
        this.servicesActions.loadFissoPodDetailsIfNotExist();
      }
      Object.keys(activeServices).forEach(key => {
        activeServices[key].utilities.forEach(utility => {
          if (utility.utNumber === this.pod) {
            this.serviceName = activeServices[key].serviceName;
            this.utenza = utility;
          }
        });
      });
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }

  serviceIcon(icon: string): string {
    return this.optimaIconUtils.getServiceIconByName(icon);
  }

  getName(name) {
    return this.serviziAttiviService.getNumberName(name);
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  ngOnDestroy(): void {
    if (this.fissoPodDetailsSubscription) {
      this.fissoPodDetailsSubscription.unsubscribe();
    }
  }

}
