import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import 'rxjs/add/observable/forkJoin';
import {RouterData} from '../../../../../common/model/router/routerData';
import {PodDetail} from '../../../../../common/model/fisso/PodDetail';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../../common/utils/ObservableUtils';
import {ActiveServiceStatus} from '../../../../../common/enum/ServiceStatus';


@Component({
  selector: 'app-configurazione-router',
  templateUrl: './configurazione-router.component.html',
  styleUrls: ['./configurazione-router.component.scss']
})
export class ConfigurazioneRouterComponent implements OnDestroy, OnInit {
  @select(['services', 'fissoPodDetails'])
  fissoPodDetails: Observable<Array<PodDetail>>;
  @select(['services', 'routerInfo'])
  routerInfo: Observable<any>;
  public allLinee: Array<RouterData> = [];
  public selectedLinee: RouterData;
  private servicesSubscribe: Subscription;
  private fissoServiceSubscribe: Subscription;
  vlanId: Array<any>;
  voipData: Array<PodDetail>;

  constructor() {
    this.voipData = [];
    this.vlanId = [];
    this.getData();
  }

  ngOnInit() {
  }

  private hasVoip() {
    this.fissoServiceSubscribe = this.fissoPodDetails.map(data => {
      return Object.values(data).filter(detail => this.selectedLinee &&
        detail.tipoContratto.toUpperCase().includes('VOIP') &&
        // detail.idAdsl === this.selectedLinee.id &&
        ActiveServiceStatus[detail.stato.toUpperCase()]);
    }).subscribe(
      data => {
        this.voipData = data;
        data.forEach(item => {
          if (!this.vlanId.includes(item.vlanId)) {
            this.vlanId.push(item.vlanId);
          }
        });
      });
  }

  private getData() {
    this.servicesSubscribe = this.routerInfo.subscribe(data => {
      if (data && Object.values(data).length > 0) {
        this.allLinee = data['LineeADSL'].filter(linea =>
          !!ActiveServiceStatus[linea.stato.toUpperCase()]);
        if (this.allLinee.length) {
          this.selectedLinee = this.allLinee[0];
        }
      }
      this.hasVoip();
    });
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.fissoServiceSubscribe, this.servicesSubscribe]);
  }
}

