<div class='col-xs-12 col-sm-12 col-sm-12 col-xs-12 no-padding' >
  <p class='sub-title sub-block'>
    Per garantire il funzionamento del servizio VOIP, creare una seconda connessione WAN utilizzata per il
    traffico telefonico con i seguenti parametri:
  </p>
  <section class='row block'>
    <p class="name"><b>Connessione PPP over Ethernet over ATM (PPPoE) sulla linea ADSL</b></p>
    <div>
      <div class="router-data">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>VPI</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box disabled" id="vpi2">8 </p>
      </div>
      <div class="router-data">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>VCI</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3  box disabled" id="vci2" disabled>36 </p>
      </div>
      <div class="router-data mobile-md">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>Encapsulation</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box disabled" id="encapsulation2">LLC </p>
      </div>
      <div class="router-data mobile-xs">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>Username PPP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box" id="username2" type="text">
          {{voipData[0].credenzialiRadius && voipData[0].credenzialiRadius.username ? voipData[0].credenzialiRadius.username : 'username'}}
        </p>
      </div>
      <div class="router-data mobile-xs">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>Password PPP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box" id="password2" type="text">
          {{voipData[0].credenzialiRadius && voipData[0].credenzialiRadius.password ? voipData[0].credenzialiRadius.password : 'password'}}</p>
      </div>
    </div>

  </section>
  <div>
  </div>
</div>
