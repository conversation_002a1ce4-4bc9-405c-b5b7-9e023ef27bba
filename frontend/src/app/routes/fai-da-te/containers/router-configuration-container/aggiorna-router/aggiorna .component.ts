import { Component, OnInit } from '@angular/core';
import 'rxjs/add/observable/forkJoin';
import { HttpClient } from '@angular/common/http';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '../../../../../common/services/notification/notification.service';
import { firmwareDocuments, routerFirmwareNotification } from '../../../config/config';
import { FormUtils } from '../../../../../common/utils/FormUtils';


@Component({
  selector: 'app-configurazione-router',
  templateUrl: './aggiorna.component.html',
  styleUrls: ['./aggiorna.component.scss']
})
export class AggiornaComponent implements OnInit {

  documentOptions = Object.keys(firmwareDocuments);

  documents = firmwareDocuments;

  formGroup: FormGroup;

  constructor(private httpClient: HttpClient, private formBuilder: FormBuilder, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.formGroup = this.formBuilder.group({
      documentVersion: [null, Validators.required]
    });
  }

  download(name) {
    return this.httpClient.get(`/api/routerAggiornaPdf`, {
      headers: {'fileName': name},
      responseType: 'blob'
    });
  }

  downloadFile() {
    const firmwareVersion = this.formGroup.value.documentVersion;
    if (firmwareVersion && firmwareDocuments[firmwareVersion]) {
      const firmwareInfo = firmwareDocuments[firmwareVersion];
      return this.download(firmwareInfo.pdf).subscribe(data => {
          this.notificationService.successMessage(routerFirmwareNotification.success);
          const file = new Blob([data], {type: 'application/pdf'});
          if (navigator.appVersion.toString().indexOf('.NET') > 0) { // for IE browser
            window.navigator.msSaveBlob(file, firmwareInfo.pdf);
          } else {
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(file);
            link.download = firmwareInfo.pdf;
            link.click();
          }
        },
        () => this.notificationService.errorMessage(routerFirmwareNotification.error));
    }
    FormUtils.setFormControlsAsTouched(this.formGroup);
  }

  downloadSIGFile() {
    const firmwareVersion = this.formGroup.value.documentVersion;
    if (firmwareVersion && firmwareDocuments[firmwareVersion]) {
      const firmwareInfo = firmwareDocuments[firmwareVersion];
      return this.download(firmwareInfo.sig).subscribe(data => {
        this.notificationService.successMessage(routerFirmwareNotification.success);
        const file = new Blob([data], {type: 'application/octet-stream'});
        if (navigator.appVersion.toString().indexOf('.NET') > 0) { // for IE browser
          window.navigator.msSaveBlob(file, firmwareInfo.sig);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(file);
          link.download = firmwareInfo.sig;
          link.click();
        }
      }, () => this.notificationService.errorMessage(routerFirmwareNotification.error));
    }
    FormUtils.setFormControlsAsTouched(this.formGroup);
  }

}

