import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';

@Component({
  selector: 'app-no-voip-router',
  templateUrl: './no-voip-router.component.html',
  styleUrls: ['./no-voip-router.component.scss']
})
export class NoVoipRouterComponent {
  @Input('username') username: string;
  @Input('password') password: string;

  constructor() {
  }

  title: string;

  @Input('type')
  set type(type: string) {
    this.title = type === 'FTTH' ? 'Connessione PPP over Ethernet (PPPoE) sulla porta GbE WAN' :
      'Connessione PPP over Ethernet over ATM (PPPoE) sulla linea ADSL';
  }

}
