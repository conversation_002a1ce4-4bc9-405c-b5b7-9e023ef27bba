@import "~app/shared/styles/colors";

.container {
  width: 95%;
  margin: auto;
  color: $dark-blue;
  padding: 0;
}

.block {
  border-radius: 10px;
  width: 100%;
  margin: auto;
  background-color: $menu-background;
  border: 1px solid $menu-border;
  padding: 10px 20px 20px 10px;
}

.sub-block {
  margin-top: 20px;
}

.sub-title {
  font-size: 16px;
}

.left-padding {
  margin-left: 0;
}

.box {
  min-height: 35px;
  padding: 5px;
  background: white;
  border-radius: 5px;
  border: 2px solid $menu-border;
  color: #646464;
  word-wrap: break-word;
}

.box-title {
  & :nth-child(2) {
    text-align: right !important;
  }
}

.disabled {
  background-color: #eeeeee;
  border: 1px solid #d6d6d6;
  color: #000;
}

.name {
  padding: 0 0 10.5px 15px;
}


@media screen and (max-width: 991px) {
  .container {
    width: 100%;
    padding: 0;
  }
  .block {
    border: none;
    padding: 10px 0;
  }
  .name {
    padding-left: 0;
  }
  .box-title {
    padding-left: 0;
    width: auto;
  }
  .router-data {
    float: left;
    width: 50%;

    .box {
      width: 65%;
    }

    .box-title {
      margin-top: 5px;
    }
  }
  .voip-data {
    .box-title {
      width: 30%;
      margin-top: 5px;
    }

    .box {
      width: 70%;
    }
  }
}

@media screen and (max-width: 700px) {
  .sub-title{
    font-size: 14px;
  }
  .mobile-md {
    width: 100%;
    .box {
      width: 30%;
    }
  }
  .mobile-xs {
    width: 100%;

    .box-title {
      width: 25%;
    }

    .box {
      width: 75%;
    }
  }
  .voip-data {
    .box-title {
      width: 40%;
    }

    .box {
      width: 60%;
    }
  }
}

@media screen and (max-width: 481px) {
  .mobile-xs {

    .box-title {
      width: 35%;
    }

    .box {
      width: 65%;
    }
  }
  .voip-data {
    .box-title {
      width: 45%;
      padding-right: 5px
    }

    .box {
      width: 55%;
    }
  }
}
