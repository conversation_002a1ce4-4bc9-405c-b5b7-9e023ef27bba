import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {RouterData} from '../../../../../common/model/router/routerData';
import {PodDetail} from '../../../../../common/model/fisso/PodDetail';

@Component({
  selector: 'app-router-container',
  templateUrl: './router-container.component.html',
  styleUrls: ['./router-container.component.scss']
})
export class RouterContainerComponent implements OnInit, OnChanges {

  @Input('selectedLinee') selectedLinee: RouterData;
  @Input('VOIP') VOIP: Array<PodDetail>;
  @Input('vlanId') vlanId: Array<any>;

  constructor() {
  }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges): void {
    // this.VOIP = this.VOIP.filter(data =>  data['idAdsl'] === this.selectedLinee.id);
  }

  public hasParam(param) {
    return this.selectedLinee.tipoxDSL.toUpperCase() === param.toUpperCase();

  }
}
