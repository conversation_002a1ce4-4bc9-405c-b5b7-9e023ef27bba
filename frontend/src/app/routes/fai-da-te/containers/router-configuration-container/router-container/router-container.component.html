<div *ngIf="(hasParam('ADSL Bitstream') || has<PERSON>aram('ADSL EasyIp'))" class="container">
  <app-no-voip-router [username]="selectedLinee.username" [password]="selectedLinee.psw" [type]="selectedLinee.tipoxDSL"></app-no-voip-router>
</div>
<div *ngIf="(hasParam('ADSL Bitstream') || hasParam('ADSL EasyIp') ) " class="container">
  <!--'ADSL EasyIp' was absent -->
  <app-no-voip-router [username]="selectedLinee.username" [password]="selectedLinee.psw" [type]="selectedLinee.tipoxDSL"></app-no-voip-router>
  <app-has-voip-router *ngIf="VOIP && VOIP.length" [username]="selectedLinee.username" [password]="selectedLinee.psw" [voipData]="VOIP"></app-has-voip-router>
  <app-voip-adslrouter *ngIf="VOIP && VOIP.length" [username]="selectedLinee.username"
                       [password]="selectedLinee.psw"
                       [adslId]="selectedLinee.id"
                       [voipData]="VOIP"></app-voip-adslrouter>
</div>
<div *ngIf="(hasParam('fibra') || hasParam('FTTH')) " class="container"><!--'ADSL EasyIp' was absent -->
  <app-no-adslrouter [username]="selectedLinee.username" [password]="selectedLinee.psw" [type]="selectedLinee.tipoxDSL"></app-no-adslrouter>
  <app-has-voip-router-fibra *ngIf="VOIP && VOIP.length" [username]="selectedLinee.username"
                             [password]="selectedLinee.psw"
                             [vlanId]="vlanId"
                             [voipData]="VOIP"></app-has-voip-router-fibra>
  <app-voip-adslrouter *ngIf="VOIP && VOIP.length" [username]="selectedLinee.username"
                       [password]="selectedLinee.psw"
                       [adslId]="selectedLinee.id"
                       [voipData]="VOIP"
  ></app-voip-adslrouter>
</div>
<div *ngIf="(hasParam('fibra'))" class="container">
  <app-no-adslrouter [username]="selectedLinee.username" [password]="selectedLinee.psw" [type]="selectedLinee.tipoxDSL"></app-no-adslrouter>
</div>
<div *ngIf="!(this.hasParam('ADSL Bitstream') || hasParam('ADSL EasyIp')|| hasParam('fibra') || hasParam('FTTH'))" class="container">
  <app-no-adslrouter [username]="selectedLinee.username" [password]="selectedLinee.psw" [type]="selectedLinee.tipoxDSL"></app-no-adslrouter>
</div>
<div *ngIf="(VOIP && VOIP.length) && !(hasParam('ADSL Bitstream') || hasParam('ADSL EasyIp') || hasParam('fibra') || hasParam('FTTH'))"
     class="container">
  <!--'ADSL EasyIp' was absent -->
  <app-voip-adslrouter [username]="selectedLinee.username"
                       [password]="selectedLinee.psw"
                       [adslId]="selectedLinee.id"
                       [voipData]="VOIP"></app-voip-adslrouter>
</div>
