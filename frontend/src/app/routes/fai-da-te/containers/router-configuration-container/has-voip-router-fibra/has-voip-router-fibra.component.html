<div class='col-xs-12 col-sm-12 col-sm-12 col-xs-12 no-padding'>
  <p class='sub-title sub-block'>
    Per garantire il funzionamento del servizio VOIP, creare una seconda connessione WAN utilizzata per il traffico
    telefonico con i seguenti parametri:
  </p>
  <section class='row block'>
    <p class="name"><b>Conessione PPP over Ethernet (PPPoE) sulla porta GbE WAN</b></p>
    <div>
      <div class="router-data">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>VLAN ID</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3  box disabled " id="vlan"><span
          *ngFor="let id of vlanId">{{id ? id : 837}} </span>
        </p>
      </div>
      <div class="router-data mobile-xs">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>Username PPP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box " id="username11">
          {{voipData[0].credenzialiRadius && voipData[0].credenzialiRadius.username ? voipData[0].credenzialiRadius.username : 'username'}}</p>
      </div>
      <div class="router-data mobile-xs">
        <div class="col-xs-12 col-sm-12 col-md-3 box-title"><b>Password PPP</b></div>
        <p class="col-xs-12 col-sm-12 col-md-3 box" id="password11">
          {{voipData[0].credenzialiRadius && voipData[0].credenzialiRadius.password ? voipData[0].credenzialiRadius.password : 'password'}}
        </p>
      </div>
    </div>

  </section>
  <div>
  </div>
</div>
