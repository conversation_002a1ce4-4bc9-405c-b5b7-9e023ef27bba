@import "~app/shared/styles/colors";

.deferments-button {
  background-color: $dark-blue;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  border-radius: 3px;
}

.invoice-table {
  margin-top: 2%;
  padding-left: 25px;
  padding-right: 25px;
  padding-bottom: 10px;

  .table-header, .table-body {
    display: inline-block;
    float: left;
    width: 11.87%;
    min-height: 20px;
    text-align: center;
  }

  .table-header-business {
    width: 9.87%;
    background-color: white !important;
  }

  .table-body-business {
    width: 9.87%;
  }

  .checkbox-block {
    width: 5%;
  }

  .arrows-mobile {
    display: none;
  }

  .checkbox-container {
    padding-left: 20px;
  }

  .table-body {
    padding-top: 10px;
    padding-bottom: 10px;

    .table-body-header {
      display: none;
    }
  }

  .table-header {
    padding-top: 10px;
    min-height: 40px;
  }

  .table-row {
    float: left;
    width: 100%;
  }

  .icon-container {
    padding-top: 4px;
  }
}

.invoice-table.bordered > div:nth-child(2n+10) {
  background-color: $menu-background;
}

.bordered{
  border-radius: 10px;
}

.success {
  background: url("/assets/img/optimaIcons/success.png") no-repeat center;
  background-size: contain;
}

.icon {
  height: 20px;
  padding: 0 20px;
  text-align: left;
}

.icon-pdf-load {
  width: 100%;
  color: #36749d;
  font-size: 34px;
  cursor: pointer;
  line-height: 0;
}

.paginator {
  margin-top: 5px;
  padding-bottom: 10px;
  border-bottom: 1px solid #c8d9e9;
  margin-bottom: 2%;

  ::ng-deep .paginator {
    .navigation {
      margin-right: 0;
    }
  }
}
@media screen and (max-width: 991px) {
  .invoice-table {
    padding-left: 5px;
    padding-right: 5px;

    .table-header-business{
      width: 10.6%;
    }

    .checkbox-container {
      padding-left: 0;
    }

    .table-body {
      width: 12.12%;
    }

    .table-body-business {
      width: 10.77%;
    }
    .checkbox-block {
      width: 3%;
    }
  }
}
@media screen and (max-width: 800px) {
  .invoice-table {
    padding-left: 2%;
    padding-right: 2%;

    .arrows-mobile {
      display: block;
      float: right;

      span {
        font-size: 26px;
        margin-left: 15px;
        line-height: 22px;
      }
    }

    .table-header, .line {
      display: none;
    }

    .success-icon {
      margin-left: 4%;
    }

    .mobile-header {
      padding-left: 10px;
      padding-right: 10px;
      float: left;
      width: 100%;

      .clickable-block {
        float: left;
        width: 100%;
      }

      .table-body {
        width: auto;
      }

      .invoice-id {
        float: right;
      }
    }

    .table-body-mobile {
      background-color: $menu-background;
      float: left;
      width: 100%;
      padding-bottom: 10px;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
      border-top: 1px solid $menu-border;

      .table-body {
        width: 50%;
        text-align: inherit;
        padding-left: 15%;

        .table-body-header {
          display: block;
          font-weight: bold;
        }
      }
    }

    .table-row {
      border: 1px solid $menu-border;
      margin-top: 1%;
      border-radius: 5px;
    }
  }

  .invoice-table.bordered > div:nth-child(2n+10) {
    background: none;
  }
}
@media screen and (max-width: 480px) {
  .invoice-table {
    .table-body-mobile {
      .table-body {
        padding-left: 10%;
      }
    }
  }
}
