import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {BillingInformation, Contabile, DilazioneClienteData, Invoice, RichiediDilazione} from './invoice.model';


import 'rxjs/add/operator/delay';
import 'rxjs/add/operator/do';


@Injectable()
export class InvoiceService {

  readonly url = 'api/invoices';
  readonly urlSaldo = 'api/saldo';
  readonly urlSaldoInScadenza = 'api/saldoInScadenza';
  readonly contamibleUrl = 'api/accounting';
  readonly dilazioneClienteUrl = 'api/get-dilazione-cliente';
  readonly richiedaDilazioneUrl = 'api/get-richiedi-dilazione';
  readonly saveRichiediDilazioneUrl = 'api/save-richiedi-dilazione';
  constructor(private http: HttpClient) {  }

  public getSaldo(): Observable<number> {
    const headers = new HttpHeaders({ clientid: localStorage.getItem('clientId') });
    return this.http.get<number>(this.urlSaldo, { headers: headers });
  }
  public getInvoiceData(): Observable<Invoice[]> {
    const headers = new HttpHeaders({clientid: localStorage.getItem('clientId')});
    return this.http.get<Invoice[]>(this.url, {headers: headers});
  }
  public getBillingCenterInformation(): Observable<BillingInformation> {
    return this.http.get<BillingInformation>(`api/invoices/billing-center/${localStorage.getItem('clientId')}`);
  }
  public getSaldoInScadenza(): Observable<number> {
    const headers = new HttpHeaders({ clientid: localStorage.getItem('clientId') });
    return this.http.get<number>(this.urlSaldoInScadenza, { headers: headers });
  }
  public getContanibleData(): Observable<Contabile> {
    const headers = new HttpHeaders({ clientid: localStorage.getItem('clientId')});
    return this.http.get<Contabile>(this.contamibleUrl, { headers: headers });
  }
  public getDilazioneClienteData(): Observable<DilazioneClienteData> {
    const headers = new HttpHeaders({ clientid: localStorage.getItem('clientId')});
    return this.http.get<DilazioneClienteData>(this.dilazioneClienteUrl, { headers: headers });
  }
  public getPdf(endUrl): Observable<Blob> {
    return this.http.get(`api/pdf/${localStorage.getItem('clientId')}/${endUrl}`, {responseType: 'blob'});
  }
  public getExcelFile(year: string): Observable<Blob> {
    return this.http.get(`api/excel/report/${localStorage.getItem('clientId')}/${year}`, {responseType: 'blob'});
  }
  public getCondominiExcelFile(year: string, fiscalCode: string): Observable<Blob> {
    return this.http.get(`api/excel/report/${fiscalCode}/${year}/${localStorage.getItem('clientId')}`, {responseType: 'blob'});
  }
  public getTrafficoPdf(endUrl): Observable<Blob> {
    return this.http.get(`api/pdf/traffico/${localStorage.getItem('clientId')}/${endUrl}`, {responseType: 'blob'});
  }
  public getRichiediDilazioneData(): Observable<RichiediDilazione> {
    return this.http.post<RichiediDilazione>(this.richiedaDilazioneUrl + '?clientId=' + localStorage.getItem('clientId'), {});
  }
  public saveRichiediDilazione(body: Object): Observable<any> {
    return this.http.post<any>(this.saveRichiediDilazioneUrl + '?clientId=' + localStorage.getItem('clientId'), body);
  }
}
