import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Dilazione, DilazioneDataForPayPal} from '../../invoice.model';
import {Observable} from 'rxjs/Observable';
import {DilazioneActions} from '../../../../redux/dilazione/actions';
import {InvoiceService} from '../../invoice.service';
import * as _ from 'lodash';

import {ModalActions} from '../../../../redux/modal/actions';
import {ModalEntity} from '../../../../common/model/modal/ModalEntity';
import {invoiceErrorModalMessage} from '../../config/config';
import * as moment from 'moment';
import {UserData} from '../../../../common/model/userData.model';
import {PayPalService} from '../../../../common/services/paypal/pay-pal-service.service';


@Component({
  selector: 'app-deferments-invoice-layout',
  templateUrl: './deferments-invoice-layout.component.html',
  styleUrls: ['./deferments-invoice-layout.component.scss']
})
export class DefermentsInvoiceLayoutComponent implements OnInit {

  isMobile = window.innerWidth <= 767;

  @select(['dilazione', 'dilazione'])
  private dilazione: Observable<Dilazione>;
  dilazioneData: Dilazione;
  pagaList: Array<string> = [];
  showPagamentoModalSelectedRate: boolean;
  payPalPayment: any;
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userData: UserData;
  dilazioneDataArray: Array<any> = [];
  dilazioneDataForPayPal: DilazioneDataForPayPal;

  constructor(private modalActions: ModalActions, private actions: DilazioneActions, private service: InvoiceService,
              private payPalService: PayPalService) {

    this.dilazione.subscribe(value => {
      if (Object.keys(value).length === 0) {
        this.service.getDilazioneClienteData().subscribe(dilazione => {
          if ( dilazione && dilazione.dilazione ) {
          this.actions.setDilazione(dilazione.dilazione, dilazione.esito);
          this.dilazioneData = dilazione.dilazione;
          }
        });
      }
      this.dilazioneData = value;
    });
    this.userInfo.subscribe(data => {
      this.userData = data;
    });
  }

  ngOnInit() {
  }

  openModal(title: string, text: string) {
    this.modalActions.showModal({title, text} as ModalEntity);
  }

  handleChange(value, event) {
    const {pagaList, dilazioneData} = this;
    const rates = dilazioneData.rate.filter(item => item.dataScadenza < value.dataScadenza &&
      item.importoPagato !== item.importoRata).map(item => item.idRata);
    if (event.target.checked) {
      if (rates.length !== pagaList.length || _.intersection(pagaList, rates).length !== pagaList.length) {
        event.target.checked = false;
        this.openModal(invoiceErrorModalMessage.title, invoiceErrorModalMessage.message);
        return false;
      }
      pagaList.push(value.idRata);
    } else {
      if (pagaList.indexOf(value.idRata) !== pagaList.length - 1) {
        event.target.checked = true;
        this.openModal(invoiceErrorModalMessage.title, invoiceErrorModalMessage.message);
        return false;
      }
      pagaList.splice(this.pagaList.indexOf(value.idRata), 1);
    }
  }

  openPaymentForSelectedRate(pagaList) {
    if (pagaList && pagaList.length) {
      this.showPagamentoModalSelectedRate = true;
    }
  }

  hideDialogModal() {
    this.showPagamentoModalSelectedRate = false;
  }

  openPaymentForSelectedRateByPayPal(pagaList) {
    let totalValue = 0.00;
    this.dilazioneDataArray = new Array<any>();
    this.dilazione.subscribe(item => {
      for (let i = 0; i < item.rate.length; i++) {
        for (let j = 0; j < pagaList.length; j++) {
          if (item.rate[i].idRata === pagaList[j]) {
            this.dilazioneDataForPayPal = new DilazioneDataForPayPal();
            this.dilazioneDataForPayPal.idRata = item.rate[i].idRata;
            if (item.rate[i].importoPagato === item.rate[i].importoRata) {
            this.dilazioneDataForPayPal.Importo = +item.rate[i].importoRata.toFixed(2);
            } else {
              this.dilazioneDataForPayPal.Importo = +(item.rate[i].importoRata - item.rate[i].importoPagato).toFixed(2);
            }
            totalValue += +this.dilazioneDataForPayPal.Importo;
            this.dilazioneDataArray.push(this.dilazioneDataForPayPal);
          }
        }
      }
    });
    this.payPalPayment = {
      'TipoPagamento': 'Paypal',
      'SistemaChiamante': 'Selfcare',
      'Data': moment(Date.now()).format('DD/MM/YYYY'),
      'TotalePagamento': totalValue.toFixed(2),
      'Dilazioni': this.dilazioneDataArray,
      'CodiceCliente': this.userData.id,
      'RagioneSociale': this.userData.nameInInvoice,
      'CF': this.userData.fiscalCode,
      'PIVA': this.userData.vatNumber
    };
    this.payPalService.postPayPalActivationDilazioni(this.payPalPayment).subscribe(item =>
      window.open(item.Return_URL));
    this.showPagamentoModalSelectedRate = false;
  }

  openPaymentForSelectedRateByCreditCart(pagaList) {
    window.open(`/api/properties/payment/rate?listRate=${pagaList}&clientId=${localStorage.getItem('clientId')}&access_token=${localStorage.getItem('access_token')}`);
    this.showPagamentoModalSelectedRate = false;
  }

}
