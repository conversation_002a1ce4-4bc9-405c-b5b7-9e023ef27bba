<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 invoice-layout">
  <div *ngIf="invoices.length==0" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-content text-center">
    <h4>Nessuna fattura trovata.</h4>
  </div>
  <div *ngIf="invoices.length!==0">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 accountant no-padding" *ngIf="!isUserPA">
      <div class="name">Saldo contabile</div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding accountant-message-block"
           *ngIf="saldo === 0 || (contabile?.numeroFattureScoperte === 0 && dilazioneData?.dilazione?.numeroRate === 0)">
        <div class="accountant-message">Complimenti, il tuo
          <b>ID Cliente {{getUserId()}}</b> risulta in regola con i pagamenti.
        </div>
      </div>
      <div class="app-button" *ngIf="showPagamentoFlessibileButton"
           (click)="showRichiediSlittamentoProssimaFatturaModal()">
        Richiedi slittamento prossima fattura
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 accountant-message-block no-padding"
           *ngIf="(saldo !== 0 && contabile?.numeroFattureScoperte >= 1) || (dilazioneData?.dilazione?.numeroRate) ">
        <div *ngIf="saldo !== 0 && contabile?.numeroFattureScoperte >= 1">
          <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 scaduto-label">
            <b>Importo scaduto: </b>
          </div>
          <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 accountant-message saldo-value">
            <b>€{{saldo | number : '1.2-2'}}</b>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 paga-button">
            <div class="app-button paga" (click)="openPaymentForAllFattura(allPagaList)">
              PAGA
            </div>
            <div class="col-lg-7 col-md-7 col-sm-7 col-xs-7" *ngIf="showAllegaPagamento">
              <div class="app-button alegga-button" (click)="openAllegaPagamento()">
                <img class="clip" src="assets/img/icons/clip.png" alt="clip">
                ALLEGA PAGAMENTO
              </div>
            </div>
          </div>
          <div class="app-button" *ngIf="richiediDilazioneData?.fattibilita && shouldShowRichiediButton"
               (click)="showRichiediDilazioneModal()">
            RICHIEDI DILAZIONE
          </div>
        </div>
        <div class="app-button" routerLink="/invoices/deferments"
             *ngIf="dilazioneData?.dilazione?.numeroRate">
          DILAZIONI E PAGAMENTO RATE
        </div>
      </div>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 accountant-message-block no-padding"
           *ngIf="!(saldo !== 0 && contabile?.numeroFattureScoperte >= 1) && !(dilazioneData?.dilazione?.numeroRate) ">
        <div class="app-button" *ngIf="richiediDilazioneData?.fattibilita && shouldShowRichiediButton"
             (click)="showRichiediDilazioneModal()">
          RICHIEDI DILAZIONE
        </div>
      </div>
      <div *ngIf="atLeastOneItemSold">
        <div *ngIf="isDebtSoldToRepaymentCompany">
          Attenzione, le fatture scoperte risultano cedute ad una società di recupero crediti
        </div>
        <div *ngIf="!isDebtSoldToRepaymentCompany">
          <div class="accountant-message"> Sull’
            <b>ID Cliente {{getUserId()}} </b> risulta uno scoperto di
            <b>€{{saldo | number : '1.2-2'}}</b> da corrispondere ad Optima.
            Risultano inoltre fatture <b>cedute</b> ad una società
            di recupero crediti per un totale di <b>€{{ceduteSum | number : '1.2-2'}}</b>
          </div>
          <div class="accountant-message"> Ti invitiamo a saldare al più presto
            l'importo dovuto o a contattare il nostro
            Servizio Clienti per maggiori informazioni.
          </div>
          <div class="app-button" (click)="openPaymentForAllFattura(allPagaList)">
            PAGA L’INTERO IMPORTO SCADUTO
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="isUserPA">
      <div class="header-text">Centri di fatturazione</div>

      <div class="grid-container">
        <div class="control-panel-small">
          <div class="search-title-small"><b>Rendicontazione annuale</b></div>
          <div class="input-container" [formGroup]="formGroupYearReport">
            <input class="input-year" type="number" formControlName="year"/>
            <img src="assets/img/optimaIcons/input_icon_calendar.png" alt="Calendar" class="calendar-icon"/>
          </div>
          <img class="download-excel-button" src="assets/img/buttons/scarica_file_excel.png" alt="download button"
               (click)="downloadYearlyReport()"/>
        </div>

        <div class="control-panel-small" [formGroup]="formGroupSearchByKey">
          <div class="search-title-small"><b>Trova quale fattura contiene il POD/PDR:</b></div>
          <input class="input-pod" type="text" placeholder="Numero POD/PDR" formControlName="searchValue"
                 (input)="searchByValue()"/>
          <button class="cerca-button" (click)="searchByValue()">CERCA</button>
        </div>
      </div>

      <div class="billing-centre-container">
        <div class="billing-block" *ngFor="let bill of aggregatedBillsToTable"
             (click)="bill.showAssociatedBills = !bill.showAssociatedBills">
          <div class="billing-block-header">
            <img class="billing-block-logo" src="assets/img/optima/colour_colibri.png" alt="Logo"/>
            Centro di fatturazione {{bill.idFatt}}
          </div>
          <div class="billing-block-label">CUU numero</div>
          <div class="billing-block-cuu-value">{{bill.cuu}}</div>
          <div class="line billing-block-line"></div>
          <div class="billing-block-fatture-label-container">
            <div>LA TUA FATTURA COMPRENDE:</div>
            <img *ngIf="!bill.showAssociatedBills" src="assets/img/icons/arrow-down_16x16.png" alt="Arrow"/>
            <img *ngIf="bill.showAssociatedBills" style="transform: rotate(180deg);"
                 src="assets/img/icons/arrow-down_16x16.png" alt="Arrow"/>
          </div>
          <div class="expanded-content" *ngIf="bill.showAssociatedBills">
            <div *ngFor="let associate of bill.associatedBills">
              <div class="associated-bills-container">
                <img *ngIf="associate.tipo === 'ENERGIA'" class="small-icon"
                     src="assets/img/service-icons/icon_luce_small.png"
                     alt="Luce">
                <img *ngIf="associate.tipo === 'GAS'" class="small-icon" src="assets/img/service-icons/icon_gas_small.png"
                     alt="Gas">
                <div>{{associate.symbol}} – {{associate.utenza}} – <b>{{associate.tipologiaUso}}</b></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="line"></div>

    <div class="header-text margin-top-bottom" *ngIf="isUserPA">Dettagli fattura</div>

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 control-panel no-padding" [formGroup]="formGroup">
      <div class="control-panel-item search-title"><b>Ricerca fattura</b></div>
      <div class="search-by-invoice">
        <label class="search-label">Numero fattura:</label>
        <input matInput class="app-input search-input" formControlName="invoiceId" placeholder="NUMERO FATTURA"
               (keyup)="applyFilter()"/>
      </div>
      <div class="control-panel-item search-by-date">
        <label>Data:</label>
        <input class="app-input" type="date" formControlName="startDate" placeholder="data" (change)="applyFilter()"/>
      </div>
      <div class="control-panel-item">
        <button class="app-button search-button" type="button" (click)="applyFilter()">CERCA</button>
      </div>
      <div class="control-panel-item pay-button">
        <button class="app-button" [ngStyle]="getFatturaButtonStyle()"
                (click)="openPaymentForSelectedFattura(pagaList)">Paga le fatture selezionate
        </button><!--[style.color]="pagaList.length == 0 ? 'gray' : '#36749C'"-->
      </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 invoice-table bordered">
      <div class="{{isUserBusiness ? 'table-header checkbox-block table-header-business' : 'table-header checkbox-block'}}"></div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Pagato</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Fattura</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}" *ngIf="isUserPA">Centro di fatturazione</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}" *ngIf="isUserBusiness">Ricarica SIM</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Data fattura</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Data scadenza</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Totale</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}" *ngIf="!isUserPA">Totale de pagare</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}">Scarica</div>
      <div class="{{isUserBusiness ? 'table-header table-header-business' : 'table-header'}}" *ngIf="!isUserPA">Traffico</div>

      <div class="line"></div>
      <div class="table-row" *ngFor="let item of paginatorListener | async">
        <div class="mobile-header">
          <div class="{{isUserBusiness? 'table-body checkbox-block table-body-business' : 'table-body checkbox-block'}}">
            <label class="checkbox-container">
              <input name="referencePeriod" type="checkbox"
                     [value]="item" (change)="handleChange(item,$event)" [checked]="isAlreadyMarked(item)"
                     [disabled]="(item.total===item.totalEvasion||!item.opened) || isDisabledByFatture(item.numeroFattura) || item.isSold"/>
              <span
                class="mark {{(item.total===item.totalEvasion||!item.opened)&&'disabled' || isDisabledByFatture(item.numeroFattura) && 'disabled' }}"></span>
            </label>
          </div>
          <div class="clickable-block" (click)="item.open=!item.open">
            <div class="{{isUserBusiness ? 'table-body success-icon table-body-business' : 'table-body success-icon'}}">
              <span class="icon icon-fix success" *ngIf="item.total===item.totalEvasion"></span>
            </div>
            <div class="{{isUserBusiness ? 'table-body arrows-mobile table-body-business' : 'table-body arrows-mobile'}}">
              <span class="fa fa-angle-right" *ngIf="!item.open"></span>
              <span class="fa fa-angle-down" *ngIf="item.open"></span>
            </div>
            <div class="{{isUserBusiness ? 'table-body invoice-id table-body-business' : 'table-body invoice-id'}}">{{item.numeroFattura}}</div>
          </div>
        </div>
        <div class="table-body-mobile" *ngIf="!isMobile || item.open">
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}" *ngIf="isUserBusiness">
            <span class="table-body-header">Ricarica SIM</span>
            <span class="icon icon-fix success" *ngIf="item.invoiceSeries === 'MF'"></span>
          </div>
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}" *ngIf="isUserPA">
            <span class="table-body-header">Centro di fatturazione</span>
            <span>{{item.idFatt}}</span>
          </div>
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}">
            <span class="table-body-header">Data fattura</span>
            <span>{{item.startDate| date : "dd/MM/y"}}</span>
          </div>
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}">
            <span class="table-body-header">Data scadenza</span>
            <span>{{item.endDate| date : "dd/MM/y"}}</span>
          </div>
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}">
            <span class="table-body-header">Totale</span>
            <span>{{item.total ? item.total.toFixed(2) : item.total}}</span>
          </div>
          <div class="{{isUserBusiness ? 'table-body table-body-business' : 'table-body'}}" *ngIf="!isUserPA">
            <span class="table-body-header">Totale de pagare</span>
            <span>{{item.opened ? item.opened.toFixed(2) : item.opened}}</span>
          </div>
          <div class="{{isUserBusiness ? 'table-body icon-container table-body-business' : 'table-body icon-container'}}"
               *ngIf="item.downloadUrl !== 'FTT'">
            <span class="table-body-header">Scarica</span>
            <img src="/assets/img/optimaIcons/pdf_icon.png" class="pdf-icon" (click)="downloadFile(item.id)" alt="PDF">
          </div>
          <div class="{{isUserBusiness ? 'table-body icon-container table-body-business' : 'table-body icon-container'}}"
               *ngIf="item.downloadUrl === 'FTT'">
            <span class="table-body-header">Scarica</span>
            <i class="i-button" (click)="showHideInfoWindow()">i</i>
          </div>
          <div class="{{isUserBusiness ? 'table-body icon-container table-body-business' : 'table-body icon-container'}}" *ngIf="!isUserPA">
            <span class="table-body-header">Traffico</span>
            <div *ngIf="item.downloadUrl !== 'FTT'">
              <img src="/assets/img/optimaIcons/pdf_icon.png" class="pdf-icon" *ngIf="item.spUriTrafficoVoce"
                   (click)="downloadTrafficoFile(item.id)" alt="PDF">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 paginator">
      <app-paginator #paginator [items]="invoiceTableData" [listener]="paginatorListener"
                     [showLabel]="false"></app-paginator>
    </div>
  </div>
  <app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
  <app-dialog-modal-wrapper></app-dialog-modal-wrapper>
  <!--<app-month-modal-wrapper></app-month-modal-wrapper> -->
</div>

<div class="confirm-window" *ngIf="showInfoDialog">
  <div class="modal-dialog modal-xs">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="showHideInfoWindow()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="modal-body-content">
          <div class="info-message"><p>Puoi recuperare questa fattura nel tuo cassetto fiscale sul sito dell'Agenzia
            delle entrate al link</p></div>
          <a href="https://telematici.agenziaentrate.gov.it/"> https://telematici.agenziaentrate.gov.it/ </a>
        </div>
      </div>
      <div class="modal-footer">
      </div>
    </div>
  </div>
</div>

<div class="modal-div display" *ngIf="showSlittamentoProssimaModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="title modal-title">Scegli l'offerta per cui vuoi posticipare
      la scadenza della prossima fattura:
    </div>
    <br>
    <div class="table-row" *ngFor="let item of active">
      <div class="checkbox-block col-lg-2 col-md-2 col-sm-2 col-xs-2">
        <label class="checkbox-container">
          <input name="referencePeriod" type="checkbox"
                 [value]="item" (change)="handleSlittamentoProssimaChange(item,$event)">
          <span
            class="mark"></span>
        </label>
      </div>
      <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 padding"
           *ngIf="conditionShowOfferByIdFattRichiediSlittamentoProssima(item.billingId)">
        <div class="subject">
          <span class="bold col-lg-8 col-md-8 col-sm-8 col-xs-8">{{item.tipoCommercialeDescrizione}}</span>
        </div>
        <div class="subject">
          <span class="bold col-lg-8 col-md-8 col-sm-8 col-xs-8">Data fine anno contrattuale</span>
          <span class="col-lg-4 col-md-4 col-sm-4 col-xs-4">{{item.scadenzaAnnoContrattuale | date : "dd/MM/y"}}</span>
        </div>
        <div class="subject">
          <span class="bold col-lg-8 col-md-8 col-sm-8 col-xs-8">Canone Energia</span>
          <span
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4">{{item.canoneStabilizzato ? item.canoneStabilizzato.toFixed(2) : item.canoneStabilizzato}}
            €</span>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn decline-modal" (click)="hideDialogModal()">Annulla</button>
      <button class="btn accept-modal" [ngStyle]="getSlittamentoProssimaFatturaButtonStyle()"
              (click)="confirmModalSubmit($event)"
              [attr.disabled]="selectedSlittamentoProssimaList < 1 ? 'disabled' : null">Conferma
      </button>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showConfermaSlittamentoProssimaModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="title modal-title">Procedendo con questa operazione, la prossima fattura avrà una scadenza posticipata
      di 15 giorni.
    </div>
    <br>
    <div class="modal-footer">
      <button class="btn decline-modal" (click)="hideDialogModal()">Annulla</button>
      <button class="btn accept-modal btn accept-modal" [ngStyle]="getSlittamentoProssimaFatturaButtonStyle()"
              (click)="confirmSlittamentoProssimaModal($event)">Conferma
      </button>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showSuccessConfermaSlittamentoProssimaModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/icons/ok.png" alt="Ok">
    <div class="title modal-title">La tua richiesta è stata presa in
      carico, a breve sarai contattato dal Servizio Clienti Optima per un riscontro in merito.
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showFrequentConfermaSlittamentoProssimaModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png" alt="Warning">
    <div class="title modal-title">Hai già usufruito
      del servizio per 3 volte nell’anno
      contrattuale corrente.
    </div>
    <div class="modal-text">
      Non possono essere inserite più di 3 richieste nel corso
      dell’anno contrattuale.
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showFailConfermaSlittamentoProssimaModal">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png" alt="Warning">
    <div class="title modal-title">La richiesta non può essere gestita,
      in quanto le ultime tre fatture non
      risultano pagate con domiciliazione
      bancaria
    </div>
  </div>
</div>
<div class="back" *ngIf="showSpinner">
  <div class="spinner">
  </div>
</div>
<div class="modal-div display" *ngIf="showPagamentoModalAllFattura">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text-payment">Scegli la modalità di pagamento</div>
    <div class="container-flex">
      <div class="button-with-icon" (click)="openPaymentForAllFatturaByCreditCart(allPagaList)">
        <div class="icon-mastercard"></div>
      </div>
      <div class="button-with-icon" (click)="openPaymentForAllFatturaByPayPal(allPagaList)">
        <div class="icon-paypal"></div>
      </div>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showPagamentoModalSelectedFattura">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text-payment">Scegli la modalità di pagamento</div>
    <div class="container-flex">
      <div class="button-with-icon" (click)="openPaymentForSelectedFatturaByCreditCart(pagaList)">
        <div class="icon-mastercard"></div>
      </div>
      <div class="button-with-icon" (click)="openPaymentForSelectedFatturaByPayPal(pagaList)">
        <div class="icon-paypal"></div>
      </div>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showAllegaPagamentoInstruction">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text-payment margin-bottom-allega">Allega pagamento</div>
    <div class="modal-text-allega">Per i pagamenti con Carta di credito non è necessario inviare alcuna conferma.</div>
    <div class="modal-text-allega">Se hai pagato con bollettino postale non è necessario allegare il pagamento poiché
      l'incasso sarà registrato in pochi minuti.
    </div>
    <div class="modal-text-allega"><b>Se hai pagato con bonifico bancario clicca su Procedi.</b></div>
    <div class="flex-buttons">
      <button class="annulla" (click)="hideDialogModal()"><b>ANNULLA</b></button>
      <button class="procedi" (click)="openAllegaPagamentUploadFileModalWindow()"><b>PROCEDI</b></button>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showAllegaPagamentoUploadFileModalWindow" [formGroup]="formGroupAleggaPagamento">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text-payment margin-bottom-allega">Allega pagamento</div>
    <div class="flex-inputs">
      <div class="select-options">
        <select id="questions" class="form-control">
          <option value="Pagamento" selected>Pagamento</option>
        </select>
      </div>
      <textarea class="description-allega" placeholder="Indica qui le fatture per le quali stai allegando il pagamento"
                formControlName="description"></textarea>
    </div>
    <span class="text-danger" style="margin-left: 32px"
          *ngIf="formGroupAleggaPagamento.controls['description'].invalid && formGroupAleggaPagamento.controls['description'].touched">Campo obbligatorio</span>
    <div class="flex-allega-pagamento">
      <label class="app-button alegga-button margin-left-allega" style="max-height: 33px" for="files-upload">
        <img class="clip" src="assets/img/icons/clip.png" alt="clip">
        ALLEGA PAGAMENTO
      </label>
      <div class="left-part-upload-file">
        <div *ngFor="let file of emailFilesBeforeValidation; let i = index">
          <div class="file-name" *ngIf="!emailIndexesOfFilesWithWrongSize.includes(i)
                  && !emailIndexesOfFilesWithWrongExtension.includes(i)">
            {{emailFilesBeforeValidation[i].name}}
          </div>
          <div *ngIf="emailIndexesOfFilesWithWrongSize.includes(i)" class="file-wrong">
            La dimensione del file deve essere inferiore a 5 MB
          </div>
          <div *ngIf="emailIndexesOfFilesWithWrongExtension.includes(i)" class="file-wrong">
            L’estensione del file deve essere PDF, SVG o JPG
          </div>
          <button *ngIf="!emailIndexesOfFilesWithWrongSize.includes(i)
                && !emailIndexesOfFilesWithWrongExtension.includes(i)" class="remove-file-button"
                  (click)="removeFileFromEmailForm(i, emailFilesAfterValidation.indexOf(file))">🞫
          </button>
          <button *ngIf="emailIndexesOfFilesWithWrongSize.includes(i)
                || emailIndexesOfFilesWithWrongExtension.includes(i)"
                  class="remove-file-button" (click)="closeWarningInEmailForm(i)">🞫
          </button>
        </div>
      </div>
      <input type="file" accept="application/pdf,image/jpeg, image/svg+xml" id="files-upload"
             (change)="onFileChange($event)"
             #filesInput>
    </div>
    <span class="text-danger" style="margin-left: 32px"
          *ngIf="formGroupAleggaPagamento.controls['files'].invalid && formGroupAleggaPagamento.controls['files'].touched">Campo obbligatorio</span>
    <div class="flex-button">
      <button class="invia" (click)="openIncidentWithAttachment()">INVIA</button>
    </div>
  </div>
</div>
<div class="modal-div display" *ngIf="showSuccessModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/icons/ok.png" alt="OK">
    <div class="modal-text-allega" style="font-size: 22px; margin-bottom: 15px">La tua comunicazione è stata presa in carico.</div>
    <div class="modal-text-allega" style="margin-bottom: 15px">La tua situazione contabile sarà aggiornata in 48/72 ore.</div>
    <div class="modal-text-allega">Grazie.</div>
  </div>
</div>
<div class="modal-div display" *ngIf="showNotFindFileWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/icons/error.png" alt="Error">
    <div class="modal-text-allega" style="font-size: 30px">File non trovato</div>
  </div>
</div>
