import { Component } from '@angular/core';
import { select } from '@angular-redux/store';
import { Observable } from 'rxjs/Observable';
import {NavigationEnd, Router} from '@angular/router';

@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss']
})

export class HomeComponent {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  isMobile = window.innerWidth <= 991;
  isAmazonPrimeLayoutActive: boolean = false;

  constructor(private router: Router) {
    this.router.events.subscribe(value => {
       if (value instanceof NavigationEnd) {
         this.isAmazonPrimeLayoutActive = value.url === '/home/<USER>';
       }
    });
  }

  navigate() {
    this.router.navigateByUrl('/gestione-condomini');
  }

  isUserCondominio() {
    return localStorage.getItem('sottotipoCluster') === 'Condominio';
  }
}
