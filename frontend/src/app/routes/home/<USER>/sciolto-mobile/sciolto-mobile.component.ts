import {Component, OnInit} from '@angular/core';
import {ScioltoService} from '../../../../common/services/sciolto/sciolto.service';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {select} from '@angular-redux/store';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {OffersService} from '../../../../common/services/offers/offers.service';

@Component({
  selector: 'app-sciolto-mobile',
  templateUrl: './sciolto-mobile.component.html',
  styleUrls: ['./sciolto-mobile.component.scss']
})
export class ScioltoMobileComponent implements OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userCluster: string;
  description: number;
  tariffName: number;
  promoTexts: string[] = [];
  withoutIncident: boolean;
  showModalWindowAboutAlreadyOpenIncident: boolean;
  showModalWindowAboutOpenNewIncident: boolean;
  codiceOfferta: string;

  constructor(private scioltoService: ScioltoService, private incidentEventService: IncidentEventService, private offerService: OffersService) {
    this.offerService.checkIncidentEventCrossSelling().subscribe(result => {
      this.withoutIncident = result;
    });
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.scioltoService.getGeneralScioltoInformation().subscribe(generalInformation => {
      if (generalInformation.response[0]) {
        this.codiceOfferta = generalInformation.response[0].fields.Offerta;
        this.scioltoService.getScioltoInformation(generalInformation.response[0].fields.Offerta, this.userCluster)
          .subscribe(information => {
            this.description = information.response[0].serviziMobile[0].descrizione;
            this.tariffName = information.response[0].serviziMobile[0].denominazione;
            const mobilePromos = information.response[0].promo.filter(item =>
              item.Elements.some(value => value.NomeServizio === 'MOBILE')
            );
            const mobilePromoOpzionali = information.response[0].promoOpzionali.filter(item =>
              item.Elements.some(value => value.NomeServizio === 'MOBILE')
            );
            this.promoTexts = [...mobilePromos, ...mobilePromoOpzionali].map(promo => promo.DescrizioneExt || promo.descrizioneExt);
          });
      }
    });
  }

  ngOnInit() {
  }

  hideModalWindow() {
    this.showModalWindowAboutAlreadyOpenIncident = false;
    this.showModalWindowAboutOpenNewIncident = false;
  }

  buttonClick() {
    if (this.withoutIncident) {
      this.incidentEventService.openIncidentEventForCrossSelling('MOBILE', this.codiceOfferta).subscribe(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.showModalWindowAboutOpenNewIncident = true;
          this.withoutIncident = false;
        }
      });
    } else {
      this.showModalWindowAboutAlreadyOpenIncident = true;
    }
  }
}
