<div class="col-md-11 titleBlock">
  <div class="doctor-picture">
  </div>
  <div class="title">
    TELECONSULTO MEDICO
  </div>
</div>
<div class="app-m-mobile-layout col-md-11 mobile-layout block">
  <div class="flex-block">
    <img class="icon-banner" src="/assets/img/sciolto/TELECONSULTO_img.png" alt="TELECONSULTO">
    <div class="banner-text">Abbiamo per te una promozione eccezionale per il nuovo servizio Optima di <b>TELECONSULTO
      MEDICO:</b> aggiungendo <b>€ {{price}}</b>/mese alla fattura avrai sempre a tua disposizione <b>7 giorni su
      7, 24 ore su 24</b> un Medico generico che ti supporterà per qualsiasi evenienza.
    </div>
  </div>
  <div class="additional-text">Negli ultimi anni abbiamo imparato quanto possa essere importante avere un medico
    sempre disponibile anche nei giorni festivi e ad orari insoliti. Prova a pensare quante volte avresti avuto
    bisogno di contattare il tuo medico nei weekend o festivi e non sei riuscito ad avere un supporto immediato, con
    questo servizio non succederà più.
  </div>

  <div class="promo-block" *ngIf="promoTexts && promoTexts.length > 0">
    <div class="flex-promo-header">
      <img class="promo-icon" src="/assets/img/sciolto/Promo.png" alt="PROMO">
      <div><b>PROMO PER TE</b></div>
    </div>
    <div>
      <p *ngFor="let promoText of promoTexts">
        {{promoText}}
      </p>
    </div>
  </div>

  <div class="small-text">Per ricevere info di dettaglio clicca qui!</div>
  <button class="btn btn-success" (click)="buttonClick()">CONTATTACI ORA</button>
</div>

<div class="modal-div display" *ngIf="showModalWindowAboutAlreadyOpenIncident">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Risulta già una richiesta di ricontatto per un altro servizio. Potrai richiedere ulteriori
      informazioni su altri servizi durante la chiamata che riceverai.
    </div>
    <div class="modal-text">Grazie!</div>
  </div>
</div>
<div class="modal-div display" *ngIf="showModalWindowAboutOpenNewIncident">
  <div class="inner-modal-div" style="min-height: 240px">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Grazie, ti contatteremo a breve per fornirti tutte le informazioni di cui hai bisogno.</div>
  </div>
</div>
