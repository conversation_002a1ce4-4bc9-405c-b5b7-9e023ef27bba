@import "../../../../shared/styles/colors";
@import "../../../../shared/styles/app.mat-dropdown";

.titleBlock {
  clear: both;
  text-align: center;

  .title {
    color: #36749d;
  }

  .icon {
    position: absolute;
    background: url("../../../../../assets/img/optima/Set_Icone_AreaClienti_Luce.png") no-repeat;
    width: 80px;
    background-size: contain;
    top: -10px;
    left: 10px;

  }

  margin-bottom: 20px;
  border: 2px solid #b6cce3;
  border-radius: 5px;
  padding: 15px;
}

app-info {
  display: inline-block;
  position: relative;
  text-align: center;
  z-index: 200;
}

.text-information{
  color: #36749d;
  width: 140%;
  margin-left: 2%;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 30px;
  font-size: 172%;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 395px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 560px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.container-flex-text {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5%;

  .text-label {
    color: #36749d;
    font-size: 25px;
    padding-right: 5%;
  }

  .input-data {
    border-radius: 10px;
    border: 1px solid;
    color: #36749d;
    height: 37px;
  }
}

.fix-position {
  margin-left: 3%;
}

.modal-button {
  position: relative;
  left: 50%;
  transform: translate(-50%, 0);
  margin-top: 10%;
  color: white;
  background-color: #36749d;
  border-radius: 15px;
  border: 2px solid;
  height: 50px;
  width: 20%;
}

.error {
  color: red;
  position: relative;
  left: 44%;
}

.position-error {
  position: absolute;
}

.chart-position {
  margin-top: 10%;
}

.chart-size-by-month {
  width: 100%;
  margin-bottom: -10%;
}

.chart-size-by-days {
  margin-top: 19%;
  width: 110%;
  margin-bottom: 3%;
}

.service-chart-block {
  .description-block {
    margin-top: 1%;
    margin-left: 1%;
  }
}

.container-warning-circle {
  display: flex;
  align-content: space-around;
  width: 71%;
  justify-content: space-around;
  margin-bottom: 2%;

  .special-font-text {
    font-size: 15px;
    color: #36749C;
    font-style: italic;
    font-weight: bold;
  }

  .warning-circle {
    border-radius: 50%;
    border: 1px solid;
    height: 24px;
    width: 24px;
    text-align: center;
    line-height: 23px;
    font-family: fangsong;
    font-weight: bold;
    font-size: 19px;
    display: inline-block;
    color: #3f7ba2;
  }
}

.container-warning-circle-by-days {
  display: flex;
  align-content: space-around;
  width: 70%;
  justify-content: space-around;
  margin-top: 13%;
  margin-bottom: -15%;

  .special-font-text-by-days {
    font-size: 15px;
    color: #36749C;
    font-style: italic;
    font-weight: bold;
  }

  .warning-circle-by-days {
    border-radius: 50%;
    border: 1px solid;
    height: 24px;
    width: 24px;
    text-align: center;
    line-height: 23px;
    font-family: fangsong;
    font-weight: bold;
    font-size: 19px;
    display: inline-block;
    color: #3f7ba2;
  }
}

.container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  align-content: center;

  .subTitle {
    color: #36749d;
    font-weight: bold;
    font-size: 15px;
  }
}

.container-arrow {
  display: flex;
  margin-top: 20%;
  margin-left: 1%;
  color: #36749d;
  font-weight: bold;
  width: 55%;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
  .arrow-size {
    font-size: 20px;
    margin-top: -3%;
  }

  .secondTitle {
    font-size: 15px;
  }
}


.spanTitle {
  font-size: 15px;
  margin-top: 20%;
  margin-left: 29%;
  margin-bottom: -15%;
}

.conainer-third-title {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-left: 1.9%;
  padding-bottom: 5%;
  padding-top: 3%;
  cursor: pointer;
  color: #36749d;
  width: 27%;
  font-weight: bold;
  .arrow-third-size{
    font-size: 20px;
    margin-top: -6%;
  }
  .thirdTitle {
    font-size: 15px;
  }
}

.pagination-nav-hours-left {
  cursor: pointer;
  color: #07aceb;
  font-size: 23px;
  font-weight: bold;
  position: absolute;
  margin-top: 12%;
  margin-left: 435%;
}

.pagination-nav-hours-right {
  cursor: pointer;
  color: #07aceb;
  font-size: 23px;
  font-weight: bold;
  position: absolute;
  margin-top: 12%;
  margin-left: -406%;
}

.block {
  border-radius: 5px;
  border: 1px solid $menu-border;
  background-color: white;
  height: auto;
  padding-top: 15px;
  padding-bottom: 15px;
  color: $dark-blue;
}

.mobileView {
  display: none;
}

.blockWithDetail {
  border-radius: 5px 0 0 5px;
  border: 1px solid $menu-border;
  background-color: white;
  min-height: 200px;
  color: $dark-blue;
}

.detail {
  border-radius: 0 5px 5px 0;
  background: #f0f5f9;
  padding: 15px;
  min-height: 200px;

  span {
    display: block;
  }
}

.noActive {
  color: #b6cce3;
}

.subBlock {
  color: #36749d;

  .title {
    color: #36749d;
    font-weight: bold;
    font-size: 18px;
  }
}


.lineBlock {
  border-bottom: 1px solid $menu-border;
}

.bigLetter {
  margin-bottom: 10px;
  font-size: 16px;
}

.smallLetter {
  font-size: 14px;
}

.etichetta-field {
  display: flex;
  height: 15px;
  margin-bottom: 5px;
  align-items: flex-end;

  /deep/ .form-block {
    width: 72%;
  }

  /deep/ .field-editor {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    width: 120%;
  }
}

/*/deep/ .fa-pencil:before {
  position: relative;
  left: 15px
}
/deep/ .edit-button {
  margin-right: 0;
  margin-left: calc(20% + -30px);
}*/

.user-data-editor {
  margin-left: 3px;
}

.plainBlock {
  font-weight: 500;
  padding: 10px 0;
  font-size: 14px;
  line-height: 15px;
  position: relative;

  > p {
    padding-right: 110px;
    margin-bottom: 5px;
  }
}

.button {
  position: absolute;
  right: 15px;
  top: 10px;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 2px 15px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &.active {
    border: 1px solid #cccccc;
    color: #cccccc;
    background-color: #ffffff;
  }
}

.button-position {
  margin-right: -77%;
  margin-top: 32%;
  cursor: pointer;
  border-radius: 10px;
  font-size: 14px;
}

.nascondi {
  background: white;
}

.visual {
  display: inline-block;
  background: white;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-top: 10px;
}

.button:hover {
  background: #f0f5f9;
}

.subBlockWithDetails {
  padding: 10px 0;

  .title {
    color: #e54c36;
    font-weight: bold;
    font-size: 20px;
  }
}

.contentBlock {
  padding: 0;
}

.icons {
  margin-top: 10px;
}

.app--btn-dropdown {
  padding-right: 0 !important;
  padding-left: 0 !important;
  border: none;
  background: none;
}

.icon {
  width: 65px;
  height: 65px;
  margin: 10px auto 0 auto;
  display: inline-block;
}

.autolettura {
  background: url("../../../../../assets/img/optimaIcons/autolettura_color.png") no-repeat center;
  background-size: contain;
}

.modifica {
  background: url("../../../../../assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

button a {
  margin-left: 15px;
}

.mobileView {
  display: none;
}

.special-font {
  font-size: 14px;
}

.special-font-consumi {
  font-size: 12px;
  color: $dark-blue;
  margin-top: -10%;
}

.special-font-consumi-without-corso {
  font-size: 12px;
  color: $dark-blue;
  margin-top: -15%;
}

.mat-menu-style {
  width: 450px;
}

.contentBlock {
  .block {
    overflow-y: auto;
    height: 310px;
  }
}

@media only screen and (max-width: 1488px) {
  .smallLetter {
    font-size: 13px;
  }
}

@media only screen and (max-width: 991px) {
  .lineBlock {
    border-bottom: none !important;
  }
  .special-font {
    font-size: 14px;
  }
  .blockWithDetail {
    background-color: $menu-background;
  }
  .lineBlock:nth-child(1) {
    .plainBlock {
      border-top: 1px solid $menu-border;
    }
  }
  .titleBlock {
    display: none;
  }
  .block, .menuBlock {
    border: none;
  }

  .detailBlock {
    padding-top: 10px;
  }
  .service-chart-block {
    border: none;
  }
  .mobileView {
    display: inline-block;
  }
  .luce-details-mobile-layout {
    padding-left: 15px;
    padding-right: 15px;
  }
  .plainBlock {
    padding: 10px 15px;
  }
  .subBlockWithDetails {
    border-bottom: 1px solid $menu-border;
  }
  .detail {
    min-height: 10px;
    border-radius: 0;
    padding: 10px 15px;
    position: absolute;
    width: 90%;
  }
  .col-md-9 {
    padding: 0;
  }
  .icon {
    float: left;
    width: 65px;
    height: 65px;
    margin: 10px 15px 0 auto;
  }
  .icons {
    margin-left: 0;
    margin-top: 0;
    height: 190px;
    padding-left: 90%;
  }
  .url {
    margin-top: 15px;

    a {
      padding: 8px 0;
      color: $dark-blue;
    }

    a:hover {
      text-decoration: underline;
    }
  }
  .modificaPDF {
    clear: both;
    display: none;
  }
  .show {
    display: block;
  }
  .pdflist {
    line-height: 15px;
    padding: 5px 0;

    a {
      color: #36749d;
      text-decoration: none;
    }
  }

  .button {
    top: 10px;
    padding: 8px 15px;
  }

  .modifica {
    margin-top: 10px;
    margin-right: 15px;
  }
  .icon-autolettura {
    height: 65px;
    width: 65px;
  }
  .icon-selection {
    height: 65px;
    margin-bottom: 10px;
  }
  .app--btn-dropdown {
    padding-bottom: 0;
    padding-top: 0;
  }
  .autolettura {
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 10px;
    margin-right: 15px;
  }
  .nascondi {
    background: white;
  }
}

@media only screen and (max-width: 768px) {
  .special-font {
    font-size: 14px;
  }
  .detail {
    min-height: 10px;
    border-radius: 0;
    padding: 10px 15px;
    position: initial;
    width: 100%;
  }
  .icons {
    width: 280px;
    margin: 30px auto 30px;
    padding-right: 0;
    padding-left: 0;
    height: 100px;

  }
  .app--btn-dropdown {
    padding-right: 0 !important;
    padding-left: 0 !important;
    border: none;
    background: none;
    height: 35px;
  }

  .icon-autolettura {
    padding-right: 0 !important;
    padding-left: 0 !important;
    border: none;
    background: none;
    height: 50px;
  }
  .icon-selection {
    // padding-top: 35px;
    margin-bottom: 0;
  }

  .icon {
  }
  .autolettura {
    background: white none;
    border: 1px solid $dark-blue;
    font-size: 18px;
    height: 38px;
    alignment: center;
    border-radius: 2px;
    text-align: center;
    align-content: center;
    margin-top: 0;
    margin-bottom: 10px;
    margin-left: 0;
    width: 280px;
    //position: absolute;
    padding-top: 5px;
  }
  .autolettura:after {
    font-family: Lato-Regular, serif;
    font-style: normal;
    color: $dark-blue;
    content: "Autolettura";
  }
  .modifica {
    background: white none;
    border: 1px solid $dark-blue;
    font-size: 18px;
    alignment: center;
    border-radius: 2px;
    text-align: center;
    align-content: center;
    margin-top: 0;
    height: 38px;
    width: 280px;
    //position: absolute;
    padding-top: 5px;
    margin-right: 0;
  }
  .modifica:after {
    font-family: Lato-Regular, serif;
    font-style: normal;
    color: $dark-blue;
    content: "Modifica";
  }

  ::ng-deep.mat-menu-panel {
    margin-right: 0;
    margin-top: 34px;
  }
}

@media only screen and (max-width: 399px)
{
  .service-chart-block {
    .legend {
      margin-top: 53%;
      margin-left: 49%;
    }
  }
  .button-position {
    margin-right: 3px;
    margin-top: 86%;
    font-size: 9px;
    padding: 5px 10px;
  }
  .container-warning-circle-by-days{
    width: 105%;
  }
  .container-warning-circle{
    width: 100%
  }
  .chart-size-by-month{
    width: 165%;
    margin-left: -5%
  }
  .chart-size-by-days{
    margin-top: 25%;
    width: 190%;
    margin-left: -5%
  }
  .chart-position{
    width: 155%;
  }
  .text-information{
    width: 100%;
    font-size: 15px;
  }
}

@media only screen and (min-width: 400px) and (max-width: 499px) {
  .service-chart-block {
    .legend {
      margin-top: 44%;
      margin-left: 49%;
    }
  }
  .button-position {
    margin-right: 24px;
    margin-top: 72%;
    font-size: 9px;
    padding: 5px 10px;
  }
  .chart-size-by-month{
    width: 140%;
    margin-left: -5%
  }
  .chart-size-by-days{
    width: 160%;
    margin-top: 25%;
    margin-left: -5%
  }
  .chart-position{
    width: 140%;
  }
  .text-information{
    width: 100%;
    font-size: 15px;
  }
  .container-warning-circle-by-days{
    width: 105%;
  }
  .container-warning-circle{
    width: 100%
  }
}

@media only screen and (min-width: 500px) and (max-width: 599px) {
  .button-position {
    margin-right: 35px;
    margin-top: 48%;
    font-size: 9px;
    padding: 5px 10px;
  }
  .service-chart-block {
    .legend{
      margin-top: 32%;
      margin-left: 58%;
    }
  }
  .text-information{
    width: 100%;
    font-size: 15px;
  }
}

@media only screen and (min-width: 600px) and (max-width: 699px) {
  .service-chart-block {
    .legend{
      margin-top: 31%;
      margin-left: 63%;
    }
  }
  .button-position {
    margin-right: 40px;
    margin-top: 45%;
    font-size: 9px;
  }
}
@media only screen and (min-width: 699px) and (max-width: 768px) {
  .button-position {
    margin-right: 6%;
    margin-top: 39%;
    font-size: 9px;
  }
  .service-chart-block {
    .legend {
      margin-top: 28%;
      margin-left: 70%;
    }
  }
}
@media only screen and (min-width: 650px) and (max-width: 991px) {
  .container {
    width: 65%;
  }
  .chart-size-by-days {
    width: 101%;
  }
}

@media only screen and (min-width: 769px) and (max-width: 991px) {
  .button-position {
    margin-right: 40px;
    margin-top: 37.6%;
    font-size: 10px;
  }
  .service-chart-block {
    .legend {
      margin-top: 28%;
      margin-left: 74%;
    }
  }
  .container-warning-circle-by-days {
    width: 65%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1440px) {
  .chart-size-by-days {
    width: 165%;
    margin-top: 25%;
  }
  .chart-size-by-month {
    width: 150%;
    margin-top: 25%;
  }
  .service-chart-block {
    .legend {
      margin-top: 1%;
    }
  }
  .chart-position {
    width: 145%;
    margin-top: 13%;
  }
  .text-information{
    width: 180%;
  }
  .pagination-nav{
    margin-top: 90%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1170px) {
  .container-warning-circle-by-days{
    width: 117%;
  }
  .container-warning-circle{
    width: 120%;
  }
  .container-arrow{
    width: 112%;
  }
  .conainer-third-title{
    width: 53%;
  }
  .button-position {
    margin-top: 14%;
    margin-right: -100%;
    font-size: 12px;
  }
  .pagination-nav{
    margin-top: 95%;
  }
}
@media only screen and (min-width: 1171px) and (max-width: 1440px) {
  .container-warning-circle-by-days{
    width: 95%;
  }
  .container-warning-circle{
    width: 98%;
  }
  .container-arrow{
    width: 92%;
  }
  .conainer-third-title{
    width: 45%;
  }
  .button-position {
    margin-top: 11%;
    margin-right: -89%;
    font-size: 12px;
  }
  .pagination-nav{
    margin-top: 110%;
  }
}
@media only screen and (min-width: 1441px) and (max-width: 1600px) {
  .chart-size-by-days {
    width: 120%;
  }
  .chart-position {
    width: 110%;
  }
  .text-information{
    width: 175%;
  }
  .container-warning-circle{
    width: 91%;
  }
  .container-warning-circle-by-days{
    width: 89%;
  }
  .container-arrow{
    width: 73%;
  }
  .conainer-third-title{
    width: 35%;
  }
  .button-position {
    margin-right: -84%;
  }
}

@media only screen and (min-width: 1601px) and (max-width: 1772px) {
  .container-arrow{
    width: 65%;
  }
  .conainer-third-title{
    width: 31%;
  }
  .container-warning-circle-by-days{
    width: 79%;
  }
  .container-warning-circle{
    width: 81%;
  }
  .text-information{
    width: 155%;
  }
  .button-position{
    margin-right: -82%;
  }
}
