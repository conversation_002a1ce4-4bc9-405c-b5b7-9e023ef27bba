import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {MobileService} from '../../../../common/services/mobile/mobile.service';
import {ContractRecord} from '../../../../common/model/mobile/contract-record/ContractRecord';
import {BaseChartDirective} from 'ng2-charts';
import {ProductMapping} from '../../../../common/model/mobile/product-record/ProductMapping';
import SubscriptionDetails from '../../../../common/model/mobile/product-record/SubscriptionDetails';
import {MobileRecordStatus} from '../../../../common/enum/MobileRecordStatus';
import {ServiziAttiviService} from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import {MobileServiceType} from '../../../../common/enum/MobileServiceType';
import {NormalizeUtils} from '../../../../common/utils/NormalizeUtils';
import AdditionalProduct from '../../../../common/model/mobile/contract-record/AdditionalProduct';
import MainProduct from '../../../../common/model/mobile/contract-record/MainProduct';
import Product from '../../../../common/model/mobile/contract-record/Product';
import {ActiveServiceStatus} from '../../../../common/enum/ServiceStatus';
import {Observable} from 'rxjs/Observable';
import {MobileState} from '../../../../redux/model/MobileState';
import {select} from '@angular-redux/store';
import {MobileActions} from '../../../../redux/mobile/actions';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import SubscriptionAutoricaricaRequest from '../../../mobile/models/PaymentAutoricaricaModels';
import {PaymentService} from '../../../../common/services/payment/payment.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {Utility} from '../../../../common/model/services/userServices.model';


const unlimitedMap = {
  [MobileServiceType.DATI]: 999,
  [MobileServiceType.DATA]: 999,
  [MobileServiceType.ROAMING_DATA]: 999,
  [MobileServiceType.ROAMING_DATA_EU]: 999,
  [MobileServiceType.SMS]: 501,
  [MobileServiceType.SMS_NATIONAL]: 501,
  [MobileServiceType.VOCE]: 43200,
  [MobileServiceType.VOICE_NATIONAL]: 43200,
  [MobileServiceType.INTERNATIONAL_MIN_ALL_ZONES]: 43200,
  [MobileServiceType.VOICE_ON_NET]: 43200
};

const unlimitedChartData = [100, 0];
const emptyChart = [0, 0];

@Component({
  selector: 'app-mobile-layout',
  templateUrl: './mobile-layout.component.html',
  styleUrls: ['./mobile-layout.component.scss']
})
export class MobileLayoutComponent implements OnDestroy {

  @select(['mobile'])
  mobileState: Observable<MobileState>;

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  serviceDataSubscription: Subscription;
  totalSecurityInfo: Utility;
  safeCallInfo: Utility;
  checkid = 0;
  validityPeriods = {};
  disabledCostoRinnovo = {};
  autoricaricaEnabled = {};

  availableServices = {
    Nazionale: {
      DATA: {unit: 'GB', name: MobileServiceType.DATI},
      SMS_NATIONAL: {unit: 'SMS', name: MobileServiceType.SMS},
      VOICE_NATIONAL: {unit: 'Min', name: MobileServiceType.VOCE}
    },
    'Roaming e UK': {
      'Roaming DATA Limit': {unit: 'GB', name: MobileServiceType.DATI},
      SMS_NATIONAL: {unit: 'SMS', name: MobileServiceType.SMS},
      VOICE_NATIONAL: {unit: 'Min', name: MobileServiceType.VOCE}
    },
    'Extra UE': {
      RoamingDataEU_Extra: {unit: 'GB', name: MobileServiceType.DATI},
      RoamingMinutesEU_Extra: {unit: 'Min', name: MobileServiceType.VOCE}
    },
    Internazionale: {
      InternationalMinsVideoAllZones: {unit: 'Min', name: MobileServiceType.VOCE}
    }
  };

  chartTitleSuffixes = {
    [MobileServiceType.VOICE_NATIONAL]: 'vs TUTTI',
    [MobileServiceType.VOICE_ON_NET]: 'vs OPTIMA',
    [MobileServiceType.ROAMING_DATA]: 'in ROAMING'
  };

  public doughnutChartData = [{data: [0, 0], borderWidth: [0, 0]}];

  public doughnutChartsAllData = [];

  public doughnutChartsAllDataOptions = {};


  public colors = [
    {
      backgroundColor: ['#36749D', '#ffffff'],
    }
  ];

  productOptionMap: object;

  @ViewChild('myChart')
  myChart: BaseChartDirective;

  public options = {
    cutoutPercentage: 90,
    maintainAspectRatio: false,
    animation: false,
    responsive: true,
    tooltips: {enabled: false},
    circumference: 5.5,
    rotation: 89.91,
    elements: {
      center: {
        text: 'Desktop',
        color: '#36A2EB',
        fontStyle: 'Helvetica',
        sidePadding: 15
      }
    }
  };

  contractRecords: Array<ContractRecord> = [];

  selectedRecord: ContractRecord;
  selectedService = MobileServiceType.DATA;

  productDescription: object;
  simDetailRecords: any;
  activeProductsMap: object;
  yourOffers: Array<ProductMapping> = [];
  yourOptions: Array<ProductMapping> = [];
  expiresOn: Date;
  balance = 0;
  serviceBalance;

  displayMobileTable: boolean;
  showTariffDescription = true;
  active: string;
  mobileStateSubscription: Subscription;
  groupNamesInfo: string[];
  formGroup: FormGroup;

  constructor(private mobileService: MobileService, private attivService: ServiziAttiviService,
              private mobileActions: MobileActions, private paymentService: PaymentService, private fb: FormBuilder) {
    mobileActions.loadContractRecordsIfNotExist(localStorage.getItem('clientId'));
    this.mobileStateSubscription = this.mobileState.subscribe(state => {
      const {contractRecords, contractRecordsLoaded} = state;
      if (contractRecordsLoaded && contractRecords) {
        this.contractRecords = contractRecords.filter(r => ActiveServiceStatus[this.mobileStatusDecode(r)
          .toUpperCase()]);
        this.activeProductsMap = this.buildActiveProductsMap(contractRecords);
      }
    });
    this.formGroup = this.fb.group({
      group: [null, [Validators.required]]
    });
  }

  switchDisplayMobileTable() {
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      serviceState.services.forEach(service => {
        if (service.serviceName === 'TotalSecurity') {
          this.totalSecurityInfo = service.utilities[0];
        }
        if (service.serviceName === 'SafeCall') {
          this.safeCallInfo = service.utilities.find(utility => utility.additionalInfo.msisdn === this.selectedRecord.msisdnId.toString());
        }
      });
    });
    this.displayMobileTable = !this.displayMobileTable;
    if (!this.displayMobileTable) {
      this.active = '';
    } else {
      this.active = 'active-piano-tariffario';
    }
  }

  showDetails(contractRecord, i) {
    this.checkid = i;
    if (contractRecord) {
      if (this.selectedRecord && this.selectedRecord.id === contractRecord.id) {
        this.selectedRecord = null;
        this.doughnutChartsAllData = [];
        this.displayMobileTable = false;
      } else {
        this.selectedRecord = null;
        this.doughnutChartsAllData = [];
        this.selectedRecord = contractRecord;
        const activeProduct = this.activeProductsMap[contractRecord.msisdnId];
        this.productOptionMap = NormalizeUtils.groupByExpression(activeProduct.productOptions,
          item => item.option.counter.name);
        this.mobileService.loadSimBalance(contractRecord.msisdnId).subscribe(information =>
          this.balance = information ? Number(information) : 0);
        this.mobileService.loadSimDetailRecord(contractRecord.msisdnId).subscribe((response: Array<SubscriptionDetails>) => {
          this.simDetailRecords = NormalizeUtils.normalizeListWithExprssion(response, item => item.groupName);
          for (const groupName in this.simDetailRecords) {
            if (this.simDetailRecords.hasOwnProperty(groupName)) {
              const group = this.simDetailRecords[groupName];
              this.simDetailRecords[groupName] = NormalizeUtils.normalizeListWithExprssion(group, item => item.counter.name);
            }
          }
          this.groupNamesInfo = Object.keys(this.simDetailRecords);
          this.formGroup.get('group').setValue(this.groupNamesInfo[0]);
          this.showGroupInfo();
        });
        this.mobileService.loadProductDescription(contractRecord.mainProductId).subscribe((data) => {
          this.productDescription = data;
          this.doughnutChartData = [{data: [80, 20], borderWidth: emptyChart}];
        });
        this.yourOffers = this.mobileService.getYourOffers(contractRecord);
        this.yourOptions = this.mobileService.getYourOptions(contractRecord);
        if (contractRecord.additionalProducts && contractRecord.additionalProducts.length > 0) {
          contractRecord.additionalProducts.forEach(record => {
            if (record.active && record.product.renewalPeriod > 0) {
              this.expiresOn = new Date(record.expiresOn);
            }
          });
        }
      }
    }
  }

  showGroupInfo() {
    this.doughnutChartsAllData = [];
    for (const key in this.availableServices[this.formGroup.value.group]) {
      if (!(Object.keys(this.productOptionMap).indexOf(key) < 0)) {
        this.selectService(this.formGroup.value.group, key);
      }
    }
  }

  selectService(groupName: string, serviceName, typeOption?: MobileServiceType) {
    this.selectedService = serviceName;
    const selectedOption = typeOption ? typeOption : serviceName;
    const productOption = this.simDetailRecords[groupName][selectedOption];
    if (productOption && productOption.activations.length > 0) {
      const option = productOption.activations.reduce((result, filter) => {
        result['amount'] += filter.amount;
        result['liveAmount'] += filter.liveAmount;
        return result;
      }, {amount: 0, liveAmount: 0});
      let amount = option.amount;
      const isLiveAmountNotANumber = isNaN(option.liveAmount);
      let liveAmount = (serviceName === MobileServiceType.VOICE_NATIONAL && isLiveAmountNotANumber) ||
      isLiveAmountNotANumber ? amount : option.liveAmount;
      if ((selectedOption === 'DATA' || selectedOption === 'Roaming DATA Limit' || selectedOption === 'RoamingDataEU_Extra') && amount !== liveAmount) {
        amount = Math.trunc(amount / 1024);
        liveAmount = (liveAmount / 1024).toFixed(1);
      } else if ((selectedOption === 'DATA' || selectedOption === 'Roaming DATA Limit' || selectedOption === 'RoamingDataEU_Extra') && amount === liveAmount) {
        amount = (amount / 1024).toFixed(1);
        liveAmount = (liveAmount / 1024).toFixed(1);
      }
      if (selectedOption === 'VOICE_NATIONAL' || selectedOption === 'VOICE_ON_NET' || selectedOption === 'RoamingMinutesEU_Extra') {
        amount = Math.trunc(amount);
        liveAmount = Math.trunc(liveAmount);
      }
      const unlimited = amount >= unlimitedMap[selectedOption];
      const chartData = unlimited ? unlimitedChartData : [liveAmount, amount - liveAmount];
      this.doughnutChartData = [{data: chartData, borderWidth: emptyChart}];
      this.serviceBalance = {
        amount, liveAmount,
        name: selectedOption,
        unlimited
      };

      this.doughnutChartsAllDataOptions = {
        chartData: this.doughnutChartData,
        serviceBalance: this.serviceBalance,
        unit: this.availableServices[this.formGroup.value.group][this.selectedService].unit,
        suffix: this.chartTitleSuffixes[this.serviceBalance.name],
      };
      if ((this.showTariffDescription === true && serviceName === 'SMS_NATIONAL' && unlimited === true) || (this.showTariffDescription === true && serviceName === 'VOICE_NATIONAL' && unlimited === true)) {
        this.showTariffDescription = false;
      } else if (this.showTariffDescription === false && unlimited === false) {
        this.showTariffDescription = true;
      }
      if (!typeOption) {
        this.doughnutChartsAllData.push(this.doughnutChartsAllDataOptions);
      }
      if (typeOption === 'VOICE_ON_NET' || typeOption === 'VOICE_NATIONAL') {
        this.doughnutChartsAllData.pop();
        this.doughnutChartsAllData.push(this.doughnutChartsAllDataOptions);
      }
    } else {
      this.serviceBalance = null;
      this.doughnutChartData = [{data: emptyChart, borderWidth: emptyChart}];
    }
  }

  buildActiveProductsMap(contractRecord: ContractRecord[]) {
    const result = {};
    if (contractRecord) {
      contractRecord.forEach(item => {
        result[item.msisdnId] = this.getActiveProduct(item);

        const request = new SubscriptionAutoricaricaRequest();
        request.COD_CLIENTE = localStorage.getItem('clientId');
        request.SUBSCRIPTION_ID = item.id.toString();
        this.paymentService.postPaymentAutoricaricaInformation(request).subscribe(response => {
          this.autoricaricaEnabled[item.msisdnId] = response.RicaricaRicorrente === 1 && response.MaskedPan !== null;
        });

        if (result[item.msisdnId]['prId']) {
          this.mobileService.checkPeriod(localStorage.getItem('clientId'), result[item.msisdnId]['prId']).subscribe(period => {
            if (period !== null && period.text !== '') {
              this.validityPeriods[item.msisdnId] = period.text;
            }
            (period !== null && period.text !== null && item.descrizioneCanaleAcquisto === 'Canale Ag. Dirette')
              ? this.disabledCostoRinnovo[item.msisdnId] = false
              : this.disabledCostoRinnovo[item.msisdnId] = true;
          });
        }
      });
    }
    return result;
  }

  getActiveProduct(selectedRecord: ContractRecord): MainProduct | Product {
    const {mainProduct, additionalProducts} = selectedRecord;
    mainProduct.prId = selectedRecord.id;
    if (mainProduct.productMapping && mainProduct.productMapping.flagSoloDati || !additionalProducts
      || additionalProducts.length === 0) {
      return mainProduct;
    }
    return this.getActiveAdditionalProduct(additionalProducts);
  }

  getActiveAdditionalProduct(additionalProducts: Array<AdditionalProduct>): MainProduct | Product {
    const active = additionalProducts.find(item => item.active && item.product
      && item.product.renewalPeriod >= 1);
    if (active) {
      active.product.prId = active.id;
    }
    return active ? active.product : {} as Product;
  }

  mobileStatusDecode(record) {
    if (record && record.lastStatusChange && record.lastStatusChange.status) {
      return this.attivService.mobileStatusDecode(record.lastStatusChange.status.name);
    }
  }

  hasDetails(record) {
    if (record && record.lastStatusChange && record.lastStatusChange.status) {
      const status = record.lastStatusChange.status.description;
      return status === MobileRecordStatus.ACTIVE || status === MobileRecordStatus.INITIACTIVE || status === MobileRecordStatus.LOST_SIM
        || status === MobileRecordStatus.HARD_SUSPENSION || status === MobileRecordStatus.SOFT_SUSPENSION;
    }
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.mobileStateSubscription]);
  }

}
