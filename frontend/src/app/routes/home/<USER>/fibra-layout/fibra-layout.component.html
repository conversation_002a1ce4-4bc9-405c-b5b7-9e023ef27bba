<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
 <div class="title">
   CONTROLLA STATO E DETTAGLIO DELLA TUA LINEA INTERNET
 </div>
</div>
<div class="col-md-5 no-padding-mobile block main {{detail?'blockWithDetail':''}}">
  <div *ngFor="let service of sortedServices">
    <div *ngFor="let utility of service.utilities ; let i = index" class="lineBlock">
      <div
        class="{{!detail?'subBlock':'subBlockWithDetails'}} plainBlock {{( detail && (utility.id == detail.id)) ?'active':'noActive'}}">

        <p><b>Stato:</b> {{utility.status}}</p>
        <p><b>Linea: </b> {{utility.utNumber}}</p>
        <p class="addition" *ngIf="podDetails[utility.utNumber]?.sede">
          <b>Indirizzo: </b>
          {{podDetails[utility.utNumber].sede.cap}},
          {{podDetails[utility.utNumber].sede.descrizioneSede | titlecase}}</p>

        <div *ngIf="!(detail && (utility.id == detail.id))"
             class="button" id=" {{utility.id}}" (click)="selectValue(utility, i)">Dettaglio
        </div>
        <div *ngIf="(detail && (utility.id == detail.id))" class="button nascondi" (click)="hide()">Nascondi</div>
      </div>
      <ng-container *ngIf="checkid == i">
        <div class="visible-sm-block visible-xs-block">
          <ng-container *ngTemplateOutlet="fibraDetails"></ng-container>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div class="hidden-xs hidden-sm">
  <ng-container *ngTemplateOutlet="fibraDetails"></ng-container>
</div>


<ng-template #fibraDetails>
  <div *ngIf="detail" class="col-md-6 detail">
    <div class="col-md-12 no-padding-mobile">
      <div class="desktopView">
        <span class="detailRow bigLetter"><b>Linea: {{detail.utNumber}}</b></span>
        <span class="detailRow smallLetter"><b>Stato: </b>{{detail.status| titlecase}}</span>
      </div>
      <span class="detailRow smallLetter"><b>Data Attivazione: </b>{{detail.startDate| date : "dd/MM/y"}}</span>
      <div *ngIf="podDetails[detail.utNumber]">
        <span class="detailRow smallLetter"><b>Tipologia linea: </b>{{podDetails[detail.utNumber].descrizioneTipoLinea | titlecase}}</span>
        <span *ngIf="podDetails[detail.utNumber]?.sede" class="detailRow smallLetter"><b>Indirizzo fornitura:
          </b>{{podDetails[detail.utNumber].sede.cap}}, {{podDetails[detail.utNumber].sede.descrizioneSede | titlecase}}</span>
        <span *ngIf="podDetails[detail.utNumber].codiceMigrazione"
              class="detailRow smallLetter"><b>Codice migrazione: </b>{{podDetails[detail.utNumber].codiceMigrazione}}</span>
        <span *ngIf="podDetails[detail.utNumber].tipoxDSL"
              class="detailRow smallLetter"><b>Tipologia servizio: </b>{{decodeType(podDetails[detail.utNumber].profiloProdottoAttivo)}}</span>
      </div>
    </div>
  </div>
  <div class="col-md-1 icons" *ngIf="detail">
    <section class="icon-selection" *ngIf="pdf">
      <button class="app--btn-dropdown no-hover"  [matMenuTriggerFor]="menu">
        <i class="icon modifica"></i>
      </button>
      <div class="mat-menu-style">
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below">
        <div class="mat-menu-style">
        <button mat-menu-item *ngIf="pdf.length===0">
          <span> No PDF </span>
        </button>
<!--        <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--          Variazioni e richieste-->
<!--        </button>-->
        <button class="menu-button odds-bg" mat-menu-item *ngFor="let pdfRow of pdf">
          <span class="icon-pdf-load"> </span>
          <a target='_blank' href="{{pdfRow.link}}">{{pdfRow.name}}</a>
        </button>
        </div>
      </mat-menu>
      </div>
    </section>
  </div>
</ng-template>
