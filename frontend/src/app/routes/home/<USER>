import {NgModule, NO_ERRORS_SCHEMA} from '@angular/core';
import {HomeComponent} from './containers/home/<USER>';
import {RouterModule, Routes} from '@angular/router';
import {HttpService} from '../../services/http.service';

import { MatMenuModule, MatTableModule } from '@angular/material';
import { CdkTableModule } from '@angular/cdk/table';
import { CommonModule } from '../../common/common.module';
import { UserServicesService } from '../profilePage/userServices/userServices.service';
import { SharedModule } from '../../shared/shared.module';
import { SelectModule } from 'ng2-select';
import { RouteService } from '../../layout/submenu/route.service';
import { MDBBootstrapModule } from 'angular-bootstrap-md';
import { CommonModule as AngularCommonModule } from '@angular/common';
import { HomeService } from './containers/home/<USER>';
import { IndexLayoutComponent } from './containers/index-layout/index-layout.component';
import { MobileLayoutComponent } from './containers/mobile-layout/mobile-layout.component';
import { MobileModule } from '../mobile/mobile.module';
import { ChartsModule } from 'ng2-charts';
import { LuceLayoutComponent } from './containers/luce-layout/luce-layout.component';
import { GasLayoutComponent } from './containers/gas-layout/gas-layout.component';
import { ServiziAttiviService } from '../../common/services/servizi-attivi/servizi-attivi.service';
import { FibraLayoutComponent } from './containers/fibra-layout/fibra-layout.component';
import { FissoLayoutComponent } from './containers/fisso-layout/fisso-layout.component';
import { AmazonPrimeComponent } from './containers/amazon-prime/amazon-prime.component';
import { ServiziAggiuntniviComponent } from './containers/servizi-aggiuntnivi-layout/servizi-aggiuntnivi.component';
import { ScioltoGasComponent } from './sciolto/sciolto-gas/sciolto-gas.component';
import { ScioltoFibraComponent } from './sciolto/sciolto-fibra/sciolto-fibra.component';
import { ScioltoMobileComponent } from './sciolto/sciolto-mobile/sciolto-mobile.component';
import { ScioltoTeleconsultoMedicoComponent } from './sciolto/sciolto-teleconsulto-medico/sciolto-teleconsulto-medico.component';
import { ScioltoLuceComponent } from './sciolto/sciolto-luce/sciolto-luce.component';
import { ScioltoFissoComponent } from './sciolto/sciolto-fisso/sciolto-fisso.component';


const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
    children: [
      {path: 'index', component: IndexLayoutComponent},
      {path: 'mobile', component: MobileLayoutComponent},
      {path: 'sciolto/mobile', component: ScioltoMobileComponent},
      {path: 'luce', component: LuceLayoutComponent},
      {path: 'sciolto/luce', component: ScioltoLuceComponent},
      {path: 'gas', component: GasLayoutComponent},
      {path: 'sciolto/gas', component: ScioltoGasComponent},
      {path: 'internet', component: FibraLayoutComponent},
      {path: 'sciolto/internet', component: ScioltoFibraComponent},
      {path: 'fisso', component: FissoLayoutComponent},
      {path: 'sciolto/fisso', component: ScioltoFissoComponent},
      {path: 'amazonprime', component: AmazonPrimeComponent},
      {path: 'servizi-aggiuntnivi', component: ServiziAggiuntniviComponent},
      {path: 'sciolto/teleconsulto-medico', component: ScioltoTeleconsultoMedicoComponent}
    ],
    resolve: {}
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    SharedModule,
    SelectModule,
    MatTableModule,
    CdkTableModule,
    CommonModule,
    AngularCommonModule,
    MatMenuModule,
    MDBBootstrapModule.forRoot(),
    MobileModule,
    ChartsModule
  ],
  providers: [HttpService,
    HttpService,
    RouteService,
    UserServicesService, HomeService, ServiziAttiviService
  ],
  declarations: [
    HomeComponent,
    IndexLayoutComponent,
    AmazonPrimeComponent,
    // ScioltoFissoComponent,
    // ScioltoGasComponent,
    // ScioltoFibraComponent,
    // ScioltoMobileComponent,
    // ScioltoTeleconsultoMedicoComponent,
    // ScioltoLuceComponent,
    // AmazonPrimeLayoutComponent,
    // LuceLayoutComponent,
    // GasLayoutComponent,
    // FibraLayoutComponent,
    // FissoLayoutComponent
  ],
  exports: [
    RouterModule
  ],
  schemas: [NO_ERRORS_SCHEMA]
})
export class HomeModule {
}
