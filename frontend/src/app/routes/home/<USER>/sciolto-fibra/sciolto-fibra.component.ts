import {Component, OnInit} from '@angular/core';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {ScioltoService} from '../../../../common/services/sciolto/sciolto.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import {Cluster} from '../../../../common/enum/Cluster';

@Component({
  selector: 'app-sciolto-fibra',
  templateUrl: './sciolto-fibra.component.html',
  styleUrls: ['./sciolto-fibra.component.scss']
})
export class ScioltoFibraComponent implements OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userCluster: string;
  price: number;
  promoTexts: string[] = [];
  withoutIncident: boolean;
  showModalWindowAboutAlreadyOpenIncident: boolean;
  showModalWindowAboutOpenNewIncident: boolean;
  codiceOfferta: string;
  additionalText: string;

  constructor(private scioltoService: ScioltoService, private incidentEventService: IncidentEventService, private offerService: OffersService) {
    this.offerService.checkIncidentEventCrossSelling().subscribe(result => {
      this.withoutIncident = result;
    });
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
        this.additionalText = this.userCluster === Cluster.CONSUMER ? '(IVA inclusa)' : '(IVA esclusa)';
      }
    });
    this.scioltoService.getGeneralScioltoInformation()
      .subscribe(generalInformation => {
        if (generalInformation.response[0]) {
          this.codiceOfferta = generalInformation.response[0].fields.Offerta;
          this.scioltoService.getScioltoInformation(generalInformation.response[0].fields.Offerta, this.userCluster).subscribe(information => {
            this.price = information.response[0].serviziAdsl[0].listini[0].canone;
            const internetPromos = information.response[0].promo.filter(item =>
              item.Elements.some(value => value.NomeServizio === 'ADSL')
            );
            const internetPromoOpzionali = information.response[0].promoOpzionali.filter(item =>
              item.Elements.some(value => value.NomeServizio === 'ADSL')
            );
            this.promoTexts = [...internetPromos, ...internetPromoOpzionali].map(promo => promo.DescrizioneExt || promo.descrizioneExt);
          });
        }
      });
  }

  ngOnInit() {
  }

  hideModalWindow() {
    this.showModalWindowAboutAlreadyOpenIncident = false;
    this.showModalWindowAboutOpenNewIncident = false;
  }

  buttonClick() {
    if (this.withoutIncident) {
      this.incidentEventService.openIncidentEventForCrossSelling('INTERNET', this.codiceOfferta).subscribe(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.showModalWindowAboutOpenNewIncident = true;
          this.withoutIncident = false;
        }
      });
    } else {
      this.showModalWindowAboutAlreadyOpenIncident = true;
    }
  }
}
