<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
  <div class="title">
    INTERNET
  </div>
</div>
<div class="col-md-11 no-padding-mobile block main">

  <div class="flex-block">
    <img class="icon-banner" src="/assets/img/sciolto/INTERNET_img.png" alt="INTERNET">
    <div class="banner-text">Passa ad Optima per il servizio <b>INTERNET!</b> <!--Ti riserviamo un’offerta di soli
      <b>{{price}} € al mese</b> {{additionalText}}.-->
    </div>
  </div>

  <div class="promo-block" *ngIf="promoTexts && promoTexts.length > 0">
    <div class="flex-promo-header">
      <img class="promo-icon" src="/assets/img/sciolto/Promo.png" alt="PROMO">
      <div><b>PROMO PER TE</b></div>
    </div>
    <div>
      <p *ngFor="let promoText of promoTexts">
        {{promoText}}
      </p>
    </div>
  </div>

  <div class="small-text">Per ricevere info di dettaglio clicca qui!</div>
  <button class="btn btn-success" (click)="buttonClick()">CONTATTACI ORA</button>
</div>

<div class="modal-div display" *ngIf="showModalWindowAboutAlreadyOpenIncident">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Risulta già una richiesta di ricontatto per un altro servizio. Potrai richiedere ulteriori
      informazioni su altri servizi durante la chiamata che riceverai.
    </div>
    <div class="modal-text">Grazie!</div>
  </div>
</div>
<div class="modal-div display" *ngIf="showModalWindowAboutOpenNewIncident">
  <div class="inner-modal-div" style="min-height: 240px">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="modal-text">Grazie, ti contatteremo a breve per fornirti tutte le informazioni di cui hai bisogno.</div>
  </div>
</div>
