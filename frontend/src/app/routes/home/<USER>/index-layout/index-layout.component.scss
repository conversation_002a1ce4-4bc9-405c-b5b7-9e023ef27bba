@import "~angular-bootstrap-md/scss/bootstrap/bootstrap";
@import '../../../../shared/styles/app/variables.scss';
@import '../../../../shared/styles/app/media-queries.scss';
@import "~app/shared/styles/colors";

body {
  overflow-y: hidden;
}

.alan {
  width: 60%;
  margin-left: 20%;
  //margin-left: 50px;
  //width: 82%;
}

.fatture-icon-info-block {
  background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_Fatture.png') no-repeat;
  background-size: contain;
  height: 20px;
}

// .fatture-icon-archive {
//   background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_Archivio_Fatture.png') no-repeat;
//   background-size: contain;
//   height: 30px;
// }

// .fatture-icon-download {
//   background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_Download.png') no-repeat;
//   background-size: contain;
//   height: 20px;
// }

.fatture-info-block-icon {
  float: left;
  height: 25px;
  padding: 0 20px;
  text-align: center;
  margin-left: 20px;
}

.fatture-info-block {
  color: #36749d;
  border: #36749d 1px solid;
  justify-content: center;
  background-color: white;
  border-radius: 5px;
  //max-width: 330px;
  height: 105px;
}

.fatture-info-block-paga-btn {
  width: 70px;
}

.fatture-info-btn-wrapper {
  //margin-left: 10px;
  //max-width: 230px;
}

.fatture-info-btn {
  color: #36749d;
  border: #36749d 1px solid;
  text-align: center;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 5px;

  //width: 230px;
  height: 37px;
  margin-left: -5px;
  cursor: pointer;

  //padding-bottom: 0;
}

.fatture-info-block-inner {
  margin-top: 5px;
  margin-bottom: 5px;
}

.fatture-info-block-first {
  border-bottom: #36749d 1px solid;
}

.mt-5 {
  margin-top: 5px;
}

.stabilizatto-button-success {
  background-color: #9BC641;
  color: white;
  border: 1px solid #9BC641;
  border-radius: 5px;
  cursor: pointer;
}

.disabled {
  background-color: #e5e5e5;
  border: #979797 solid 1px;
  border-radius: 5px;
  color: #979797;
}

.col-md-9 {
  padding-left: 30px;
}

.mobile {
  display: none;
  float: left;
}

.for-mobile {
  margin-left: 15px;
  margin-right: 15px;
  display: none;
  float: left;
}

.item {
  height: 200px;
}

#myCarousel {
  height: 200px;
}

.carousel-indicators {
  top: 50% !important;
}

.iframeText {
  color: #36749d;
  width: 70%;
  text-align: center;
  font-size: 16px;
  line-height: 19px;
  padding: 5px 0;
  margin: 25px auto 0;

}

::ng-deep.carousel-indicators li {
  background-color: #b6cce3 !important;
  border: none;
  height: 5px;
  width: 5px;
  margin: 0 3px;
}

::ng-deep.carousel .carousel-indicators {
  bottom: -35px;
}

::ng-deep.carousel-indicators .active {
  background-color: $dark-blue !important;
  height: 5px;
  margin: 0 3px;
  width: 5px;
}

.desktop {
  margin-bottom: 15px
}

.videoWrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 */
  height: 0;
  margin-top: 10px;
}

.videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: auto;
}

.view {
  margin-left: 5%;

  .appBanner {
    width: 87%;
    height: 253px;
  }
}

.separator {
  height: 5%;
}

::ng-deep.carousel {
  width: 80%;
}

.amazon-prime-block {
  background: $black-blue;
  text-align: center;
  background: url("../../../../../assets/img/amazon/banner_amazon.png") no-repeat center;
  background-size: contain;
  height: 125px;
  cursor: pointer;
}

.amazon-prime-old-statement{
  text-align: center;
  background: url("../../../../../assets/img/amazon/banner_amazone_prime.png") no-repeat center;
  background-size: contain;
  height: 85px;
  margin-top: 7px;
  cursor: pointer;
}

@media only screen and (min-width: 1000px) {
  .col-md-6 {
    max-width: 49%
  }
}

.block {
  border: 1px solid #b6cce3;
  border-radius: 5px;
  padding: 10px 20px;
  background-color: white;

  h2 {
    color: #e54c36;
    font-weight: bold;
    padding: 15px 0;
  }

  .mainText {
    height: 15px;
    color: #36749d;
    font-size: 18px;
    font-weight: bold;
  }

  a {
    color: black;
    font-size: 17px;
    font-weight: 400;
  }
}

.text {
  padding: 10px 20px;
}

.text-contact-menu {
  padding: 0 0 0 20px;
}

.menu {
  border: 1px solid #b6cce3;
  border-radius: 5px;
  padding: 15px 10px 10px;
  margin: 0 0 27px 0;

  .icon {
    font-size: 60px;
    line-height: 100px;
    float: left;
    color: #e54c36;
    margin: auto;
  }

  .icon-envelope, .icon-documents {
    line-height: 0;
  }

  .spedizioni-icon {
    background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_SpedizionoIcon.png') no-repeat;
    background-size: contain;
    margin: auto;
    width: 55px;
  }

  .autolettura-iocn {
    background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_Autolettura.png') no-repeat;
    background-size: contain;
    margin: auto;
    width: 55px;
  }

  .segnalazioni-icon {
    background: url('../../../../../assets/img/optima/le_tue_segnalazioni.png') no-repeat;
    background-size: contain;
    margin: auto;
    width: 55px;
  }

  .fatture-icon {
    background: url('../../../../../assets/img/optima/Set_Icone_AreaClienti_Fatture.png') no-repeat;
    background-size: contain;
    margin: auto;
    width: 55px;
  }

  .text {
    float: left;
    color: #36749d;

    .mainText {
      line-height: 12px;
      font-size: 19px;
      font-weight: bold;
    }

    .subText {
      font-size: 14px;
    }
  }
}

.menuBlocks {
  background-color: white;

  a:hover {
    text-decoration: none;
  }
}

.menu:hover {
  background-color: #f0f5f9;
}

.icon {
  float: left;
  height: 60px;
  padding: 0 20px;
  text-align: center;
  margin-left: 20px;
}

.telegram {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Telegram.png") no-repeat center;
  background-size: contain;
}

.facebook {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Facebook.png") no-repeat center;
  background-size: contain;
}

.chatlive-icon {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Colibri.png") no-repeat center;
  background-size: contain;
}

.google {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Google.png") no-repeat center;
  background-size: contain;
}

.notificationBlock {
  padding: 15px 0;
  line-height: 8px;

  .them {
    color: black;
  }

  .mes {
    color: #36749d;
  }
}

@media only screen and (max-width: 1600px) {
  .carousels {
    padding: 0;
    margin-left: -8%;

    .view {
      width: 308px;

      .appBanner {
        margin-left: 28%;
        height: 80%;
        margin-top: 5px;
      }

      .top-view {
        margin-top: 0;
      }
    }
  }
  .amazon-prime-block {
    height: 119px;
  }
  ::ng-deep.carousel {
    width: 100%;
  }
  .block {
    h2 {
      color: #36749d;
      font-weight: bold;
      padding: 15px 0;
      font-size: 18px;
    }

    .mainText {
      height: 15px;
      color: #36749d;
      font-size: 16px;
    }

    a {
      color: black;
      font-size: 15px;
    }
  }

  .text {
    padding: 10px 20px 0 20px;
  }

  .menu {
    border: 1px solid #b6cce3;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 0 0 15px 0;

    .icon {
      font-size: 60px;
      line-height: 100px;
    }

    .icon-envelope, .icon-documents {
      line-height: 0;
    }

    .text {
      .mainText {
        line-height: 10px;
        font-size: 15px;
      }

      .subText {
        font-size: 14px;
      }
    }
  }
  .icon {
    height: 60px;
    padding: 0 20px;
    margin-left: 20px;
  }
}

@media only screen and (max-width: 1300px) {
  .amazon-prime-block {
    height: 94px;
  }
  .carousels {
    margin-left: -8%;

    .view {
      width: 60%;

      .appBanner {
        margin-left: 27%;
        height: 80%;
        margin-top: 5px;
      }

      .top-view {
        margin-top: 0;
      }
    }
  }
}

@media only screen and (max-width: 990px) {
  .amazon-prime-block {
    height: 109px;
  }

  .amazon-prime-old-statement {
    height: 77px;
  }
  ::ng-deep.carousel {
    width: 100%;
  }
  .block {
    h2 {
      color: #36749d;
      font-weight: bold;
      padding: 15px 0;
      font-size: 16px;
    }

    .mainText {
      height: 4px;
      color: #36749d;
      font-size: 14px;
    }

    a {
      color: black;
      font-size: 13px;
    }
  }

  .text {
    padding: 10px 10px;
  }

  .menu {
    border: 1px solid #b6cce3;
    border-radius: 5px;
    padding: 5px 10px;
    margin: 0 0 10px 0;

    .icon {
      font-size: 45px;
      line-height: 110px;
      margin-left: -5px;
    }

    .icon-envelope, .icon-documents {
      line-height: 2;
    }

    .text {
      .mainText {
        line-height: 15px;
        font-size: 15px;
      }

      .subText {
        font-size: 14px;
      }
    }
  }
  .icon {
    height: 50px;
    padding: 0 10px;
    margin-left: 10px;
  }
}

@media only screen and (max-width: 767px) {
  .iframeText {
    display: block;
  }
  .carousels {
    margin-left: 6%;

    .view {
      width: 60%;

      .appBanner {
        margin-top: 50px;
        margin-left: 34%;
        height: 80%;
      }
    }
  }
  .videoWrapper {
    display: block;
  }
  .forDesktop {
    display: none;
  }
  .forMobile {
    display: block;
  }
  .for-mobile {
    display: block;
  }
  .mobile {
    display: block;
    width: 100%;
    margin: 15px auto 0 auto;
    padding: 0;
    border: 1px solid #b6cce3;
    border-radius: 5px;
  }
  .menu {
    margin-top: 15px;
    text-align: center;
  }

  .desktop {
    display: none;
  }
  .block {
    border-radius: 5px;
    padding: 10px 20px;

    h2 {
      color: #e54c36;
      padding: 15px 0;
    }

    .mainText {
      height: 15px;
      font-size: 18px;
    }

    a {
      font-size: 17px;
    }
  }

  .text {
    padding: 10px 20px;
  }


  .menu {
    .icon {
      font-size: 60px;
      line-height: 100px;
      float: left;
      width: auto;
      margin: auto;
    }

    .text {
      width: 75%;
      margin-left: -20px;
      text-align: left;

      .mainText {
        line-height: 12px;
        font-size: 19px;
      }

      .subText {
        font-size: 17px;
      }
    }
  }

  .icon {
    height: 70px;
    padding: 0 20px;
    margin-left: 20px;

  }
  .alan {
    margin-top: 50px;
  }
  .appBanner {
    margin-top: 50px;
  }
}

@media only screen and (max-width: 600px) {
  .amazon-prime-block {
    height: 135px;
  }
  .amazon-prime-old-statement {
    height: 95px;
  }
  .view {
    width: 75%;

    .appBanner {
      margin-top: 50px;
      margin-left: 16%;
      height: 80%;
    }
  }
}

@media only screen and (max-width: 400px) {
  .view {
    width: 80%;

    .appBanner {
      margin-top: 50px;
      margin-left: 39px;
      height: 80%;
    }
  }
  .amazon-prime-block {
    height: 87px;
  }
  .amazon-prime-old-statement {
    margin-top: 0;
  }
  ::ng-deep.carousel {
    width: 100%;
  }
  .icon {
    height: 60px;
    padding: 0 10px;
    margin-left: 10px;
  }
  .text {
    padding: 10px 0 10px 20px;
  }
  .menu {
    .icon {
      width: 40px;
      margin-left: 2px
    }

    .text {
      .mainText {
        line-height: 17px;
        font-size: 14px;
      }

      .subText {
        font-size: 12px;
      }
    }
  }
  .block {
    border-radius: 5px;
    padding: 10px 20px;

    h2 {
      color: #e54c36;
      padding: 15px 0;
    }

    .mainText {
      height: 13px;
      font-size: 13px;
    }

    a {
      font-size: 13px;
    }
  }
  .alan {
    margin-top: 50px;
    margin-left: 31px;
  }
  .appBanner {
    margin-top: 50px;
    margin-left: 31px;
  }
}

@media only screen and (max-width: 265px) {
  .icon {
    height: 60px;
    padding: 0 10px 70px 10px;
  }
}

@media only screen and (max-width: 1630px) {
  .fatture-info-btn {
    height: 75px;
  }

  .fatture-info-block {
    height: 180px;
  }
}

@media only screen and (max-width: 767px) {
  .fatture-info-block {
    height: 105px;
  }

  .fatture-info-btn {
    height: 37px;
  }
}

@media only screen and (max-width: 576px) {
  .fatture-info-btn {
    margin-left: -15px;
    margin-top: 5px;
  }
}

