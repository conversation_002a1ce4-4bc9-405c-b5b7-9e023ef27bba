<div class="col-lg-12 col-md-12 col-sm-12 servizi-aggiuntnivi">

  <div class="col-lg-12 col-md-12 page-title" *ngIf="!isMobile">
    <div class="icon-servizi-aggiuntivi"></div>
    <div class="text">SERVIZI AGGIUNTIVI</div>
  </div>

  <div class="table-container">
    <div class="table-border" *ngFor="let service of this.userServices">
      <div class="header-table-container">
        <div class="{{getIconClass(service.serviceName)}}"></div>
        <div class="header-table">{{service.serviceName.toUpperCase()}}</div>
      </div>
      <div>
        <div class="table-body" *ngFor="let information of service.utilities">
          <div class="button-container">
            <div>
              <div><b>Stato:</b> {{information.status}}</div>
              <div><b>Data attivazione:</b> {{information.startDate | date : 'dd/MM/yyyy'}}</div>
            </div>
            <button [routerLink]="getRouterLink(service.serviceName)">ENTRA</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
