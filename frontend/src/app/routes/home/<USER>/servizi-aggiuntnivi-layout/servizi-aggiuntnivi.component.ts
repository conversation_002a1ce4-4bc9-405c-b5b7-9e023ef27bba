import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import {UserServices} from '../../../../common/model/services/userServices.model';
import {fromJS} from 'immutable';
import {ServiceStatus} from '../../../../common/enum/ServiceStatus';

@Component({
  selector: 'app-servizi-aggiuntnivi',
  templateUrl: './servizi-aggiuntnivi.component.html',
  styleUrls: ['./servizi-aggiuntnivi.component.scss']
})
export class ServiziAggiuntniviComponent implements OnInit {

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;
  isMobile = window.innerWidth <= 991;
  userServices: Array<UserServices> = [];
  serviceNamesMap = {
    'AMAZON': 'AMAZON PRIME',
    'TutelaLegale': 'Tutela Legale',
    'TotalSecurity': 'Total Security',
    'SafeCall': 'Safe Call'
  };
  validServiceNames = ['AMAZON PRIME', 'Teleconsulto medico', 'Tutela Legale', 'Assistenza H24', 'Total Security', 'Safe Call'];

  constructor() {
    this.serviceData.subscribe(userServices => {
      this.userServices = fromJS(userServices.services)
        .map(service => this.updateService(service))
        .filter(service => this.isValidService(service))
        .toJS();
    });
  }

  updateService(service) {
    const updatedService = service.set('utilities', service.get('utilities').filter(this.isActiveUtility));
    const serviceName = updatedService.get('serviceName');
    return updatedService.set('serviceName', (this.serviceNamesMap[serviceName] || serviceName));
  }

  isActiveUtility(utility) {
    const status = utility.get('status');
    return status === ServiceStatus.ATTIVATO || status === ServiceStatus.IN_ATTIVAZIONE || status === ServiceStatus.ATTIVO;
  }

  isValidService(service) {
    const utilities = service.get('utilities');
    const serviceName = service.get('serviceName');
    return utilities !== null && !utilities.isEmpty() && this.validServiceNames.includes(serviceName);
  }

  getRouterLink(serviceName: string): string {
    switch (serviceName) {
      case 'AMAZON PRIME':
        return '/faidate/servizi-attivi/amazon-prime';
      case 'Tutela Legale':
        return '/faidate/servizi-attivi/tutela-legale';
      case 'Teleconsulto medico':
        return '/faidate/servizi-attivi/teleconsulto-medico';
      case 'Assistenza H24':
        return '/faidate/servizi-attivi/assistenza-h24';
      case 'Total Security':
        return '/faidate/servizi-attivi/total-security';
      case 'Safe Call':
        return '/faidate/servizi-attivi/safe-call';
    }
  }

  getIconClass(serviceName: string): string {
    switch (serviceName) {
      case 'Teleconsulto medico':
        return 'doctor-picture';
      case 'AMAZON PRIME':
        return 'amazon-short-icon';
      case 'Assistenza H24':
        return 'assistance-picture';
      case 'Tutela Legale':
        return 'legal-protection-picture';
      case 'Total Security':
        return 'total-security-picture';
      case 'Safe Call':
        return 'safe-call-picture';
    }
  }

  ngOnInit() {
  }
}
