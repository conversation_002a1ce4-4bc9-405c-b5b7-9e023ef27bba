<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
  <div class="title">
    CONTROLLA STATO E DETTAGLIO DELLA TUA LINEA TELEFONICA
  </div>
</div>
<div class="col-md-12 contentBlock">
  <div class="col-md-5 block main {{detail?'blockWithDetail':''}} no-padding-mobile">
    <div *ngFor="let service of sortedServices">
      <div *ngFor="let utility of service.utilities; let i = index" class="lineBlock">
        <div
          class="{{!detail?'subBlock':'subBlockWithDetails'}} plainBlock
          {{( detail && (utility.id == detail.id)) ?'active':'noActive'}}">

          <p><b>Stato: </b>{{utility.status}}</p>
          <p><b>Linea: </b>{{utility.utNumber}}</p>
          <p class="addition"
             *ngIf="podDetails[utility.utNumber]&&podDetails[utility.utNumber].variazioniLinea[0].sede">
            <b>Indirizzo: </b>
            {{podDetails[utility.utNumber].variazioniLinea[0].sede.descrizioneSede | titlecase}}</p>

          <div *ngIf="!(detail && (utility.id == detail.id))"
               class="button" (click)="selectValue(utility, i)">Dettaglio
          </div>
          <div *ngIf="(detail && (utility.id == detail.id))" class="button nascondi" (click)="hide()">Nascondi</div>
        </div>
        <ng-container *ngIf="checkid == i">
          <div class="visible-xs-block visible-sm-block">
            <ng-container *ngTemplateOutlet="fissoDetails"></ng-container>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="hidden-xs hidden-sm">
    <ng-container *ngTemplateOutlet="fissoDetails"></ng-container>
  </div>
</div>


<ng-template #fissoDetails>
  <div *ngIf="detail" class="col-md-6 block detail">
    <div class="col-md-9 ">
      <div class="desktopView">
        <span class="detailRow bigLetter"><b>Linea: {{detail.utNumber}}</b></span>
        <span class="detailRow smallLetter"><b>Stato: </b>{{detail.status}}</span>

      </div>
      <span class="detailRow smallLetter"><b>Data Attivazione: </b>{{detail.startDate| date : "dd/MM/y"}}</span>
      <div *ngIf="podDetails[detail.utNumber]">
        <span
          class="detailRow smallLetter"><b>Tipologia servizio: </b>{{podDetails[detail.utNumber].tipoCarrier}}</span>
        <div
          *ngIf="podDetails[detail.utNumber].variazioniLinea && podDetails[detail.utNumber].variazioniLinea.length>0">
          <span *ngIf="podDetails[detail.utNumber].variazioniLinea[0].linea" class="detailRow smallLetter"><b>Tipologia linea:
          </b>{{podDetails[detail.utNumber].variazioniLinea[0].linea.descrizioneTipoLinea}}</span>
          <span *ngIf="podDetails[detail.utNumber].variazioniLinea[0].sede" class="detailRow smallLetter"><b>Indirizzo fornitura:
          </b>{{podDetails[detail.utNumber].variazioniLinea[0].sede.descrizioneSede | titlecase}}</span>
          <span *ngIf="podDetails[detail.utNumber].variazioniLinea[0].codiceMigrazioneOpt"
                class="detailRow smallLetter"><b>Codice migrazione: </b>{{podDetails[detail.utNumber].variazioniLinea[0].codiceMigrazioneOpt }}</span>
          <span class="detailRow smallLetter" *ngIf="podDetails[detail.utNumber].opzioni.length > 0"><b>Opzioni:</b>
          <p class="pod-details-option"
             *ngFor="let option of podDetails[detail.utNumber].opzioni">- {{option.descrizioneOpzione}}</p>
        </span>
        </div>
      </div>
    </div>
  </div>
  <!--Hide icons light version fix-->
  <div class="col-md-1 icons" *ngIf="detail">
    <section class="icon-selection" *ngIf="pdf">
      <button class="app--btn-dropdown no-hover" [matMenuTriggerFor]="menu">
        <i class="icon modifica"></i>
      </button>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below">
        <div class="mat-menu-style">
          <button mat-menu-item *ngIf="pdf.length===0">
            <span> No PDF </span>
          </button>
<!--          <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--            Variazioni e richieste-->
<!--          </button>-->
          <button class="menu-button odds-bg" mat-menu-item *ngFor="let pdfRow of pdf">
            <span class="icon-pdf-load"> </span>
            <a target='_blank' href="{{pdfRow.link}}">{{pdfRow.name}}</a>
          </button>
        </div>
      </mat-menu>
    </section>
  </div>
</ng-template>
