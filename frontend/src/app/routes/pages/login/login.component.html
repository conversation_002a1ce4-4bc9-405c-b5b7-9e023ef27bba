<div class="wrapper">
  <div class="flex-panel">

    <div class="left-part-flex hide-block">
      <div class="logo"></div>
      <div class="servizi-picture"></div>
    </div>

    <div class="line hide-block"></div>

    <div class="right-part-flex">

      <p class="text-center header-font-size"><b>Accedi a Optima Italia</b></p>
      <form [formGroup]="valForm" role="form" name="loginForm" novalidate=""
            (submit)="submitForm($event, valForm.value)">

        <div class="field-error">
          <input class="input-field" id="inputUserName" type="text" name="username" placeholder="Inserisci il tuo Codice cliente o l’e-mail"
                 autocomplete="off" formControlName="username"/>
          <span class="text-span-danger"
                *ngIf="valForm.controls['username'].hasError('required') && (valForm.controls['username'].dirty || valForm.controls['username'].touched)">Campo obbligatorio</span>
        </div>

        <div class="field-error">
          <input class="input-field" id="exampleInputPassword1" type="password" name="password" placeholder="Password"
                 formControlName="password"/>
          <span class="text-span-danger"
                *ngIf="valForm.controls['password'].hasError('required') && (valForm.controls['password'].dirty || valForm.controls['password'].touched)">Campo obbligatorio</span>
        </div>

        <span class="text-danger center-block">{{mess}}</span>
        <span *ngIf="showErrorAboutUsernameOrPassword" class="center-block text-style">Nome utente o password errati</span>
        <button class="btn btn-block btn-primary" type="submit">ACCEDI</button>
        <span class="text-danger center-block">{{mess}}</span>
        <!--<a href="http://areaclienti-old.optimaitalia.com/web/optimaitalia/home?p_p_id=OPTIMA_LOGIN_PORTLET_WAR_OptimaAreaClienti&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_OPTIMA_LOGIN_PORTLET_WAR_OptimaAreaClienti_action=forgotPassword" class="btn btn-block btn-primary mt-lg" type="submit">Reset password</a>-->

        <div class="forgetPassword">
          <a class="forgetPassword" href="/forgotPassword{{secondPartOfURL}}" (click)="usernameIsEmail()">Hai dimenticato la password?</a>
        </div>
        <!--<br/>-->
        <div>
          <a class="btn btn-block btn-primary register-button" href="/forgotPassword{{secondPartOfURL}}"
             (click)="usernameIsEmail()">REGISTRATI PER IL PRIMO ACCESSO</a>
        </div>
        <!--<button (click)="socialSignIn('facebook')">Sign in with Facebook</button>-->
        <!--<button (click)="socialSignIn('google')">Signin in with Google</button>-->
        <!--<div class="btn btn-block btn-primary mt-lg" routerLink="/forgotPassword">-->
        <!--Reset password-->
        <!--</div>-->
        <!--<a class="btn btn-block btn-primary mt-lg" href="/forgotPassword">-->
        <!--Reset password-->
        <!--</a>-->
        <!--<a href="https://areaclienti-old.optimaitalia.com/web/optimaitalia/home?p_p_id=OPTIMA_LOGIN_PORTLET_WAR_OptimaAreaClienti&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_OPTIMA_LOGIN_PORTLET_WAR_OptimaAreaClienti_action=forgotPassword" class="btn btn-block btn-primary mt-lg" type="submit">Reset password</a>-->
      </form>
    </div>
  </div>

  <!-- END panel-->
  <div class="p-lg text-center">
    <span>&copy;</span>
    <span>{{ settings.app.year }}</span>
    <span>-</span>
    <span>{{ settings.app.name }}</span>
    <br/>
    <span>{{ settings.app.description }}</span>
  </div>

</div>

<app-dialog-modal-wrapper></app-dialog-modal-wrapper>
<!--<app-month-modal-wrapper></app-month-modal-wrapper>-->
<app-spinner *ngIf="isShowSpinner"></app-spinner>

<div class="modal-div display" *ngIf="openModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text">Abbiamo trovato più utenze associate al tuo indirizzo email, con quale di queste vuoi
      procedere?
    </div>
    <div class="users" *ngFor="let userEntity of userEntity" (click)="selectedUser(userEntity.clientId)">
      <p><b>ID cliente:</b> {{userEntity.clientId}}</p>
      <p><b>Intestatario:</b> {{userEntity.intestatario}}</p>
    </div>
  </div>
</div>

<div class="modal-div display" *ngIf="openModalWindowEmailError">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="modal-image" src="assets/img/icons/Alert.png" alt="Alert">
    <div class="modal-text">Credenziali errate.</div>
  </div>
</div>

<div class="modal-div display" *ngIf="openModalWindowDeletedAccount">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="modal-image" src="assets/img/icons/Alert.png" alt="Alert">
    <div class="modal-text">Account Cancellato: Il tuo account non risulta più attivo in quanto le tue utenze Optima
      risultano disattivate da più di 10 anni.
    </div>
  </div>
</div>
