import {Component, Inject, OnInit} from '@angular/core';
import {SettingsService} from '../../../core/settings/settings.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {UserActions} from '../../../redux/user/actions';
import {UserDetails} from '../../../common/model/UserDetails';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material';
import {AuthService} from '../../../services/auth/auth.service';
import {NotificaMdpService} from '../../../common/services/notifica-mdp/notifica-mdp.service';
import {NotificaMdpActions} from '../../../redux/notifica-mdp/actions';
import {ActivatedRoute, Router} from '@angular/router';
import {DialogModalActions} from '../../../redux/dialogModal/actions';
import {DialogModalEntity} from '../../../common/model/dialogModal/DialogModalEntity';
import {HttpService} from '../../../services/http.service';
import {BriefUserData} from '../../../common/model/user/BriefUserData';


@Component({
  selector: 'app-login-alert',
  templateUrl: 'login-alert.component.html',
})
export class LoginAlertComponent {

  constructor(
    public dialogRef: MatDialogRef<LoginAlertComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LoginAlertData) {
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

}

export interface LoginAlertData {
  data: string;
}


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  isShowSpinner = false;
  valForm: FormGroup;
  router: Router;
  public mess;
  private url = 'api/getCredentials';

  userEntity: Array<BriefUserData>;
  openModalWindow: boolean;
  showErrorAboutUsernameOrPassword: boolean;
  openModalWindowEmailError: boolean;
  openModalWindowDeletedAccount: boolean;
  isLoginByEmail: boolean;
  secondPartOfURL: string;
  emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  constructor(
    /*private socialAuthService: AuthServiceSocial,*/
    public dialog: MatDialog,
    public settings: SettingsService,
    fb: FormBuilder,
    private _router: Router,
    private httpService: HttpService,
    private userActions: UserActions,
    private authService: AuthService,
    private notificaMdpService: NotificaMdpService,
    private notificaMdpActions: NotificaMdpActions,
    /*private router: Router,*/
    private route: ActivatedRoute,
    private dialogModalActions: DialogModalActions,
  ) {
    this.router = _router;
    this.valForm = fb.group({
      'username': [null, Validators.required],
      'password': [null, Validators.required]
    });

  }

  usernameIsEmail() {
    if (this.emailRegex.test(String(this.valForm.controls.username.value).toLowerCase())) {
      this.secondPartOfURL = `/${this.valForm.controls.username.value}`;
    } else {
      this.secondPartOfURL = '';
    }
  }

  login(fields) {
    this.authService.checkIfUserAlreadyRestoredPassword(fields).subscribe(
      response => {
        if (response) {
          this.isLoginByEmail = false;
          this.isShowSpinner = true;
          this.authService.login(fields, this.getQueryStringParams(this.router.url)).subscribe(
            (data: any) => {
              this.userActions.setUerDetails(data as UserDetails);
              this.isShowSpinner = false;
              this.notificaMdpService.getNotificationMdpIfClientExist(localStorage.getItem('clientId')).subscribe(
                information => {
                  if (information) {
                    this.notificaMdpActions.showHide(information);
                  }
                });
            },
            (error: any) => {
              this.isShowSpinner = false;
              if (error.deletedAccount) {
                this.openModalWindowDeletedAccount = true;
              } else {
                this.isLoginByEmail ? this.openModalWindowEmailError = true : this.showErrorAboutUsernameOrPassword = true;
              }
            }
          );
        } else {
          this.isShowSpinner = false;
          this.dialogModalActions.showDialogModal({
            text: 'La tua password è scaduta, ti invitiamo a crearne una nuova. ' +
                   'Ti ricordiamo inoltre di aggiornarla regolarmente seguendo le migliori pratiche per la protezione dei tuoi dati. ' ,
            hasButtons: true,
            callbackToExecuteOnSubmit: () => {
              window.location.href = '/forgotPassword';
            }
          } as DialogModalEntity);
        }
      },
      (error: any) => {
        this.isLoginByEmail ? this.openModalWindowEmailError = true : this.showErrorAboutUsernameOrPassword = true;
        this.isShowSpinner = false;
      });
  }

  openDialog(): void {
    const dialogRef = this.dialog.open(LoginAlertComponent, {
      width: '250px',
      data: 'Test text'
    });
    dialogRef.afterClosed().subscribe();
  }

  submitForm($ev, value: any) {
    $ev.preventDefault();

    Object.keys(this.valForm.controls).forEach((c) => {
      this.valForm.controls[c].markAsTouched();
    });

    if (this.valForm.valid) {
      this.showErrorAboutUsernameOrPassword = false;
      this.isShowSpinner = true;
      if (this.emailRegex.test(String(value.username).toLowerCase())) {
        this.authService.getAllUsersWithTheSameEmail(value).subscribe(response => {
          if (response === null) {
            this.showErrorAboutUsernameOrPassword = true;
            this.isShowSpinner = false;
          } else if (response.length > 1) {
            this.userEntity = response;
            this.isShowSpinner = false;
            this.openModalWindow = true;
          } else if (response.length === 0) {
            this.openModalWindowEmailError = true;
            this.isShowSpinner = false;
          } else {
            this.isLoginByEmail = true;
            this.prepareToLogin(value);
          }
        });
      } else {
        this.prepareToLogin(value);
      }
    }
  }

  prepareToLogin(value: any) {
    this.settings.app.username = value.username;
    localStorage.clear();
    localStorage.setItem('clientUserName', value.username);
    this.login(value);
  }

  selectedUser(username: string) {
    const value = {
      'username': username,
      'password': this.valForm.controls.password.value
    };
    this.hideDialogModal();
    this.prepareToLogin(value);
  }

  hideDialogModal() {
    this.openModalWindow = false;
    this.openModalWindowEmailError = false;
    this.openModalWindowDeletedAccount = false;
    this.isLoginByEmail = false;
  }

  getQueryStringParams(url: string) {
    return url.includes('?') ? url.slice(url.indexOf('?')) : '';
  }

  ngOnInit() {

  }

}

/*@Component({
  selector: 'app-login-alert',
  templateUrl: 'login-alert.component.html',
})
export class LoginAlertComponent {

  constructor(
    public dialogRef: MatDialogRef<LoginAlertComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LoginAlertData) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

}

export interface LoginAlertData {
  data: string;
}*/
