<nav>
  <img src="/assets/img/logo/optima_new_main_logo.svg" class="logo" routerLink="/prospect/login" alt="Logo"/>
</nav>

<div class="wrapper-block">
  <div class="register-form" [formGroup]="formGroup">
    <div class="header-form-text">Registrati per seguire in tempo reale lo stato di lavorazione delle tue utenze
      Optima.
    </div>

    <label>Codice Fiscale o Partita IVA associati al tuo contratto</label>
    <input type="text" formControlName="CF"/>
    <span class="text-danger" *ngIf="formGroup.get('CF').hasError('required')
      && (formGroup.get('CF').dirty || formGroup.get('CF').touched)">Campo obbligatorio</span>
    <span class="text-danger show"
          *ngIf="showErrorCfAlreadyRegistered">Codice Fiscale o Partita IVA già registrati</span>

    <label>E-mail associata al tuo contratto o ID contratto</label>
    <input type="text" formControlName="emailOrContractId"/>
    <span class="text-danger" *ngIf="formGroup.get('emailOrContractId').hasError('required')
      && (formGroup.get('emailOrContractId').dirty || formGroup.get('emailOrContractId').touched)">Campo obbligatorio</span>
    <span class="text-danger" *ngIf="showErrorNotFoundEmailOrContract">L'e-mail associata al contratto o all'ID del contratto non è stata trovata.</span>

    <label>Inserisci una Password</label>
    <input type="password" formControlName="password1"/>
    <span class="text-danger" *ngIf="formGroup.get('password1').hasError('required')
      && (formGroup.get('password1').dirty || formGroup.get('password1').touched)">Campo obbligatorio</span>

    <label>Conferma la Password</label>
    <input type="password" formControlName="password2"/>
    <span class="text-danger" *ngIf="formGroup.get('password2').hasError('required')
      && (formGroup.get('password2').dirty || formGroup.get('password2').touched)">Campo obbligatorio </span>
    <span class="text-danger show"
          *ngIf="formGroup.hasError('mismatch')
      && (formGroup.controls['password2'].dirty || formGroup.controls['password2'].touched)">Le password non coincidono </span>
    <button class="btn-salva" (click)="checkForm()">SALVA</button>
  </div>
</div>

<div class="modal-div show" *ngIf="showSussesModalWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="openLoginPage()"></i>
    <img class="modal-image" src="/assets/img/icons/ok.png" alt="Ok"/>
    <div class="modal-text" *ngIf="!showAlreadyRegisterModalWindow">Registrazione effettuata!</div>
    <div class="modal-text" *ngIf="showAlreadyRegisterModalWindow">Siete già registrati nel sistema!</div>
    <button class="btn-modal" (click)="openLoginPage()">ACCEDI</button>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
