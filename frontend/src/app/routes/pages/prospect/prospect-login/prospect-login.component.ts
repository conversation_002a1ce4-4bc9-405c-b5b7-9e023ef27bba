import {Component, OnInit} from '@angular/core';
import {ProspectUserService} from '../../../../common/services/prospect-user/prospect-user.service';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ShortInformationProspectUserContract} from '../../../../common/model/prospect-user/prospectUser';
import {Router} from '@angular/router';

@Component({
  selector: 'app-prospect-login',
  templateUrl: './prospect-login.component.html',
  styleUrls: ['./prospect-login.component.scss']
})
export class ProspectLoginComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  formGroupLogin: FormGroup;
  formGroupContract: FormGroup;
  showChooseContractModalWindow: boolean;
  showLoginError: boolean;
  contractsDetails: ShortInformationProspectUserContract[] = [];
  showNoSuchContract: boolean;

  constructor(private formBuilder: FormBuilder, private prospectService: ProspectUserService, private router: Router) {
    this.formGroupLogin = this.formBuilder.group({
      username: [null, Validators.required],
      password: [null, Validators.required]
    });
    this.formGroupContract = this.formBuilder.group({
      option: [null, Validators.required]
    });
  }

  ngOnInit() {
  }

  hideModalWindow() {
    this.showChooseContractModalWindow = false;
  }

  login($ev) {
    $ev.preventDefault();
    this.showLoginError = false;
    this.showNoSuchContract = false;
    if (!this.formGroupLogin.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupLogin);
    } else {
      this.prospectService.sendLoginForm(this.formGroupLogin.value).subscribe(response => {
        if (response.status === 200) {
          this.prospectService.getContractsByCodiceFiscale(this.formGroupLogin.value.username).subscribe(contractInformation => {
              for (let i = 0; i < contractInformation.response.length; i++) {
                if (this.contractsDetails.length === 0) {
                  this.contractsDetails.push(new ShortInformationProspectUserContract(contractInformation.response[i]));
                } else {
                  let contractExists = false;
                  for (let j = 0; j < this.contractsDetails.length; j++) {
                    if (this.contractsDetails[j].idGruppo === contractInformation.response[i].idGruppo) {
                      contractExists = true;
                      this.contractsDetails[j].luce = this.contractsDetails[j].luce || contractInformation.response[i].icona === 'EE';
                      this.contractsDetails[j].gas = this.contractsDetails[j].gas || contractInformation.response[i].icona === 'GAS';
                      this.contractsDetails[j].internet = this.contractsDetails[j].internet || contractInformation.response[i].icona === 'INTERNET';
                      this.contractsDetails[j].fisso = this.contractsDetails[j].fisso || contractInformation.response[i].icona === 'VOCE';
                      this.contractsDetails[j].mobile = this.contractsDetails[j].mobile || contractInformation.response[i].icona === 'MOBILE';
                      this.contractsDetails[j].teleconsulto = this.contractsDetails[j].teleconsulto || contractInformation.response[i].icona === 'YH';
                      this.contractsDetails[j].assistenza = this.contractsDetails[j].assistenza || contractInformation.response[i].icona === 'H24';
                      this.contractsDetails[j].consulenzaLegale = this.contractsDetails[j].consulenzaLegale || contractInformation.response[i].icona === 'TUTELALEGALE';
                    }
                  }
                  if (!contractExists) {
                    this.contractsDetails.push(new ShortInformationProspectUserContract(contractInformation.response[i]));
                  }
                }
              }
            if (this.contractsDetails.length === 0) {
              this.showNoSuchContract = true;
            } else if (this.contractsDetails.length === 1) {
              this.moveToStatusPage(this.contractsDetails[0].idGruppo.toString());
            } else {
              this.showChooseContractModalWindow = true;
            }
          });
        }
      }, (error) => {
        if (error.status === 403) {
          this.showLoginError = true;
        }
      });
    }
  }

  moveToStatusPage(idGruppo: string) {
    this.prospectService.setGroupIdAndCF(idGruppo, this.formGroupLogin.value.username);
    this.router.navigateByUrl('/prospect/registration-status').catch(() => {
    });
  }

  chooseContract() {
    if (!this.formGroupContract.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupContract);
    } else {
      this.moveToStatusPage(this.formGroupContract.value.option);
    }
  }
}
