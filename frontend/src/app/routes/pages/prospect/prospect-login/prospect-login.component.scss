.wrapper-block {
  background: transparent linear-gradient(91deg, #00B8FF 0%, #0035AD 100%);
  padding-top: 1px;
  padding-bottom: 1px;
}

.login-form {
  width: 830px;
  background-color: white;
  margin: 100px auto;
  border-radius: 30px;
  padding: 60px 90px;
}

.img-logo {
  width: 304px;
  height: 95px;
  margin: 0 auto;
  display: block;
}

.text-header {
  font-size: 31px;
  color: #00A9EA;
  text-align: center;
  font-weight: bold;
  margin-top: 25px;
  margin-bottom: 45px;
}

label {
  display: block;
  letter-spacing: 0;
  color: #6E6E6E;
  font-size: 15px;
  font-weight: normal;
  margin-top: 15px;
}

input[type="text"], [type="password"] {
  width: 100%;
  box-shadow: inset 0 3px 6px #00000029;
  border: 0.5px solid #B2C8DD;
  border-radius: 5px;
  height: 55px;
}

.error-text {
  text-align: center;
  margin-top: 13px;
  margin-bottom: -13px;
}

.btn-submit {
  padding: 11px 49px;
  background: #97B825;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  color: white;
  display: block;
  font-size: 19px;
  margin: 70px auto 0;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  backdrop-filter: blur(50px);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  margin: auto;
  top: 15vh;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 617px;
  min-height: 440px;
  background: #FFFFFF;
  border-radius: 30px;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #00a9ea;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #6E6E6E;
  width: 80%;
  margin: auto;
  padding-top: 50px;
  padding-bottom: 50px;
  font-size: 30px;
  font-weight: bold;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: 3% auto auto;
  width: 120px;
  height: 120px;
  display: block;
}

.btn-modal {
  padding: 11px 49px;
  background: #97B825;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  color: white;
  margin: 0 auto;
  display: block;
  font-size: 19px;
}

.modal-block {
  background: #F7F7F7;
  border: 1px solid #B0C7DD;
  border-radius: 10px;
  width: 75%;
  padding: 15px;
  font-size: 15px;
  display: flex;
  align-items: center;
  margin: 0 auto 20px;
}

.text-inline {
  display: flex;
  justify-content: space-around;
}

.img-icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  margin-top: 5px;
}

input[type="radio"] {
  width: 20px;
  height: 20px;
  border: 1px solid #B0C7DD;
  margin-right: 25px
}

@media only screen and (max-width: 1000px) {
  .login-form {
    width: 90%;
  }
}

@media only screen and (max-width: 651px) {
  .inner-modal-div {
    top: 0;
    width: auto;
    border-radius: 0;
  }
}

@media only screen and (max-width: 560px) {
  .login-form {
    padding: 50px 50px;
  }
  .text-header {
    font-size: 27px;
  }
  .img-logo {
    width: 215px;
  }
}
