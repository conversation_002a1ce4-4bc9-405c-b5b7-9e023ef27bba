@import "~app/shared/styles/colors";

nav {
  height: 100px;
  background-color: white;
  display: flex;
  align-items: center;
}

.back-button {
  position: absolute;
  left: 20px;
  cursor: pointer;
}

.back-button img {
  width: 83px;
}

.logo-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo {
  height: 65px;
  cursor: pointer;
}

.wrapper-block {
  background: transparent linear-gradient(180deg, #00B8FF 0%, #0035AD 100%);
  padding-top: 1px;
  padding-bottom: 1px;
}

.information-block {
  width: 1382px;
  height: 1550px;
  background: #e7f7fe;
  opacity: 0.91;
  margin: 70px auto 70px;
  padding: 70px;
}

h1 {
  font-size: 31px;
  color: #6E6E6E;
  font-weight: bold;
}

.text {
  color: #00A9EA;
  font-size: 22px;
  font-weight: bold;
  font-style: italic;
}

.span-energy-text {
  text-align: center;
  font-size: 18px;
  font-style: italic;

  .header {
    font-size: 22px;
    color: #00A9EA;
    font-weight: bold;
  }
}

hr {
  height: 1px;
  border-width: 0;
  background-color: #B0C7DD;
  width: 978px;
  margin: 50px auto;
}

.font-size-text {
  font-size: 22px;
}

.span-text-below {
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 22px;
  justify-content: center
}

.img-layout {
  width: 1092px;
  display: block;
  animation: fadeIn 2s;
  margin: 20px 20px auto auto;
}

.service-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  margin: 50px 0;

  .service-image {
    cursor: pointer;
    width: 130px;
    height: 130px;
  }
}

.revert-button {
  margin: 40px auto;
  background: #00A9EA;
  border-radius: 100px;
  color: white;
  padding: 15px 50px;
  border: 0;
  display: block;
}

.margin {
  margin: 20px 0;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  backdrop-filter: blur(5px);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  width: 722px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 5px;
  padding-bottom: 50px;
  padding-top: 50px;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #20a9ea;
  cursor: pointer;
}

// Successful modal window
.modal-text {
  text-align: center;
  color: #6E6E6E;
  width: 80%;
  margin: auto;
  padding-top: 30px;
  font-size: 23px;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: auto;
  width: 147px;
  height: 147px;
  display: block;
}

.btn-modal {
  background: #00A9EA;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  font-size: 19px;
  font-weight: bold;
  color: white;
  display: block;
  padding: 15px 50px;
  margin: 45px auto auto;
}

// upload file modal window
.padding {
  padding: 70px;
}

.alert-picture {
  width: 16px;
  height: 58px;
  margin-right: 16px;
}

.header-upload-modal {
  color: #6E6E6E;
  font-size: 22px;
  //text-align: center;
  //margin: auto;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.upload-modal-text {
  font-size: 15px;
  color: #6E6E6E;
  text-align: left;
  margin: 25px auto;
  //padding: 0 60px;
}

.upload-file-block {
  //padding: 0 60px;
}

.upload-label {
  font-size: 15px;
  color: #36749A;
  margin-bottom: 8px;
}

.upload-input-block {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;

  .upload-file-name {
    width: 195px;
    padding: 8px;
    border-radius: 5px;
    border: 1px solid #B2C8DD;
    overflow: hidden;
    margin-right: 10px;
    color: $dark-blue;
    font-weight: bold;
  }

  .remove-file {
    width: 40px;
    cursor: pointer;
    margin-left: 5px;
  }

  .remove-file-button {
    border: none;
    font-size: 14px;
    background: none;
  }
}

.upload-file-button {
  display: flex;
  justify-content: center;

  .upload-button {
    width: 140px;
    cursor: pointer;
  }
}

#files-upload {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

label {
  margin: 0;
}

.hide {
  display: none !important;
}

.pointer {
  cursor: pointer;
}

.input-block {
  display: flex;
  color: #36749A;
  align-items: center;
  justify-content: center;
  margin-top: 20px;

  .label-input {
    font-size: 15px;
    font-weight: bold;
    margin-right: 10px;
  }

  .input-number-block {
    border: 1px solid #B2C8DD;
    border-radius: 5px;
    height: 35px;
    padding-left: 10px;
    min-width: 139px;
  }

  /* Chrome, Safari, Edge, Opera */
  .input-number-block::-webkit-inner-spin-button,
  .input-number-block::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  .input-number-block {
    -moz-appearance: textfield;
  }

  select {
    -webkit-appearance: none; /*Removes default chrome and safari style*/
    -moz-appearance: none;
    background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
    background-position-x: 95%;
  }

  .contact-time-info {
    position: relative;
    float: left;
    margin-left: 2%;
    display: inline-block;

    .info-circle {
      border-radius: 50%;
      border: 1px solid;
      height: 17px;
      width: 17px;
      line-height: 15px;
      font-weight: 700;
      font-size: 13px;
    }
  }
}

.button-calling-modal-window {
  background: #00A9EA;
  border-radius: 10px;
  padding: 10px;
  color: white;
  border: 0;
  font-size: 12px;
  display: block;
  margin: 30px auto 0;
}

.modal-container {
  display: flex;
  flex-direction: column;

  .modal-image {
    width: 144px;
    height: 144px;
  }

  .modal-container-text {
    color: #6E6E6E;
    font-weight: bold;
    text-align: center;
    font-size: 20px;
  }
}

@media only screen and (max-width: 1385px) {
  .information-block {
    width: 95%
  }
  .img-layout {
    width: 100%;
    margin: 20px 20px auto 20px;
  }
  hr {
    width: auto;
  }
}

@media only screen and (max-width: 800px) {
  nav {
    justify-content: center;
  }
  .logo {
    margin: 0;
  }
  .information-block {
    padding: 35px;
    margin: 50px auto 50px;
  }
}

@media only screen and (max-width: 765px) {
  .inner-modal-div {
    width: 95%;
  }
  h1 {
    font-size: 28px;
  }
  .text {
    font-size: 18px;
  }
}

@media only screen and (max-width: 651px) {
  .img-layout {
    width: 321px;
    margin: 0 auto;
  }
  .information-block {
    height: auto;
  }
  .text {
    margin: 40px 0;
  }
  .inner-modal-div {
    width: 100%;
    height: 100%;
    align-content: center;
    border-radius: 0;
  }
  .padding {
    padding: 50px;
  }
  .header-upload-modal {
    font-size: 25px;
  }
  .upload-modal-text {
    font-size: 18px;
    margin: 50px 0;
  }
  .span-text-below {
    font-size: 18px;
    text-align: left;
    align-items: flex-start
  }
  .font-size-text {
    font-size: 18px;
  }
  hr {
    width: auto;
  }
}

@media only screen and (max-width: 400px) {
  .back-button img {
    width: 70px;
  }
}
