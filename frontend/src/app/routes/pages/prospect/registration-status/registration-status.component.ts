import {Component, ElementRef, OnInit, Renderer2, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {ProspectUserService} from '../../../../common/services/prospect-user/prospect-user.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {contactTimeConfirmWindowExpression, createContactTimeOptions} from '../../../fai-da-te/config/config';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {CategoryCCN, EmailToSupportWithFiles, ProspectUserContract} from '../../../../common/model/prospect-user/prospectUser';
import Validator from '../../../../common/utils/Validator';
import {FormUtils} from '../../../../common/utils/FormUtils';

@Component({
  selector: 'app-registration-status',
  templateUrl: './registration-status.component.html',
  styleUrls: ['./registration-status.component.scss'],
})
export class RegistrationStatusComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  @ViewChild('modalWindowWithClientId') modalWithClientId;
  @ViewChild('modalWindowWithCCN') modalWithCCN;
  @ViewChild('modalWindowQCCKO') modalQCCKO;
  @ViewChild('modalWindowFlagContrattoCestinato') modalFlagContrattoCestinato;
  @ViewChild('sendDocumentsModal') sendDocumentsModal;
  // @ViewChild('documentsSuccessfullySentModal') documentsSuccessfullySentModal;
  selectedContract: ProspectUserContract;
  contracts: ProspectUserContract[];

  emailFilesBeforeValidation: Array<File> = [];
  emailFilesAfterValidation: Array<File> = [];
  emailIndexesOfFilesWithWrongSize = [];
  emailIndexesOfFilesWithWrongExtension = [];

  formGroupSupportCall: FormGroup;
  contactTimeOptions = createContactTimeOptions();
  categoryCCN = CategoryCCN;
  services = ['luce', 'gas', 'internet', 'mobile', 'teleconsulto medico', 'assistenza H24', 'consulenza legale'];

  showLayoutWithCCN: boolean;
  showLayoutWithAzioneQCC: boolean;
  showLayoutWithClientId: boolean;
  showLayoutWithFlagContrattoCestinato: boolean;
  showLayoutWithAttivazioneParziale: boolean;
  showLayoutWithDates: boolean;
  allContractsHaveQCCKO: boolean;
  showMenu = true;
  isMobile = window.innerWidth <= 650;
  selectServiceName: string;

  constructor(private router: Router, private prospectService: ProspectUserService, private renderer: Renderer2,
              private formBuilder: FormBuilder) {
    if (this.prospectService.getGroupId() === null || this.prospectService.getCF() === null) {
      this.moveToProspectLoginPage();
    } else {
      this.prospectService.getContractsByCodiceFiscale(this.prospectService.getCF()).subscribe(information => {
        this.contracts = information.response.filter(contract =>
          (contract.idGruppo === parseInt(this.prospectService.getGroupId(), 10)));
        this.updateServiceImages();
        if (this.contracts.length > 0) {
          if (!this.isMobile) {
            this.selectContract(this.findFirstContractBasedOnServiceOrder());
          }
          this.allContractsHaveQCCKO = this.contracts.every(contract => contract.azioneQCC === 'QCCKO');
        }
      });
    }
    this.formGroupSupportCall = this.formBuilder.group({
      phoneNumber: [null, [Validators.required, Validator.withLength(8, 12), Validator.digits()]],
      contactTime: [null, [Validators.required, Validator.withExpression(contactTimeConfirmWindowExpression())]],
    });
  }

  ngOnInit() {
  }

  findFirstContractBasedOnServiceOrder(): ProspectUserContract | undefined {
    for (const service of this.services) {
      const icona = this.getIconaFromService(service);
      const contract = this.contracts.find(c => c.icona === icona);
      if (contract) {
        return contract;
      }
    }
    return undefined;
  }

  getIconaFromService(service: string): string | null {
    switch (service) {
      case 'luce':
        return 'EE';
      case 'gas':
        return 'GAS';
      case 'internet':
        return 'INTERNET' || 'VOCE';
      case 'mobile':
        return 'MOBILE';
      case 'teleconsulto medico':
        return 'YH';
      case 'assistenza H24':
        return 'H24';
      case 'consulenza legale':
        return 'TUTELALEGALE';
      default:
        return null;
    }
  }

  getImageSrc(service: string, isHovered: boolean = false): string {
    const contract = this.getContractForService(service);
    const baseImageName = `${service}.png`;
    const koImageName = `${service}_ko.png`;
    const koPassaggioImageName = `${service}_ko_passaggio.png`;
    const onImageName = `${service}_on.png`;
    const passaggioImageName = `${service}_passaggio.png`;
    const offImageName = `${service}_off.png`;
    if (!contract) {
      return `assets/img/big-icons-services/${service}/${offImageName}`;
    }
    const errorCondition = contract.categorieCCN || contract.flagContrattoCestinato || contract.azioneQCC === 'QCCKO';
    if (errorCondition && isHovered) {
      return `assets/img/big-icons-services/${service}/${koPassaggioImageName}`;
    } else if (isHovered) {
      return `assets/img/big-icons-services/${service}/${passaggioImageName}`;
    } else if (errorCondition) {
      return `assets/img/big-icons-services/${service}/${koImageName}`;
    } else if (this.selectedContract && this.selectedContract.icona === contract.icona) {
      return `assets/img/big-icons-services/${service}/${onImageName}`;
    } else {
      return `assets/img/big-icons-services/${service}/${baseImageName}`;
    }
  }

  getContractForService(service: string): ProspectUserContract | undefined {
    if (this.contracts) {
      const icona = this.getIconaFromService(service);
      return this.contracts.find(contract => contract.icona === icona);
    }
  }

  selectContract(contract: ProspectUserContract | undefined) {
    if (contract) {
      this.selectedContract = contract;
      this.selectServiceName = this.getServiceFromIcona(contract.icona);
      this.updateLayoutInfo();
      this.openModalWindow();
      this.updateServiceImages();
      if (this.isMobile) {
        this.showMenu = false;
      }
    }
  }

  getImageLayoutSrc(imageName: string): string {
    const basePath = 'assets/img/register-status-layout/';
    if (this.isMobile) {
      return basePath + imageName.replace('.png', '_responsive.png');
    }
    return basePath + imageName;
  }

  updateLayoutInfo() {
    this.resetInfo();
    if (this.selectedContract.categorieCCN) {
      this.showLayoutWithCCN = true;
    } else if (this.selectedContract.azioneQCC && this.selectedContract.azioneQCC !== '') {
      this.showLayoutWithAzioneQCC = true;
    } else if (this.selectedContract.attivazioneParziale && this.selectedContract.flagContrattoCestinato === true) {
      this.showLayoutWithAttivazioneParziale = true;
      this.showLayoutWithFlagContrattoCestinato = true;
    } else if (this.selectedContract.attivazioneParziale) {
      this.showLayoutWithAttivazioneParziale = true;
    } else if (this.selectedContract.flagContrattoCestinato === true) {
      this.showLayoutWithFlagContrattoCestinato = true;
    } else if (this.selectedContract.dataInvioAttivazione) {
      this.showLayoutWithClientId = true;
    } else {
      this.showLayoutWithDates = true;
    }
  }

  resetInfo() {
    this.showLayoutWithCCN = false;
    this.showLayoutWithAzioneQCC = false;
    this.showLayoutWithDates = false;
    this.showLayoutWithClientId = false;
    this.showLayoutWithFlagContrattoCestinato = false;
    this.showLayoutWithAttivazioneParziale = false;
  }

  updateServiceImages() {
    this.contracts.forEach(contract => {
      const service = this.getServiceFromIcona(contract.icona);
      if (service) {
        const imageSrc = this.getImageSrc(service);
        this.setImageSrc(service, imageSrc);
      }
    });
  }

  onMouseOver(service: string) {
    const imageElement = document.querySelector(`img[alt='${service}']`);
    if (imageElement) {
      const currentSrc = imageElement.getAttribute('src');
      const onImageName = `assets/img/big-icons-services/${service}/${service}_on.png`;
      if (currentSrc !== onImageName) {
        this.renderer.setAttribute(imageElement, 'src', this.getImageSrc(service, true));
      }
    }
  }

  onMouseOut(service: string) {
    const imageElement = document.querySelector(`img[alt='${service}']`);
    if (imageElement) {
      this.renderer.setAttribute(imageElement, 'src', this.getImageSrc(service, false));
    }
  }

  getServiceFromIcona(icona: string): string | null {
    switch (icona) {
      case 'EE':
        return 'luce';
      case 'GAS':
        return 'gas';
      case 'VOCE':
      case 'INTERNET':
        return 'internet';
      case 'MOBILE':
        return 'mobile';
      case 'YH':
        return 'teleconsulto medico';
      case 'H24':
        return 'assistenza H24';
      case 'TUTELALEGALE':
        return 'consulenza legale';
      default:
        return null;
    }
  }

  setImageSrc(service: string, imageSrc: string) {
    const imageElement = document.querySelector(`.service-image[alt="${service}"]`);
    if (imageElement) {
      this.renderer.setProperty(imageElement, 'src', imageSrc);
    }
  }

  onFilesChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length
      && this.emailFilesAfterValidation.length < 5) {
      currentFile = event.target.files[0];
      for (let i = 0; i < this.emailFilesAfterValidation.length; i++) {
        if (this.emailFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.emailFilesBeforeValidation.push(currentFile);
      this.emailFilesAfterValidation.push(currentFile);
      if (currentFile.size > 5242880) {
        this.emailIndexesOfFilesWithWrongSize.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateFile(currentFile.name)) {
        this.emailIndexesOfFilesWithWrongExtension.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      }
      const reader = new FileReader();
      if (this.emailFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
    }
    event.target.value = '';
  }

  validateFile(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf';
  }

  removeFileFromForm(beforeValIndex: number, afterValIndex: number) {
    this.emailFilesBeforeValidation.splice(beforeValIndex, 1);
    this.emailFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongSize.length; i++) {
      if (this.emailIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.emailIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongExtension[i]--;
      }
    }
  }

  closeWarningInEmailForm(index: number) {
    this.emailFilesBeforeValidation.splice(index, 1);
    this.emailIndexesOfFilesWithWrongSize = this.emailIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.emailIndexesOfFilesWithWrongExtension = this.emailIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  sendFile() {
    const requestBody = new FormData();
    for (const file of this.emailFilesAfterValidation) {
      requestBody.append('file', file);
    }
    requestBody.append('contractId', this.selectedContract.idContratto.toString());
    this.prospectService.sendFiles(requestBody).subscribe(() => {
      this.renderer.addClass(this.sendDocumentsModal.nativeElement, 'hide');
      // this.renderer.removeClass(this.documentsSuccessfullySentModal.nativeElement, 'hide');
      this.selectedContract.isSentDocuments = true;
    });
  }

  openModalWindow() {
    if (this.selectedContract.categorieCCN && this.modalWithCCN) {
      this.renderer.removeClass(this.modalWithCCN.nativeElement, 'hide');
    } else if (this.selectedContract.attivazioneParziale === 1 && this.modalQCCKO) {
      this.renderer.removeClass(this.modalQCCKO.nativeElement, 'hide');
    } else if (this.selectedContract.idCliente && this.modalWithClientId) {
      this.renderer.removeClass(this.modalWithClientId.nativeElement, 'hide');
    } else if (this.selectedContract.flagContrattoCestinato && this.modalFlagContrattoCestinato) {
      this.renderer.removeClass(this.modalFlagContrattoCestinato.nativeElement, 'hide');
    }
  }

  sendEmailToSupportCall() {
    if (this.formGroupSupportCall.valid) {
      const fullName = `${this.selectedContract.nome} ${this.selectedContract.cognome}`;
      const hour = new Date(parseInt(this.formGroupSupportCall.value.contactTime, 10)).getHours();
      const objectText = `Gestione CCN ${fullName}`;
      const bodyText = `Il cliente ${fullName} per gestione CCN su contratto ${this.selectedContract.idContratto}` +
        ` richiede di essere contattato al numero ${this.formGroupSupportCall.value.phoneNumber} con la seguente disponibilita':` +
        ` fascia oraria ${hour}-${hour + 2}. Note aggiuntive: nessuna`;
      const emailBody = new EmailToSupportWithFiles(this.selectedContract.email, objectText, bodyText, this.selectedContract.invioEmail);
      this.prospectService.sendEmailToSupport(emailBody).subscribe(response => {
        if (response !== -1) {
          this.selectedContract.isBookedCall = true;
        }
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.formGroupSupportCall);
    }
  }

  backButton() {
    this.showMenu = true;
    this.selectedContract = null;
  }

  hideModalWindow(elementRef: ElementRef) {
    this.renderer.addClass(elementRef.nativeElement, 'hide');
  }

  moveToGeneralLoginPage() {
    this.router.navigateByUrl('/login').catch(() => {
    });
  }

  moveToProspectLoginPage() {
    this.router.navigateByUrl('/prospect/login').catch(() => {
    });
  }
}
