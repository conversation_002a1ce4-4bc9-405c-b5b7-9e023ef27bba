
<div class="wrapperBg">
</div>
<div class="wrapper">
  <div class="block-center mt-xl wd-xl">
    <!-- START panel-->
    <div class="panel panel-red panel-flat">
      <div class="panel-heading text-center">
        <div class="block-center img-rounded logo"></div>
      </div>
      <div class="panel-body">
        <p class="text-center pv">INSERISCI LE TUE CREDENZIALI</p>
        <form [formGroup]="valForm" class="form-validate mb-lg" role="form" name="loginForm" novalidate="" (submit)="submitForm($event, valForm.value)">

          <div class="form-group has-feedback login-form-input">
            <input class="form-control" id="inputUserName" type="text" name="username" placeholder="Enter login" autocomplete="off" formControlName="username" required="" />
            <span class="fa fa-user-o form-control-feedback text-muted"></span>
            <span class="text-danger" *ngIf="valForm.controls['username'].hasError('required') && (valForm.controls['username'].dirty || valForm.controls['username'].touched)">Campo obbligatorio</span>
          </div>
          <div class="form-group has-feedback login-form-input">
            <input class="form-control" id="exampleInputPassword1" type="password" name="password" placeholder="Password" formControlName="password" required=""/>
            <span class="fa fa-lock form-control-feedback text-muted"></span>
            <span class="text-danger" *ngIf="valForm.controls['password'].hasError('required') && (valForm.controls['password'].dirty || valForm.controls['password'].touched)">Campo obbligatorio</span>
          </div>

          <span class="text-danger center-block">{{mess}}</span>
          <button class="btn btn-block btn-primary mt-lg" type="submit">Login</button>
          <span class="text-danger center-block">{{mess}}</span>
          <!--<div class="btn btn-block btn-primary mt-lg" routerLink="/forgotPassword">-->
          <!--Reset password-->
          <!--</div>-->
          <!--<a class="btn btn-block btn-primary mt-lg" href="/forgotPassword">-->
          <!--Reset password-->
          <!--</a>-->
        </form>
      </div>
    </div>
    <!-- END panel-->
    <div class="p-lg text-center">
      <span>&copy;</span>
      <span>{{ settings.app.year }}</span>
      <span>-</span>
      <br/>
      <span>Assistenza Optima</span>
      <!--<p class ="asAdmin" routerLink="/admin"> login as admin</p>-->
    </div>
  </div>
</div>


<app-spinner *ngIf="isShowSpinner"></app-spinner>
