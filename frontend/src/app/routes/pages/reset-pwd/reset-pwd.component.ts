import {Component, OnInit, Renderer2, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ForgotPasswordService} from '../../../common/services/forgotPassword/forgot-password.service';
import {ForgotPasswordRequest} from '../../../common/model/forgotPassword/forgotPasswordRequest';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ActivatedRoute} from '@angular/router';

declare let grecaptcha: any;

@Component({
  selector: 'app-reset-pwd',
  templateUrl: './reset-pwd.component.html',
  styleUrls: ['./reset-pwd.component.scss']
})
export class ResetPwdComponent implements OnInit {
  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;
  @ViewChild('form') form;
  @ViewChild('success') success;
  @ViewChild('error') error;
  @ViewChild('text') text;
  valForm: FormGroup;
  captchaError = false;
  noSuchEmail = false;
  openModalWindowDeletedAccount = false;

  constructor(fb: FormBuilder, private forgotpwd: ForgotPasswordService, private renderer: Renderer2, private route: ActivatedRoute) {
    let defaultValue;
    if (this.route.children[0]) {
      this.route.children[0].params.subscribe(params => {
        defaultValue = params.email ? params.email : '';
      });
    }
    this.valForm = fb.group({
      'email': [defaultValue, Validators.required],
    });
  }

  hideError() {
    this.captchaError = false;
  }

  submitForm($ev, value: any) {
    $ev.preventDefault();

    Object.keys(this.valForm.controls).forEach((c) => {
      this.valForm.controls[c].markAsTouched();
    });

    const response = grecaptcha.getResponse();
    if (response.length === 0) {
      this.captchaError = true;
      return;
    } else {
      this.captchaError = false;
    }
    if (this.valForm.valid && this.captchaError === false) {
      const request = new ForgotPasswordRequest();
      request.email = this.valForm.controls.email.value;
      this.forgotpwd.restorePassword(request).subscribe(data => {
        if (data.status === 200) {
          this.renderer.addClass(this.form.nativeElement, 'hide');
          this.renderer.addClass(this.text.nativeElement, 'hide');
          this.renderer.removeClass(this.success.nativeElement, 'hide');
        } else if (data.status === 403 && data.message === 'deleted_account') {
          this.openModalWindowDeletedAccount = true;
        } else if (data.status === 404) {
          this.noSuchEmail = true;
        } else {
          this.renderer.addClass(this.form.nativeElement, 'hide');
          this.renderer.removeClass(this.error.nativeElement, 'hide');
        }
        grecaptcha.reset();
      });
    }
  }

  ngOnInit() {
  }

  hideDialogModal() {
    this.noSuchEmail = false;
    this.openModalWindowDeletedAccount = false;
  }

}
