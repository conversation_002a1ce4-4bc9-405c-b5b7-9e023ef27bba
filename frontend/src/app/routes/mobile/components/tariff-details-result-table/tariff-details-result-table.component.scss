@import "~app/shared/styles/colors";
// main wrap class for component !!!
.app--tariff-details-result-table {
  ::ng-deep .mat-table {
    .mat-cell {
      color: $dark-blue;
    }

    .mat-row, .mat-header-row {
      background-color: $menu-background;
      border-bottom-color: $menu-border;
    }

    .mat-header-cell {
      color: $dark-blue;
      font-weight: bold;
    }
    .mb-col {
      text-align: center;
    }
  }

  ::ng-deep .mat-paginator{
    color: $dark-blue;
    background-color: $menu-background;
  }

 .small{
   min-width:7%;
   max-width:7.69%;
 }
  .middle{
   min-width:15%;
    max-width:15.38%;
  }
  .big{
    min-width:23%;
    max-width:23.02%;
  }
  @media(max-width: 767px) {
    ::ng-deep .mat-table {
      .mat-row, .mat-header-row {
        background-color: #ffffff;
      }
    }
    ::ng-deep.mat-paginator{
      background-color: #ffffff;
    }
  }
  @media(max-width: 768px) {
    .align-top { vertical-align: top; }
    ::ng-deep.mobile-label {
        // width: 110px;
        display: inline-block;
            font-weight: bold;
      }
    ::ng-deep.mat-header-row {
      display: none;
    }
    ::ng-deep.mat-cell {
      text-align: left;
      margin-bottom: 10px;
       .icon {
        padding-left: 0px;
      }
    }
    ::ng-deep.mat-row {
      flex-direction: column;
      align-items: start;
      padding: 8px 24px;
    }
    ::ng-deep.mat-header-row::after,
    ::ng-deep.mat-row::after {
        content: none;
    }

    ::ng-deep.mat-paginator-range-label { display: none; }
    ::ng-deep.mat-paginator-range-actions { margin: 0px auto 5px auto;  }
    ::ng-deep.mat-paginator-page-size { display: none; }
  }
  @media(max-width: 768px){
    .small{
      min-width:100%;
      max-width:100%;
    }
    .middle{
      min-width:100%;
      max-width:100%;
    }
    .big{
      min-width:100%;
      max-width:100%;
    }
  }
  @media(min-width: 768px) {
    .mobile-label { display: none; }

  }
}
