import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TariffDetailsResultTableComponent } from './tariff-details-result-table.component';

describe('TariffDetailsResultTableComponent', () => {
  let component: TariffDetailsResultTableComponent;
  let fixture: ComponentFixture<TariffDetailsResultTableComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TariffDetailsResultTableComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TariffDetailsResultTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
