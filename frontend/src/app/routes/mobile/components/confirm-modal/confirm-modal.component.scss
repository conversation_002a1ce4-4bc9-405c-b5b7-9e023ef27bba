.modal {
  margin-top: 10%;
  text-align: left;
}

.modal-suggestion {

}

.service-name {
  font-weight: bold;
}

.modal-description {
color:#36749d ;
}

.modal-content {
  border: #36749d;
}
.modal-header{
  color: #e54c36;
  border: none;
}
.modal-title {
  color: #e54c36;
}
.modal-body{
  color: #36749d;
  font-size: 16px;
}
.modal-footer{
  border: none;
}
.close {
  opacity: 1;
  color: #e54c36;
}

.button {
  text-align: center;
  border: 1px solid #36749d;
  color: #36749d;
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

@media screen {

}

@media only screen and (max-width: 500px) {
  .modal {
    margin-top: 50%;
  }
}

