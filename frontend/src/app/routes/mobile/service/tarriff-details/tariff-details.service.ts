import { Injectable } from '@angular/core';
import TariffDetailsRequest from '../../../../common/model/mobile/TariffDetailsRequest';
import * as moment from 'moment';
import TariffDetail from '../../../../common/model/mobile/TariffDetail';

@Injectable()
export class TariffDetailsService {

  referencePeriods = {'Ultima settimana': 7, 'Ultimi 15 gg': 15, 'Ultimo mese': 30, 'Ultimi tre mesi': 90};

  tariffTypes = {'Voce': 'Voice', 'SMS/MMS': 'SMS', 'Dati / Servizi a contenuto': 'DATA'};

  constructor() {
  }

  setDateRangeAccordingToReferencePeriodIfExist(tariffDetailsRequest: TariffDetailsRequest, referencePeriod: string) {
    if (tariffDetailsRequest && referencePeriod && this.referencePeriods[referencePeriod]) {
      const today = new Date();
      tariffDetailsRequest.toDate = moment(today).format('YYYYMMDD');
      tariffDetailsRequest.fromDate = moment(new Date(today.getFullYear(),
        today.getMonth(), today.getDate() - this.referencePeriods[referencePeriod])).format('YYYYMMDD');
    }
  }

  setDateRangeAccordingToDateRangeFormDataIfExist(tariffDetailsRequest: TariffDetailsRequest, dateRange: Array<Date>) {
    if (tariffDetailsRequest && dateRange && dateRange.length === 2) {
      tariffDetailsRequest.toDate = moment(dateRange[1]).format('YYYYMMDD');
      tariffDetailsRequest.fromDate = moment(dateRange[0]).format('YYYYMMDD');
    }
  }

  getReferencePeriods(): Array<string> {
    return Object.keys(this.referencePeriods);
  }

  getTariffTypesArray() {
    return Object.keys(this.tariffTypes);
  }

  filterByType(tariffDetails: Array<TariffDetail>, tariffType: string): Array<TariffDetail> {
    if (tariffDetails && tariffType && this.tariffTypes[tariffType]) {
      const type = this.tariffTypes[tariffType];
      return tariffDetails.filter(value => value.service.indexOf(type) > -1);
    }
    return tariffDetails;
  }

}
