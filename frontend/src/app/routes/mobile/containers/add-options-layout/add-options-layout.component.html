<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 add-options-layout" [formGroup]="formGroup">
  <div class="row">
    <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12 sim-select">
      <label for="msisdnId">Seleziona una delle tue SIM.</label>
      <select id="msisdnId" class="form-control" [formControlName]="'msisdnId'">
        <option value=""></option>
        <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
    </div>
  </div>
  <div class="row description">
    Attiva subito una delle seguenti opzioni per ampliare la tua offerta e renderla ancora più completa e adatta alle
    tue esigenze.
  </div>
  <div class="row options-block" *ngIf="productRecords && productRecords.length>0">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 options-table">
      <div class="row option-row" *ngFor="let record of productRecords">
        <div class="col-lg-8 col-md-7 col-sm-7 col-xs-6 option-info">
          <div class="column option-title">{{record.name}}</div>
          <div class="column option-description">{{record.productMapping.descrizioneProdotto}}</div>
        </div>
        <div class="col-lg-4 col-md-5 col-sm-5 col-xs-6 activation-block">
          <div class="col-lg-5 col-md-6 col-sm-5 col-xs-5 option-price">€ {{record.activationPrice}}</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 activation-button-block">
            <button class="button attiva-button" (click)="openModal(record)">ATTIVA</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="statusMessage" class="row status-message-block">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
      <span class="status-message">{{statusMessage}}</span>
    </div>
  </div>
  <app-confirm-modal #confirmModal [option]="confirmModalOption">
    <modal-title>Agguingi opzioni</modal-title>
    <modal-suggestion>Hai richiesto di aggiungere l'opzione</modal-suggestion>
    <modal-description>Desideri continuare?</modal-description>
  </app-confirm-modal>
  <app-confirm-modal #confirmModal [option]="errorModalOption">
    <modal-title>Errore di attivazione</modal-title>
    <modal-suggestion>Non è possibile attivare</modal-suggestion>
    <modal-description>poiché il tuo credito è insufficiente. Ricarica la tua SIM per procedere.</modal-description>
  </app-confirm-modal>
  <app-confirm-modal #confirmModal [option]="infoModalOption">
    <modal-title>Risultato dell'operazione</modal-title>
    <modal-suggestion></modal-suggestion>
    <modal-description>Richiesta di aggiunta offerta inviata correttamente. Riceverai a breve un sms di conferma</modal-description>
  </app-confirm-modal>
</div>
