<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 traffic-details-layout" [formGroup]="formGroup">
  <div class="row">
    <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
      <label for="msisdnId">Sim ricaricabili</label>
      <select id="msisdnId" class="form-control" [formControlName]="'msisdnId'">
        <option value=""></option>
        <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
      <span class="text-danger"
            *ngIf="formGroup.controls['msisdnId'].hasError('required') && (formGroup.controls['msisdnId'].dirty ||
      formGroup.controls['msisdnId'].touched)">Campo Obbligatorio.
      </span>
    </div>
    <div class="col-lg-7 col-md-7 col-sm-7 col-xs-12 xs-margin-top">
      <label class="control-group-title">Seleziona la tipologia di traffico:</label>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 checkbox-block">
        <label *ngFor="let tariffType of tariffTypes" class="checkbox-inline checkbox-container">
          <input name="tariffType" [formControl]="formGroup.controls['tariffType']" type="radio" class="form-checkbox"
                 [value]="tariffType"/>
          <span class="mark"></span><span class="checkbox-label">{{tariffType}}</span></label>
        <div class="text-danger"
             *ngIf="formGroup.controls['tariffType'].hasError('required') && (formGroup.controls['tariffType'].dirty ||
      formGroup.controls['tariffType'].touched)">Campo Obbligatorio.
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12 xs-margin-top">
      <label>Visualizza i movimenti dal:</label>
      <input type="text"
             placeholder="Seleziona il range di date"
             class="form-control"
             [bsConfig]="{ containerClass: 'theme-dark-blue' , dateInputFormat:'dd-MM-yyyy', showWeekNumbers:false}"
             formControlName="dateRange"
             [formControl]="formGroup.controls['dateRange']"
             [value]="dateRangeValue"
             bsDaterangepicker>
    </div>
    <div class="col-lg-7 clo-ms-7 col-sm-7 col-xs-12 xs-margin-top">
      <label class="col-lg-12 col-md-12 col-sm-12 col-xs-12 suggestion">Periodo di riferimento:</label>
      <!--<div class="col-lg-12 checkbox-block">-->
      <label *ngFor="let referencePeriod of referencePeriods" class="checkbox-inline checkbox-container">
        <input name="referencePeriod"
               [formControl]="formGroup.controls['referencePeriod']" type="radio" [value]="referencePeriod"/>
        <span class="mark"></span><span class="checkbox-label">{{referencePeriod}}</span></label>
      <!--</div>-->
    </div>
  </div>
  <div class="row buttons-group">
    <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
    <span class="text-danger"
          *ngIf="(formGroup.controls['referencePeriod'].hasError('required') && (formGroup.controls['referencePeriod'].dirty ||
    formGroup.controls['referencePeriod'].touched))||(formGroup.controls['referencePeriod'].hasError('required') && (formGroup.controls['referencePeriod'].dirty ||
    formGroup.controls['referencePeriod'].touched))">Please, select period.</span>
    </div>
    <div class="col-lg-7 col-md-7 col-sm-7 col-xs-12 control-buttons-group xs-margin-top">
      <button class="button search-button" (click)="loadTariffDetails()">Ricerca</button>
      <button class="button reset" (click)="formGroup.reset()">Resetta</button>
    </div>
  </div>
  <div class="row" *ngIf="tarriffDetais && tarriffDetais.length">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 xs-margin-top">
      <app-tariff-details-result-table [tariffDetails]="tarriffDetais"></app-tariff-details-result-table>
    </div>
  </div>
</div>
