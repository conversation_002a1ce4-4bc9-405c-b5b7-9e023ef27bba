@import "~app/shared/styles/colors";

.traffic-details-layout {
  .button {
    text-align: center;
    border: 1px solid #36749d;
    color: #36749d;
    padding: 7px 15px;
    border-radius: 5px;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: white;
  }

  select {
    -webkit-appearance: none; /*Removes default chrome and safari style*/
    -moz-appearance: none;
    background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
    background-position-x: 95%;
  }

  .button {
    text-align: center;
    border: 1px solid $menu-border;
    color: $dark-blue;
    padding: 7px 15px;
    border-radius: 5px;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: white;

    &.reset {
      border-color: $coral;
      color: $coral;
    }
  }

  .form-control {
    border: 1px solid $menu-border;
    border-radius: 5px;
    font-weight: bold;
    color: $dark-blue;
  }

  label {
    color: $dark-blue;
    font-weight: bold;
  }

  .row {
    margin-top: 2%;
  }

  .checkbox-block {
    padding-left: 0;
  }

  .c-checkbox span {
    background-color: #ffffff;
    width: 25px;
    height: 25px;
  }

  ::ng-deep.theme-dark-blue {
    .bs-datepicker-head {
      background-color: $dark-blue;
    }
  }

  :-ms-input-placeholder {
    color: $menu-border;
  }

  ::-ms-input-placeholder {
    color: $menu-border;
  }

  ::placeholder {
    color: $menu-border;
    opacity: 1;
  }

  .search-button {
    margin-right: 20px;
  }

  .fa-check {
    font-size: 25px;
  }

  option {
    font-weight: bold;
  }

  .tariff-details {
    margin-left: 1%;
    margin-right: 1%;
  }

  .checkbox-inline {
    display: inline-block;
    padding: 0;
    margin-top: 1%;
    margin-left: 0;
    margin-right: 2%;

    .checkbox-label {
      margin-left: 30px;
    }
  }

  .suggestion {
    padding: 0;
  }

}

@media screen and (max-width: 991px) {
  .traffic-details-layout {
    .buttons-group {
      margin-bottom: 2%;
    }
  }
}

@media screen and (max-width: 767px) {
  .traffic-details-layout {
    .control-buttons-group {
      text-align: center;
    }

    .xs-margin-top {
      margin-top: 3%;
    }
  }
}

@media screen and (max-width: 475px) {
  .traffic-details-layout {
    .checkbox-inline {
      margin-right: 5%;
    }
  }
}

@media screen and (max-width: 450px) {
  .traffic-details-layout {
    .xs-margin-top {
      margin-top: 5%;
    }
  }
}
