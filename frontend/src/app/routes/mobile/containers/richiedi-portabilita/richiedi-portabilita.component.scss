@import "src/app/shared/styles/colors";

.recharge-sim-title {
  color: $dark-blue;
  font-weight: bold;
  margin-top: 2%;
}
.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 10vh;
  background: white;
  border: 2px solid #B0C7DD;
  border-radius: 30px;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 1000px;
}

label {
  color: $dark-blue;
  font-weight: bold;
}

option {
  font-weight: bold;
}


input[type="text"]#simNumberTemplate {
  width: 50px;
  text-align: right;
}

input[type="text"]#simNumber {
  outline: none;
}

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}


.form-control {
  border: 1px solid $menu-border;
  border-radius: 10px;
  font-weight: bold;
  color: $dark-blue;
  top: -7px;
  background-color: #e1ecf2;
}

.form-control-table {
  border: 1px solid #c8d9e9;
  border-radius: 10px;
  font-weight: bold;
  color: #36749C;
  top: -7px;
  background-color: #e1ecf2;
  height: 35px;
  width: 51%;
  margin-left: -1.3%;
}

.modal-image {
  text-align: center;
  margin: 3% auto auto;
  width: 50px;
  margin-left: 46%;
  //display: block;
}

.modal-title-text {
  margin: 4% auto auto;
  width: 60%;
  //display: block;
  text-align: center;
  font-size: 16px;
  color: #36749d;
  font-weight: 500;
}

.modal-text {
  margin: 40px auto;
  width: 85%;
  text-align: left;
  font-size: 18px;
  color: #36749d;
  font-style: italic;
}

.fa-times {
  position: absolute;
  right: 30px;
  font-size: 30px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.text-danger {
  margin-left: 1%;
}

.form-control-select {
  border: 1px solid #c8d9e9;
  font-weight: bold;
  color: #36749C;
  top: -7px;
  left: 16px;
  height: 35.5px;
  background-color: white;
  border-radius: 10px;
  margin-left: -17px;
  width: 51%;
}

.form-control-white {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  top: -7px;
  background-color: white;
}

.control-group-block {
  margin-top: 2%;
}

.button {
  text-align: center;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 5px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.payment-button {
  background-color: $dark-blue;
  color: #ffffff;
}

.importo {
  margin-top: 2%;
  display: flex;
  align-items: center;
  //.form-control {
  //  width: 60%;
  //  display: inline-block;
  //}

  .importo-label {
    display: block;
  }

  .payment-button {
    width: 38.5%;
  }
}

:-ms-input-placeholder {
  color: $menu-border;
}

::-ms-input-placeholder {
  color: $menu-border;
}

::placeholder {
  color: $menu-border;
  opacity: 1;
}

.sisal-payment {
  margin-top: 2%;
  margin-bottom: 2%;

  .sisal-pay {
    background: url("/assets/img/payment/sisalpay.png") no-repeat center;
    background-size: contain;
  }
}

.icon {
  float: left;
  height: 70px;
  padding: 0 20px;
  text-align: center;
}

.select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.modal-buttons {
  text-align: right;
  margin-top: 20px;
  margin-bottom: 2%;

  .cancel-button {
    color: $dark-blue;
    padding: 3px 8px;
    border: 1px solid $dark-blue;
    margin-right: 10px;
    border-radius: 10px;
    font-size: 14px;
    background-color: #ffffff;
    display: inline-block;
    cursor: pointer;
  }

  .continue-button {
    color: white;
    padding: 3px 8px;
    margin-left: 10px;
    border: 1px solid $dark-blue;
    border-radius: 10px;
    font-size: 14px;
    background-color: $dark-blue;
    display: inline-block;
    cursor: pointer;
  }

  .accept-modal-button {
    color: white;
    padding: 3px 8px;
    margin-left: 10px;
    border: 1px solid #9BC641;
    border-radius: 2px;
    font-size: 14px;
    background-color: #9BC641;
    display: inline-block;
    cursor: pointer;
  }

}

.modal-buttons-concept {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 2%;

  .cancel-button {
    color: $dark-blue;
    padding: 10px 20px;
    border: 2px solid #36749A;
    border-radius: 10px;
    margin-right: 10px;
    font-size: 15px;
    font-weight: bold;
    background-color: #ffffff;
    display: inline-block;
    cursor: pointer;
  }

  .continue-button {
    color: white;
    padding: 10px 20px;
    margin-left: 10px;
    border: 1px solid $dark-blue;
    border-radius: 10px;
    font-weight: bold;
    font-size: 15px;
    background-color: $dark-blue;
    display: inline-block;
    cursor: pointer;
  }

}

@media screen and (max-width: 991px) {
  .sisal-payment {
    padding-left: 15px;
    margin-bottom: 2%;
  }
  .inner-modal-div {
    width: 90%;
  }
  .modal-text {
    font-size: 16px;
  }
}

@media screen and (max-width: 767px) {

  .optima-number-block {
    padding: 0;
    margin-top: 2%;
  }
  .importo {
    display: block;
    margin-top: 2%;

    //.form-control {
    //  width: 100%;
    //}

    .importo-label {
      display: block;
    }

    .payment-button {
      margin-top: 2%;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }

    .message-block {
      text-align: center;
    }
  }
  .sisal-payment {
    display: flex;

    .sisal-pay {
      margin: auto;
    }
  }
}

@media screen and (max-width: 1287px) {
  .form-control-table {
    height: 52px;
  }

  .form-control-select {
    height: 52px;
  }
}

@media screen and (max-width: 536px) {
  .icon {
    left: 32%;
  }
  .form-control-table {
    width: 66%;
  }

  .form-control-select {
    width: 38.8%;
    font-size: 19px;
  }
}

@media screen and (max-width: 442px) {
  .form-control-table {
    font-size: 11px;
    height: 46px;
  }

  .form-control-select {
    height: 46px;
    font-size: 14px;
  }
}
