@import "src/app/shared/styles/colors";

.container {
  padding-bottom: 15px;
}

.control-group-block {
  //margin-top: 0%;
}

.mobile-title {
  align-items: center;
  justify-content: space-around;
}

.recharge-sim-title {
  color: $dark-blue;
  font-weight: bold;
  margin-top: 5%;
  padding: 0;
}

.fields {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  background-color: white;
  padding: 8px;
  margin-top: 10px;
  margin-left: -3px;
  width: 62%;
}

.fields-width {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  background-color: white;
  padding: 8px;
  margin-top: 10px;
  margin-left: -3px;
  width: 49.5%;
}

.filled-fields {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: #e1ecf2;
  margin-top: 10px;
  margin-left: 15px;
}

.filled-fields-right {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: white;
  padding: 8px;
  margin-top: 10px;
  float: right;
}

.filled-fields-left {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: white;
  padding: 8px;
  margin-top: 10px;
  float: left;
  overflow: hidden;
}

.upload-button {
  border-radius: 10px;
  background-color: #65d1eb;
  color: white;
  font-size: 13px;
  padding: 8px;
  margin-top: 7px;
  width: 125px;
  cursor: pointer;
  font-weight: 100;
}

#files-upload, #files-upload-2, #files-upload-3 {
  position: absolute;
  z-index: -1;
  opacity: 0;
  /*  margin-top: -25px;*/
  cursor: pointer;
}

.no-left-margin {
  margin-left: 0 !important;
}

.remove-file {
  width: 55px;
  margin-top: 1px;
  cursor: pointer;
}
.left-position-for-error{
  left: -13px;
}
.checkbox-text {
  color: #36749C;
  font-weight: bold;
}

.checkbox-block {
  padding: 8px 0;
}

.checkbox-inline {
  display: inline-block;
  padding: 0;
  margin-top: 1%;
  margin-left: 0;
  margin-right: 2%;
}

.checkbox-label {
  margin-left: 30px;
}

.select-button {
  margin-left: 10px;
}

.mark{
  margin-top: 2%;
}

.button-text {
  margin-left: 25px;
}

.form-control {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: #e1ecf2;
  width: 13%;
  height: 38.2px;
  margin-top: -2px;
}

.form-control-width {
  border: 1px solid #c8d9e9;
  border-radius: 5px;
  font-weight: bold;
  color: #36749C;
  background-color: #e1ecf2;
  width: 26%;
  height: 39px;
  padding-left: 10px;
  margin-top: -4px;
}

.text-select {
  font-size: 12pt;
  color: #36749d;
  font-weight: bold;
  margin-top: 10px;
}

.select-white {
  border: 1px solid #c8d9e9;
  border-radius: 5px;
  font-weight: bold;
  color: #36749C;
  background-color: white;
  padding: 8px;
  margin-top: 10px;
  float: right;
}

.select-border {
  border: 1px solid #c8d9e9;
  border-radius: 5px;
  font-weight: bold;
  background-color: white;
  margin-top: 9px;
  height: 38px;
  color: #36749d;
  padding: 8px;
  //width: 54%;
  margin-left: -15px;
  width: 80%;
}

.disable-select {
  color: gray;
}

.form-control-select {
  border: 1px solid #c8d9e9;
  font-weight: bold;
  color: #36749C;
  left: 16px;
  height: 38.9px;
  width: 15%;
  background-color: white;
  margin-left: -15%;
  padding: 5px;
}

.form-width {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: #e1ecf2;
  padding: 8px;
  width: 74.9%;
  height: 38.9px;
  margin-top: -2px;
}

/*.form-inline {
  //margin-bottom: 15px !important;
}*/

.margin-header {
  margin-bottom: 31px;
}

.margin-left-part{
  margin-top: 13px !important;
}

.form-control-date {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
  background-color: #e1ecf2;
  width: 75.5%;
  height: 38.9px;
  margin-top: -2px;
  padding-left: 10px;
}

.radio-button-text {
  margin-left: 25px;
  font-size: 12px;
  width: 65%;
  margin-top: 1% !important;
}

.button {
  text-align: center;
  border: 1px solid #36749C;
  color: white;
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: $dark-blue;
  margin-left: auto;
}

.button-text-select {
  color: white;
}

.last-button {
  text-align: center;
  border: 1px solid #36749C;
  border-radius: 5px;
  background-color: #36749C;
  color: white;
}

.title-modal {
  color: #36749d;
  width: 85%;
  font-size: 20px;
  margin: 2%;
  font-style: italic;
}

.iccd {
  background-color: gray;
}

.modal-text {
  color: #36749d;
  width: 85%;
  font-size: 15px;
  margin: 2%;
  font-style: italic;
}

.row-data {
  //justify-content: space-between;
  margin: 2%;
  color: #36749d;
  font-size: 15px;
  font-weight: bold;
  display: flex;
  border-bottom: 1px double #36749d;
}

.value {
  font-weight: initial;
  margin-left: auto;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.display {
  display: block;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 20px;
  border: 2px solid #36749d;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 600px;
}

.fa-times {
  position: absolute;
  right: 30px;
  font-size: 30px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}

.button-modifica {
  text-align: center;
  border: 1px solid #36749C;
  color: #36749d;
  padding: 7px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.row-buttons {
  margin-left: -15px;
  margin-right: -15px;
  display: flex;
  margin: 2%;
}

.modal-text-otp {
  color: #36749d;
  width: 85%;
  font-size: 21px;
  margin: 7%;
  font-style: inherit;
  text-align: center;
  margin-bottom: 100px;
}

.input-otp {
  text-align: center;
  border: 1px solid #36749C;
  color: #36749d;
  padding: 7px 15px;
  border-radius: 5px;
  position: absolute;
  margin-top: 10px;
  top: 59%;
  left: 56.7%;
  margin-right: -50%;
  transform: translate(-66%, -50%);
  font-size: 20px;
}

.row-buttons-otp {
  margin-left: auto;
  margin-right: auto;
  width: 14em;
  margin-top: 30px;
}

.modal-image {
  text-align: center;
  margin: 3% auto auto;
  width: 50px;
  margin-left: 46%;
  //display: block;
}

.modal-title {
  margin: 4% auto auto;
  width: 60%;
  //display: block;
  text-align: center;
  font-size: 18px;
  color: #36749d;
  font-weight: 700;
}

.modal-title-text {
  margin: 4% auto auto;
  width: 60%;
  //display: block;
  text-align: center;
  font-size: 16px;
  color: #36749d;
  font-weight: 500;
}

.modal-text-custom {
  text-align: center;
  color: #36749d;
  width: 70%;
  padding-top: 10px;
  font-size: 18px;
  margin: 4% auto auto;
}
.text-danger-modal {
  font-size: 18px;
  margin-bottom: 16%;
  font-style: inherit;
  text-align: center;
  margin-top: -50px;
}

.button-applica {
  left: 40.5%;
  margin: 15px 0;
  border-radius: 10px;
}

.colour {
  border: 1px solid $green;
}

.label-position{
  margin-bottom: -5px;
  margin-top: 15px;
  margin-left: -13px;
  font-weight: 100;
}
.label-position-right-part{
  left: -15px;
}
.bs-datepicker {
  position: absolute;
  background-color: white;

  .ngb-dp-header {
    background-color: #36749d;
  }

  select {
    background-color: white;
    color: white;
  }
}

.hidden-select {
  color: gray !important;
}

@media screen and (max-width: 1410px) {
  .row-right {
    width: 40%;
  }
}

@media screen and (max-width: 1275px) {
  .row-left {
    width: 40%;
  }
  .form-control-select {
    margin-left: -15.8%;
  }
  .button-applica {
    left: 31.5%;
  }
  .select-border {
    width: 81%;
  }
}

@media screen and(max-width: 400px) {
  .select-border {
    width: 107%;
  }
}
@media screen and(min-width: 319px) and (max-width: 460px) {
  .fields-width {
    width: 67%;
  }
  .select-border {
    width: 108%;
  }
  .small-row-bottom {
    width: 100%;
    margin-left: 2%;
    font-size: 12px;
    padding-right: 0;
    padding-left: 0;
  }
  .form-control {
    font-size: 12px;
    height: 35px;
  }
  .form-control-date {
    width: 100%;
  }
}
@media screen and(min-width: 460px) and (max-width: 640px) {

  .form-control-select {
    width: 19.9%;
  }
  .select-border{
    width: 89%;
  }
  .fields-width {
    width: 57.6%;
  }
  .form-control-select {
    width: 24%;
  }
  .form-control-date {
    width: 83.5%;
  }
}
@media screen and(min-width: 640px) and (max-width: 991px) {
  .form-control-select {
    width: 20.6%;
  }
  .select-border {
    width: 87.7%;
  }
  .fields-width {
    width: 57.7%;
  }
  .form-width {
    width: 78.3%;
  }
  .form-control-date {
    width: 83.8%;
  }
}
@media screen and(min-width: 991px) and (max-width: 1200px) {
  .fields-width {
    width: 51.2%;
  }
  .form-control {
    width: 15%;
  }
  .select-border {
    width: 84.7%;
  }
  .button-applica {
    left: 21.5%;
  }
  .form-width {
    width: 77.1%;
  }
  .form-control-date {
    width: 77.6%;
  }
}

@media screen and (max-width: 991px) {
  .fields {
    width: 65.8%;
    padding-top: 11.5px;
    padding-bottom: 8px;
  }
  .container {
    background-color: #f0f5f9;
  }
  .filled-fields-left {
    width: 100%
  }
  .filled-fields-right {
    width: 100%
  }
  .center-sim-title {
    margin-top: 19px;
  }
  .center-sim-title {
    margin-left: 16%;
  }
  .button-applica {
    left: 25.5%;
  }
  .small-row {
    width: 100%;
    font-size: 13px;
    margin-left: 0%;
  }
  .small-row-bottom {
    width: 100%;
    margin-left: 10%;
  }
  .form-control-width {
    border: 1px solid #c8d9e9;
    border-radius: 5px;
    font-weight: bold;
    color: #36749C;
    background-color: #e1ecf2;
    width: 26%;
    height: 39px;
    margin-top: -1px;
  }
  .recharge-sim-title {
    color: #36749C;
    font-weight: bold;
    //margin-top: 0%;
  }
  .form-control {
    border: 1px solid #c8d9e9;
    border-radius: 5px;
    font-weight: bold;
    color: #36749C;
    background-color: #e1ecf2;
    width: 18%;
    height: 40px;
    margin-top: -5px;
  }
}

@media screen and (max-width: 768px) {
  .form-control {
    display: inline;
  }
}

@media screen and (max-width: 768px) {
  .form-control {
    display: inline;
  }
  .form-group {
    margin-bottom: 0px;
  }
  .form-control-date {
    margin-top: 11px;
  }
}

@media screen and (max-width: 733px) {
  .center-sim-title {
    margin-left: 11%;
  }
  .inner-modal-div {
    width: 335px;
  }
  .input-otp {
    left: 63.7%;
  }
}

@media screen and (max-width: 458px) {
  .button-applica {
    left: 21.5%;
  }
  .form-control-select {
    width: 22%;
    margin-left: -17%;
  }
  .form-width {
    width: 93.6%;
  }
  .fields-width {
    height: 40px;
    width: 66%;
  }
  .form-control-width {
    width: 33%;
  }
  .fields {
    width: 78%;
  }
  .form-control {
    width: 21%;
  }
  .small-row-bottom {
    width: 100%;
    margin-left: 0%;
  }
  .center-sim-title {
    margin-left: 9%;
  }
  .button-applica {
    left: 12.5%;
  }
}
