import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserDataService} from '../../../../../common/services/user-data/userData.service';
import {UserData} from '../../../../../common/model/userData.model';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {InvoiceService} from '../../../../invoices/invoice.service';
import Validator from '../../../../../common/utils/Validator';
import {DialogModalActions} from '../../../../../redux/dialogModal/actions';
import {MobileService} from '../../../../../common/services/mobile/mobile.service';
import MobileOperator from '../../../../../common/model/mobile/MobileOperator';
import {OtpService} from '../../../../../common/services/otp/otp.service';
import {IncidentEventService} from '../../../../../common/services/incedentEvent/incident-event.service';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {EmailService} from '../../../../../common/services/email/email.service';
import {HttpClient} from '@angular/common/http';
import {MnpService} from '../../../../../common/services/mnp/MnpService';
import {MnpActivation} from '../../../../../common/model/mnp/mnp.model';
import * as moment from 'moment';
import {OffersService} from '../../../../../common/services/offers/offers.service';
import {LoggerService} from '../../../../../common/services/logger/logger.service';
import {FormUtils} from '../../../../../common/utils/FormUtils';
import {ConstantUtil} from '../../../../../utils/ConstantUtil';

@Component({
  selector: 'app-add-options-richiedi-portabilita',
  templateUrl: './add-options-richiedi-portabilita.component.html',
  styleUrls: ['./add-options-richiedi-portabilita.component.scss'],
  providers: [MnpService, LoggerService]
})
export class AddOptionsRichiediPortabilitaComponent implements OnInit {

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;
  userData: UserData;
  formGroup: FormGroup;
  formGroupOTP: FormGroup;
  mobileOperators: Array<MobileOperator>;
  typesContract: Array<string> = ['Prepagato', 'Postpagato'];
  typesSim: Array<string> = ['SIM tradizionale', 'E-SIM'];
  transferCredit: Array<string> = ['Si', 'No'];
  date: any;
  dateSelect: boolean;
  showFinalModal: boolean;
  showOTPModal: boolean;
  showSuccsessModal: boolean;
  showSendMailInfoModalLostDocument: boolean;
  showInfoModalLostDocument: boolean;
  showCodiceErrato: boolean;
  // documentNumber: string;
  // documentType: string;
  msisdnId: string;
  blob: Blob = new Blob();
  today = new Date();
  showAllUnCheckedField = false;
  showRichiestaTrasferimentoCredito = true;
  isAgree = false;
  mnpActivation: MnpActivation;
  incidentId: string;
  contractRecordArray: Array<ContractRecord>;
  // crmGuiId: string;
  number: string;

  isMobile = ConstantUtil.isMobile;

  // first file variable
  firstFileBeforeValidation: File;
  firstFileAfterValidation: File;
  isFirstFileWithWrongSize: boolean;
  isFirstFileWithWrongExtension: boolean;
  isFirstFileUpload: boolean;
  pathFirstFile: string;

  // second file variable
  secondFileBeforeValidation: File;
  secondFileAfterValidation: File;
  isSecondFileWithWrongSize: boolean;
  isSecondFileWithWrongExtension: boolean;
  isSecondFileUpload: boolean;
  pathSecondFile: string;

  // third file variable
  thirdFileBeforeValidation: File;
  thirdFileAfterValidation: File;
  isThirdFileWithWrongSize: boolean;
  isThirdFileWithWrongExtension: boolean;
  isThirdFileUpload: boolean;
  pathThirdFile: string;

  constructor(private service: InvoiceService, public router: Router, userService: UserDataService, private fb: FormBuilder,
              private dialogModalActions: DialogModalActions, private mobileService: MobileService, private otpService: OtpService,
              private incidentEventService: IncidentEventService, private route: ActivatedRoute, private emailService: EmailService,
              private http: HttpClient, private mnpService: MnpService, private offersService: OffersService, private logger: LoggerService) {
    this.formGroup = this.fb.group({
      msisdnIdFromUrl: [null],
      /*conditionDocument: [null, [Validators.required]],*/
      optimaNumber: [null, [Validators.required, Validator.withLength(6, 13), Validator.digits()]],
      operator: [null, [Validators.required]],
      typeContract: [null, [Validators.required]],
      /*simNumberICCD: [null, [Validators.required, Validator.withLength(15, 16), Validator.digits()]],*/
      date: [null, [Validators.required]],
      trasferimentoCredito: [true, [Validators.required]],
      agreement: [null, [Validators.required]],
      frontDocumentPhoto: [null, [Validators.required]],
      backDocumentPhoto: [null, [Validators.required]],
      photoSim: [null, [Validators.required]],
      typeSim: [this.typesSim[0], [Validators.required]]
    });

    this.route.params.subscribe(param => {
      this.formGroup.controls.msisdnIdFromUrl.setValue(parseInt(param.id, 10));
    });

    if (userService) {
      userService.getUserData().subscribe(item => {
        this.userData = item;
        this.number = item.phoneNumber ? item.phoneNumber : item.mobileNumber ? item.mobileNumber : '';
      });
      /*      userService.getUserCodeFromIncidentEvent().subscribe(() => {}, error => {
              this.crmGuiId = new HttpErrorResponse(error).error.text;
            });*/
    }

    this.formGroupOTP = this.fb.group({
      otp: [null, [Validators.required]],
    });

    mobileService.getMobileOperators().subscribe(item => this.mobileOperators = item);
    mobileService.loadContractRecords(localStorage.getItem('clientId')).subscribe(item => this.contractRecordArray = item);
  }

  actualMsisdnId() {
    return (this.formGroup.controls.msisdnIdFromUrl.value === undefined || this.formGroup.controls.msisdnIdFromUrl.value === null)
      ? this.msisdnId : this.formGroup.controls.msisdnIdFromUrl.value;
  }

/*  showAdditionalFieldsToBusinessUser(): boolean {
    if (this.userData) {
      return this.userData.cluster.value === 'BUSINESS';
    }
  }*/

  ngOnInit() {
    this.msisdnId = localStorage.getItem('msisdnId');
    localStorage.removeItem('msisdnId');
    this.formGroup.controls.agreement.setValue('false');
    this.onChanges();
  }

/*  showLostDocumentModal() {
    if (this.userData.email) {
      this.sendEmailWithAttachment();
      this.showSendMailInfoModalLostDocument = true;
    } else {
      this.showInfoModalLostDocument = true;
    }
  }*/

  dateSelectChange() {
    this.dateSelect = true;
    this.formGroup.controls.agreement.setValue(undefined);
  }

  onSubmit() {
    FormUtils.setFormControlsAsTouched(this.formGroup);
    if (this.formGroup.valid) {
      // this.logEvent(event);
      this.showFinalModal = true;
    } else {
      this.showAllUnCheckedField = true;
    }
  }

  hideDialogModal() {
    this.showFinalModal = false;
    this.showOTPModal = false;
    this.showSuccsessModal = false;
  }

  sendOTP() {
    this.otpService.sendOTP().subscribe();
    this.showOTPModal = true;
    this.showFinalModal = false;
  }

  checkOTP() {
    this.otpService.checkOTP(this.formGroupOTP.value.otp).subscribe(item => {
      // this.showCodiceErrato = true;
      if (item.otp === this.formGroupOTP.value.otp) {
        this.showOTPModal = false;
       // this.incidentEventService.createIncidentEventMNP(this.actualMsisdnId()).subscribe(createIncidentItem => {
          this.showOTPModal = false;
          this.showSuccsessModal = true;
          const documentType =
            this.userData.deputy.documentType === 'Patente' ? 'PA' :
              this.userData.deputy.documentType === 'Carta di identità' ? 'CI' :
                this.userData.deputy.documentType === 'Passaporto ' ? 'PS' : '';
          this.mnpActivation = {
            'customerId': localStorage.getItem('clientId'),
            'change': {
              'changeType': 243,
              'portingNumber': '39' + this.formGroup.controls.optimaNumber.value,
              'firstName': this.userData.deputy.firstName,
              'lastName': this.userData.deputy.lastName,
              'fiscalCode': this.userData.deputy.fiscalCode,
              'sourceSupplierCode': this.mobileOperators.find(mobile => mobile.description === this.formGroup.controls.operator.value).codice,
              'sourceSupplierDesc': this.formGroup.controls.operator.value,
              'sourceContractType': this.formGroup.controls.typeContract.value === 'Postpagato' ? 'postpaid' : 'prepaid',
              'documentType': documentType,
              'documentNumber': this.userData.deputy.documentNumber,
              'creditTransfer': this.formGroup.controls.trasferimentoCredito.value,
              'partialCheck': this.formGroup.controls.agreement.value,
              'dataCut': this.formGroup.controls.date.value != null ? moment(new Date(this.formGroup.controls.date.value)).format('DD/MM/YYYY') : moment(new Date).format('DD/MM/YYYY'),
              'msisdn': String(this.actualMsisdnId()),
              'files': [
                {
                  'nomeFile': this.firstFileAfterValidation.name,
                  'uriSharepoint': this.pathFirstFile
                },
                {
                  'nomeFile': this.secondFileAfterValidation.name,
                  'uriSharepoint': this.pathSecondFile
                },
                {
                  'nomeFile': this.thirdFileAfterValidation.name,
                  'uriSharepoint': this.pathThirdFile
                }
              ]
            }
          };
          // this.logger.logIncident('IncidentId = '.concat(String(createIncidentItem.incidentId)));
          this.mnpService.postMnpActivation(this.mnpActivation).subscribe();
       // });
      } else {
        this.showCodiceErrato = true;
      }
    }, () => this.showCodiceErrato = true
    );
  }

/*  documentTypeDocumento() {
    if (this.userData) {
      if (this.userData.deputy.documentNumber.includes('Documento')) {
        this.documentType = this.userData.deputy.documentNumber.split(':')[0];
        this.documentNumber = this.userData.deputy.documentNumber.split(':')[1];
      } else {
        return false;
      }
    }
  }*/

/*  sendEmailWithAttachment() {
    const formData = new FormData();
    this.http.get('assets/documents/Modulo_MNP_Mobile_Privati.pdf', {responseType: 'blob'}).subscribe(item => {
        this.blob = new Blob([item], {type: 'application/pdf'});
        this.http.get('assets/documents/Modulo_MNP_Mobile_Business.pdf', {responseType: 'blob'}).subscribe(item2 => {
          this.blob = new Blob([item2], {type: 'application/pdf'});
        });
        formData.append('subjectHeader', 'Optima Italia – Richiesta di Portabilità');
        formData.append('subject', ' ');
        formData.append('message', 'Gentile Cliente, \n' +
          'in allegato il modulo richiesto. \n' +
          'Le ricordiamo che tutti i moduli Optima sono disponibili nella sezione Moduli del sito optimaitalia.com e della sua Area Clienti Optima. \n' +
          'Grazie e buona giornata.\n' +
          'Servizio Clienti Optima' +
          '\n' +
          '\n' +
          'Questa e-mail è generata da un sistema automatico non presidiato, pertanto si invita cortesemente a non rispondere. Eventuali e-mail ricevute rimarranno inevase.');
        formData.append('files', this.blob, 'Modulo_MNP_Mobile_Privati.pdf');
        formData.append('files', this.blob, 'Modulo_MNP_Mobile_Business.pdf');
        formData.append('email', this.userData.email);
        this.emailService.sendEmail(formData).subscribe(() => {
          this.router.navigate(['faidate/servizi-attivi/mobile/'] + this.actualMsisdnId());
          document.location.reload();
        }, () => {
          this.router.navigate(['faidate/servizi-attivi/mobile/'] + this.actualMsisdnId());
          document.location.reload();
        });
      }
    );
  }*/

  onChanges() {
    this.formGroup.get('typeContract').valueChanges.subscribe(item => {
      this.showRichiestaTrasferimentoCredito = item !== 'Postpagato';
    });
  }

  changeAgreement() {
    if (this.dateSelect) {
      this.isAgree = !this.isAgree;
      this.formGroup.controls.agreement.setValue(this.isAgree ? true : undefined);
    }
  }

  hideSuccessDialogModal() {
    this.router.navigateByUrl('faidate/servizi-attivi/mobile/portabilita/' + this.actualMsisdnId());
  }

  redirectToFaiDaTe() {
      this.router.navigateByUrl('faidate/servizi-attivi/mobile/ricarica/' + this.actualMsisdnId());
  }

/*  getIncidentId(contractRecords: Array<ContractRecord>, msisdnId: number) {
    const contractRecord = contractRecords.find(item => item.msisdnId === msisdnId);
    return contractRecord.id;
  }*/

  logEvent(event?: any) {
    this.logger.logEvent(event);
  }

  validateFile(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf' || ext.toLowerCase() === 'png' || ext.toLowerCase() === 'jpeg' || ext.toLowerCase() === 'jpg';
  }

  // first file methods
  onFirstFileChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length) {
      currentFile = event.target.files[0];
      if (this.firstFileAfterValidation && this.firstFileAfterValidation.name === currentFile.name) {
        event.target.value = '';
        return;
      }

      this.firstFileBeforeValidation = currentFile;
      this.firstFileAfterValidation = currentFile;

      if (currentFile.size > 5242880) {
        this.isFirstFileWithWrongSize = true;
        this.firstFileAfterValidation = null;
      } else if (!this.validateFile(currentFile.name)) {
        this.isFirstFileWithWrongExtension = true;
        this.firstFileAfterValidation = null;
      }

      const reader = new FileReader();
      if (this.firstFileAfterValidation === currentFile) {
        reader.readAsDataURL(currentFile);
        this.isFirstFileWithWrongSize = false;
        this.isFirstFileWithWrongExtension = false;
        this.isFirstFileUpload = true;
      }
      reader.onload = () => {
        this.formGroup.get('frontDocumentPhoto').patchValue(this.firstFileAfterValidation);
        const formDataFirstFile = new FormData();
        formDataFirstFile.append('file', this.firstFileAfterValidation);
        this.mnpService.mnpUploadFile(formDataFirstFile).subscribe(response => {
          this.pathFirstFile = response.ServerRelativeUrl;
        });
      };
    }
    event.target.value = '';
  }

  removeFirstFileFromForm() {
    this.firstFileAfterValidation = null;
    this.isFirstFileUpload = false;
    this.formGroup.get('frontDocumentPhoto').patchValue(this.firstFileAfterValidation);
  }

  // second file methods
  onSecondFileChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length) {
      currentFile = event.target.files[0];
      if (this.secondFileAfterValidation && this.secondFileAfterValidation.name === currentFile.name) {
        event.target.value = '';
        return;
      }

      this.secondFileBeforeValidation = currentFile;
      this.secondFileAfterValidation = currentFile;

      if (currentFile.size > 5242880) {
        this.isSecondFileWithWrongSize = true;
        this.secondFileAfterValidation = null;
      } else if (!this.validateFile(currentFile.name)) {
        this.isSecondFileWithWrongExtension = true;
        this.secondFileAfterValidation = null;
      }

      const reader = new FileReader();
      if (this.secondFileAfterValidation === currentFile) {
        reader.readAsDataURL(currentFile);
        this.isSecondFileWithWrongSize = false;
        this.isSecondFileWithWrongExtension = false;
        this.isSecondFileUpload = true;
      }
      reader.onload = () => {
        this.formGroup.get('backDocumentPhoto').patchValue(this.secondFileAfterValidation);
        const formDataFirstFile = new FormData();
        formDataFirstFile.append('file', this.secondFileAfterValidation);
        this.mnpService.mnpUploadFile(formDataFirstFile).subscribe(response => {
          this.pathSecondFile = response.ServerRelativeUrl;
        });
      };
    }
    event.target.value = '';
  }

  removeSecondFileFromForm() {
    this.secondFileAfterValidation = null;
    this.isSecondFileUpload = false;
    this.formGroup.get('backDocumentPhoto').patchValue(this.secondFileAfterValidation);
  }

  // third file methods
  onThirdFileChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length) {
      currentFile = event.target.files[0];
      if (this.thirdFileAfterValidation && this.thirdFileAfterValidation.name === currentFile.name) {
        event.target.value = '';
        return;
      }

      this.thirdFileBeforeValidation = currentFile;
      this.thirdFileAfterValidation = currentFile;

      if (currentFile.size > 5242880) {
        this.isThirdFileWithWrongSize = true;
        this.thirdFileAfterValidation = null;
      } else if (!this.validateFile(currentFile.name)) {
        this.isThirdFileWithWrongExtension = true;
        this.thirdFileAfterValidation = null;
      }

      const reader = new FileReader();
      if (this.thirdFileAfterValidation === currentFile) {
        reader.readAsDataURL(currentFile);
        this.isThirdFileWithWrongSize = false;
        this.isThirdFileWithWrongExtension = false;
        this.isThirdFileUpload = true;
      }
      reader.onload = () => {
        this.formGroup.get('photoSim').patchValue(this.thirdFileAfterValidation);
        const formDataFirstFile = new FormData();
        formDataFirstFile.append('file', this.thirdFileAfterValidation);
        this.mnpService.mnpUploadFile(formDataFirstFile).subscribe(response => {
          this.pathThirdFile = response.ServerRelativeUrl;
        });
      };
    }
    event.target.value = '';
  }

  removeThirdFileFromForm() {
    this.thirdFileAfterValidation = null;
    this.isThirdFileUpload = false;
    this.formGroup.get('photoSim').patchValue(this.thirdFileAfterValidation);
  }
}
