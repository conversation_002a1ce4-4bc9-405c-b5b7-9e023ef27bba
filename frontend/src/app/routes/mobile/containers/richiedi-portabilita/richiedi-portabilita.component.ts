import {Component, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ContractRecord} from '../../../../common/model/mobile/contract-record/ContractRecord';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {MobileService} from '../../../../common/services/mobile/mobile.service';
import {NotificationService} from '../../../../common/services/notification/notification.service';
import {ActivatedRoute, Router} from '@angular/router';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {DialogModalEntity} from '../../../../common/model/dialogModal/DialogModalEntity';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {EmailService} from '../../../../common/services/email/email.service';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {UserData} from '../../../../common/model/userData.model';
import {HttpClient} from '@angular/common/http';
import {Location} from '@angular/common';
import {
  infoModalRequestNoNoRichiestaPortabilita,
  infoModalRequestNoNoSendEmailRichiestaPortabilita,
  infoModalRequestNoSiRichiestaPortabilita,
  infoModalRequestNoSiSendEmailRichiestaPortabilita,
  infoModalRequestRichiestaPortabilita,
  infoModalRequestSiSiRichiestaPortabilita,
  infoModalRequestSiSiSendEmailRichiestaPortabilita
} from './config/config';

@Component({
  selector: 'app-richiedi-portabilita',
  templateUrl: './richiedi-portabilita.component.html',
  styleUrls: ['./richiedi-portabilita.component.scss']
})
export class RichiediPortabilitaComponent implements OnInit {

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;

  formGroup: FormGroup;

  richiestaDiPortabilita: boolean;

  showAdditionalFields: boolean;
  showModalSiNoOption: boolean;
  userData: UserData;

  blob: Blob = new Blob();
  shouldShowSpinner: boolean;
  showErrorModal: boolean;

  constructor(private mobileService: MobileService, private fb: FormBuilder, private notificationService: NotificationService,
              private route: ActivatedRoute, public router: Router, private offersService: OffersService,
              private dialogModalActions: DialogModalActions, private emailService: EmailService, private userService: UserDataService,
              private http: HttpClient) {
    this.formGroup = this.fb.group({
      msisdnId: [null, Validators.required],
      firstConfirm: [null, Validators.required],
      secondConfirm: [null, Validators.required]
    });
    this.route.params.subscribe(param => {
      this.formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      this.formGroup.controls.firstConfirm.setValue('Si');
    });

    this.formGroup.controls.msisdnId.valueChanges.subscribe(item => {
      this.router.navigate(['/faidate/servizi-attivi/mobile/portabilita/' + item]);
    });

    this.userService.getUserData().subscribe(item => {
      this.userData = item;
    });

    this.offersService.checkIncidentMNP(this.formGroup.controls.msisdnId.value).subscribe(isCloseCheckIncidentRichiestaPortabilita => {
      this.richiestaDiPortabilita = isCloseCheckIncidentRichiestaPortabilita;
      if (this.richiestaDiPortabilita === false) {
        this.showErrorModal = true;
      }
    });
    this.showAlreadyHaveRequestRichiestaPortabilitaModal();
  }

  ngOnInit(): void {
  }

  showAlreadyHaveRequestRichiestaPortabilitaModal() {
    if (this.richiestaDiPortabilita === false) {
      this.showErrorModal = true
    } else {
      this.showAdditionalFields = true;
    }
  }

  onSubmit(firstAnswer, secondAnswer) {
    localStorage.setItem('msisdnId', this.formGroup.value.msisdnId);
    if (this.formGroup.valid) {
      if (firstAnswer == 'Si' && secondAnswer == 'Si') {
        if (this.userData.email) {
          this.sendEmailWithAttachment();
          this.dialogModalActions.showDialogModal(infoModalRequestSiSiSendEmailRichiestaPortabilita(this.userData.email));
        } else {
          this.dialogModalActions.showDialogModal(infoModalRequestSiSiRichiestaPortabilita());
        }
      } else if (firstAnswer == 'No' && secondAnswer == 'No') {
        if (this.userData.email) {
          this.sendEmailWithAttachment();
          this.dialogModalActions.showDialogModal(infoModalRequestNoNoSendEmailRichiestaPortabilita(this.userData.email));
        } else {
          this.dialogModalActions.showDialogModal(infoModalRequestNoNoRichiestaPortabilita());
        }
      } else if (firstAnswer == 'No' && secondAnswer == 'Si') {
        if (this.userData.email) {
          this.sendEmailWithAttachment();
          this.dialogModalActions.showDialogModal(infoModalRequestNoSiSendEmailRichiestaPortabilita(this.userData.email));
        } else {
          this.dialogModalActions.showDialogModal(infoModalRequestNoSiRichiestaPortabilita());
        }
      } else if (firstAnswer == 'Si' && secondAnswer == 'No') {
        this.showModalSiNoOption = true
      }
    } else {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    }

  }

  sendEmailWithAttachment() {
    const formData = new FormData();
    this.http.get('assets/documents/Modulo_MNP_Mobile_Privati.pdf', {responseType: 'blob'}).subscribe(item => {
        this.blob = new Blob([item], {type: 'application/pdf'});
        this.http.get('assets/documents/Modulo_MNP_Mobile_Business.pdf', {responseType: 'blob'}).subscribe(item2 => {
          this.blob = new Blob([item2], {type: 'application/pdf'})
        });
        formData.append('subjectHeader', 'Optima Italia – Richiesta di Portabilità');
        formData.append('subject', ' ');
        formData.append('message', 'Gentile Cliente, \n' +
          'in allegato il modulo richiesto. \n' +
          'Le ricordiamo che tutti i moduli Optima sono disponibili nella sezione Moduli del sito optimaitalia.com e della sua Area Clienti Optima. \n' +
          'Grazie e buona giornata.\n' +
          'Servizio Clienti Optima' +
          '\n' +
          '\n' +
          'Questa e-mail è generata da un sistema automatico non presidiato, pertanto si invita cortesemente a non rispondere. Eventuali e-mail ricevute rimarranno inevase.');
        formData.append('files', this.blob, 'Modulo_MNP_Mobile_Privati.pdf');
        formData.append('files', this.blob, 'Modulo_MNP_Mobile_Business.pdf');
        formData.append('email', this.userData.email);
        this.emailService.sendEmail(formData).subscribe(() => {
          this.router.navigate(['faidate/servizi-attivi/mobile/'] + this.formGroup.controls.msisdnId.value);
          document.location.reload();
        }, error => {
          this.router.navigate(['faidate/servizi-attivi/mobile/'] + this.formGroup.controls.msisdnId.value);
          document.location.reload();
        });
      }
    );
  }

  hideDialogModal() {
    this.router.navigateByUrl(['faidate/servizi-attivi/mobile/'] + this.formGroup.controls.msisdnId.value);
    document.location.reload();
  }

  redirectToRichiediPortabilita() {
    this.router.navigate(['faidate/servizi-attivi/mobile/richiedi-portabilita/' + this.formGroup.value.msisdnId]);
  }
}
