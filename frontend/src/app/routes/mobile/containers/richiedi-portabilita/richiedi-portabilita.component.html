<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 control-group-block">
  <div [formGroup]="formGroup" class="col-lg-11 col-md-11 col-sm-11 no-padding">
    <label for="simList">Seleziona una delle tue SIM.</label>

    <select id="simList" class="form-control" formControlName="msisdnId"
            (click)="showAlreadyHaveRequestRichiestaPortabilitaModal()">
      <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
              [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
        SIM {{record.msisdnId}}
      </option>
    </select>
    <br>
    <div *ngIf="showAdditionalFields">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label class="col-lg-6 col-md-6 col-sm-6 col-xs-6 form-control-table" for="simList">Sono intestatario della SIM
          di cui richiedo portabilità
          <span placement="right" triggers="click"
                [popover]="infoPopTemplate">
                                        <em class="info-circle">i</em>
                                    </span></label>
        <select class="form-control-select col-lg-6 col-md-6 col-sm-6 col-xs-6" name="first"
                [formControlName]="'firstConfirm'">
          <option value=""></option>
          <option [attr.value]="'Si'" [defaultSelected]="'Si'">
            {{'Si'}}
          </option>
          <option [attr.value]="'No'">
            {{'No'}}
          </option>
        </select>
      </div>
      <span class="text-danger"
            *ngIf="formGroup.controls['firstConfirm'].hasError('required')
            && (formGroup.controls['firstConfirm'].dirty || formGroup.controls['firstConfirm'].touched)">
        Campo obbligatorio
        </span>
      <br>
      <br>
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label style="padding-top: 6px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6  form-control-table" for="simList">SIM rubata o
          smarrita</label>
        <select class="form-control-select col-lg-6 col-md-6 col-sm-6 col-xs-6" name="secondConfirm"
                [formControlName]="'secondConfirm'" >
          <option value=""></option>
          <option [attr.value]="'Si'">
            {{'Si'}}
          </option>
          <option [attr.value]="'No'">
            {{'No'}}
          </option>
        </select>
      </div>
      <span class="text-danger"
            *ngIf="formGroup.controls['secondConfirm'].hasError('required')
            && (formGroup.controls['secondConfirm'].dirty || formGroup.controls['secondConfirm'].touched)">
        Campo obbligatorio
        </span>

      <div class="buttons modal-buttons">
        <div class="cancel-button" (click)="hideDialogModal()" [routerLinkActive]="['active']"
             [routerLink]="['/faidate/servizi-attivi/mobile/', formGroup.controls.msisdnId.value]">ANNULLA
        </div>
        <div class="continue-button" [style.backgroundColor]="formGroup.valid? '#36749d' : 'gray'"
             [attr.disabled]="!formGroup.valid  ? 'disabled' : null"
             (click)="onSubmit(formGroup.controls.firstConfirm.value, formGroup.controls.secondConfirm.value)">
          PROCEDI
        </div>
      </div>
    </div>
  </div>

</div>
<ng-template #infoPopTemplate>
  Sei intestatario del numero se hai
  sottoscritto il contratto di acquisto
  della SIM fornendo all’attuale
  operatore i tuoi dati come titolare del
  numero.
</ng-template>
<div class="modal-div show" *ngIf="showErrorModal">
  <div class="inner-modal-div" style="width: 600px">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <img class="image modal-image" src="/assets/img/optima/Set_Icone_AreaClienti_Segnalazione.png">
    <div class="title modal-title-text">
      Gentile Cliente, sulla SIM selezionata risulta già una richiesta di portabilità.
    </div>
  </div>
</div>

<div class="modal-div show" *ngIf="showModalSiNoOption">
  <div class="inner-modal-div">
    <h3 class="text-center font-weight-bold">Il sottoscritto dichiara di:</h3>
    <div class="modal-text">
      <div>• voler recedere dal rapporto contrattuale in essere con l’operatore di provenienza e di volere instaurare un
        rapporto contrattuale con Optima;
      </div>
      <div>• essere stato informato che la sottoscrizione del contratto con Optima non lo solleva dagli obblighi
        relativi al precedente contratto stipulato con l’operatore di provenienza;
      </div>
      <div>• essere stato informato riguardo alla disponibilità del servizio di trasparenza tariffaria ed alle modalità
        per poterne usufruire;
      </div>
      <div>• essere stato informato riguardo alle modalità vigenti per la restituzione o trasferibilità del credito
        residuo;
      </div>
      <div>• essere stato informato che, una volta avviata la procedura, la richiesta di portabilità non può essere
        revocata e che, in caso di ripensamento, può chiedere di portare nuovamente il numero verso l’operatore che sta
        lasciando o verso qualunque altro operatore;
      </div>
      <div>• di essere stato informato che Optima effettuerà la prevalidazione di cui all’art.17 dell’accordo Quadro MNP
        tramite SMS o Vocal Ordering, che in assenza di conferma mediante sms da parte del Cliente, Optima non potrà
        procedere con la portabilità;
      </div>
      <div>• aver preso visione del modulo riguardante l’assenso/diniego alla pubblicazione del numero negli elenchi
        telefonici, che gli stato sottomesso;
      </div>
      <div>• aver preso visione dell’Informativa ai sensi degli artt. 13 e 14 del Regolamento 2016/679 UE disponibile
        sul sito <u><a href="https://www.optimaitalia.com" target="_blank">www.optimaitalia.com</a></u>.
      </div>
      <div>Si rinvia alla Carta dei Servizi, presente sul sito <u><a href="https://www.optimaitalia.com"
                                                                     target="_blank">www.optimaitalia.com</a></u>,
        per un dettaglio sugli indennizzi cui si ha diritto in caso di ritardo nell’attivazione del servizio.
      </div>
    </div>
      <div class="modal-buttons-concept">
        <div class="cancel-button" (click)="hideDialogModal()">ANNULLA</div>
        <div class="continue-button" (click)="redirectToRichiediPortabilita()">PROCEDI</div>
      </div>
  </div>
</div>
