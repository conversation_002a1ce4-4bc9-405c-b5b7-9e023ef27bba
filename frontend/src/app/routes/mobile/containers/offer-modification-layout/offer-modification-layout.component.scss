@import "~app/shared/styles/colors";

.offer-modification-layout {
  color: $dark-blue;

  label, .description {
    color: $dark-blue;
    font-weight: bold;
  }

  .status-message {
    font-size: 24px;
    font-weight: bold;
    color: $dark-blue;
  }

  option {
    font-weight: bold;
  }

  .form-control {
    border: 1px solid $menu-border;
    border-radius: 5px;
    font-weight: bold;
    color: $dark-blue
  }

  .button {
    text-align: center;
    border: 1px solid $dark-blue;
    color: $dark-blue;
    padding: 7px 15px;
    border-radius: 5px;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: white;
  }

  .icon {
    float: left;
    height: 70px;
    padding: 0 20px;
    text-align: center;
    margin-left: 20px;
  }

  .optima-number-block, .sim-select {
    padding-left: 0;
  }

  .row {
    margin: 1% 2% 0 2%;
  }

  .options-block {

    .options-table {
      border: 1px solid $menu-border;
      background-color: #ffffff;
      border-radius: 5px;
      margin-bottom: 1%;

      .option-row {
        border-bottom: 1px solid $menu-border;
        margin-right: 5px;
        margin-left: 5px;
        padding-bottom: 1%;
        display: flex;
        align-items: center;

        &:last-of-type {
          border-bottom: none;
        }
      }

      .option-info {
        padding-left: 0;
        border-right: 1px solid $menu-border;
      }

      .option-title {
        font-size: 16px;
        color: $dark-blue;
        font-weight: bold;
      }

      .option-price {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
      }

      .activation-button-block {
        text-align: center;

        .attiva-button {
          background-color: $dark-blue;
          color: #ffffff;
        }
      }
    }
  }

  select {
    -webkit-appearance: none; /*Removes default chrome and safari style*/
    -moz-appearance: none;
    background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
    background-position-x: 95%;
  }
}

@media only screen and (max-width: 991px) {
  .add-options-layout {
    padding-left: 0;
  }
}

@media screen and (max-width: 767px) {
  .offer-modification-layout {
    padding-left: 15px;

    .sim-select {
      padding: 0;
    }

    .description, .options-block {
      margin-top: 2%;
    }

    .row {
      margin-left: 0;
      margin-right: 0;
    }

    .options-block {
      .options-table {
        padding-left: 5px !important;
        padding-right: 5px !important;
      }
    }
  }
}

@media screen and (max-width: 575px) {
  .offer-modification-layout {
    .option-price {
      font-size: 22px;
      padding-left: 0;
      padding-right: 5px;
    }

    .description, .options-block, .sim-select {
      margin-top: 5%;
    }
    .option-description {
      font-size: 12px !important;
    }

    .activation-block {
      padding: 0;

      .activation-button-block {
        padding: 0;

        .button {
          padding: 5px 15px;
        }
      }
    }
  }
}

@media screen and (max-width: 450px) {
  .offer-modification-layout {
    .options-block {
      .options-table {
        .option-row {
          margin-left: 0;
          margin-right: 0;

        }

        .option-info {
          padding-right: 0;
        }

        .option-price {
          font-size: 20px;
          padding-left: 3px;
          padding-right: 0;
        }
      }
    }
  }
}
