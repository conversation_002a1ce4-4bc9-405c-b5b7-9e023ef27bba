<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 offer-modification-layout"
     [formGroup]="formGroup">
  <div class="row">
    <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12 sim-select">
      <label for="msisdnId">Seleziona una delle tue SIM.</label>
      <select id="msisdnId" class="form-control" [formControlName]="'msisdnId'">
        <option value=""></option>
        <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroup.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
    </div>
  </div>
  <div class="row description">
    Scegli l'offerta più adatta a te e trova la giusta combinazione tra traffico telefonico, messaggistica e traffico
    dati che ti
    permetterà di parlare e restare connesso quanto vuoi.
  </div>
  <div class="row options-block" *ngIf="productRecords && productRecords.length;else noOptionsMessage">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 options-table">
      <div class="row option-row" *ngFor="let record of productRecords">
        <div class="col-lg-8 col-md-7 col-sm-7 col-xs-6 option-info">
          <div class="column option-title">{{record.productName}}</div>
          <div class="column option-description">{{record.productDescription}}</div>
        </div>
        <div class="col-lg-4 col-md-5 col-sm-5 col-xs-6 activation-block">
          <div class="col-lg-5 col-md-6 col-sm-5 col-xs-5 option-price">€ {{record.renewalCost}}</div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 activation-button-block">
            <button class="button attiva-button" (click)="openModal(record)">ATTIVA</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #noOptionsMessage>
    <div class="row status-message-block">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
      <span
        class="status-message">Al momento non risulta alcuna offerta attivabile su questo numero.</span>
      </div>
    </div>
  </ng-template>

  <app-confirm-modal #confirmModal [option]="confirmModalOption">
    <modal-title>Modifica offerta</modal-title>
    <modal-suggestion>Hai richiesto di attivare l’offerta <b>{{productName}}.</b></modal-suggestion>
    <modal-description *ngIf="activationCost > 0">Ti ricordiamo che l’operazione di modifica offerta ha un costo di <b>{{activationCost}} €</b>,
      che saranno addebitati alla data del rinnovo dell’offerta.</modal-description>
  </app-confirm-modal>
  <app-confirm-modal #confirmModal [option]="infoModalOption">
    <modal-title>Risultato dell'operazione</modal-title>
    <modal-suggestion></modal-suggestion>
    <modal-description>Richiesta di aggiunta offerta inviata correttamente. Riceverai a breve un sms di conferma
    </modal-description>
  </app-confirm-modal>

</div>
