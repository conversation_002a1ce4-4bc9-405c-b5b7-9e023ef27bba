<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 padding" [formGroup]="formGroup">
  <div class="col-lg-4 col-md-4 col-sm-4 col-x-4">
    <label for="simList">Scegli il numero con l’Autoricarica attiva</label>
    <select id="simList" class="col-lg-10 col-md-10 col-sm-10 col-x-10 form-control" formControlName="msisdnId"
            (change)="getAutoricaricaInformation()">
      <option value=""></option>
      <option *ngFor="let record of contractRecords | async" [attr.value]="record.msisdnId"
              [selected]="record.msisdnId===selectedNumber">
        SIM {{record.msisdnId}}
      </option>
    </select>
  </div>

  <div class="col-lg-1 col-md-1 col-sm-1 col-x-1" *ngIf="isAutoricaricaActive">
    <label>Stato</label>
    <div><b>{{status}}</b></div>
  </div>
  <div class="col-lg-2 col-md-2 col-sm-2 col-x-2" *ngIf="isAutoricaricaActive">
    <label></label>
    <div class="tumbler" (click)="tumblerEvent()">
      <img *ngIf="isActive" class="on" src="/assets/img/icons/ok.png" alt="on">
      <img *ngIf="!isActive" class="off" src="/assets/img/icons/ok_off.png" alt="off">
    </div>
  </div>

  <div class="col-lg-11 col-md-11 col-sm-11 col-xs-11 line" *ngIf="isAutoricaricaActive"></div>

  <div class="col-lg-4 col-md-4 col-sm-4 col-x-4" *ngIf="isAutoricaricaActive">
    <label>Data della prossima Autoricarica</label>
    <div><b>{{nextRenewalOffer | date:'dd/MM/yyyy'}}</b></div>
  </div>
  <div class="col-lg-4 col-md-4 col-sm-4 col-x-4" *ngIf="isAutoricaricaActive">
    <label>Importo dell’Autoricarica</label>
    <div><b>{{importoAutoricarica}} €/mese</b></div>
  </div>

  <div class="col-lg-11 col-md-11 col-sm-11 col-xs-11 line" *ngIf="isAutoricaricaActive && isActive"></div>

  <div class="col-lg-8 col-md-8 col-sm-8 col-x-8" *ngIf="isAutoricaricaActive && isActive">
    <label>Metodo di pagamento</label>
    <div class="payment-method">
      <img *ngIf="showVisaIcon" class="payment-icon" src="assets/img/payment/visa_logo.png" alt="Visa"/>
      <img *ngIf="showMastercardIcon" class="payment-icon" src="assets/img/payment/mastercard_logo.png"
           alt="Mastercard"/>
      <img *ngIf="showMaestroIcon" class="payment-icon" src="assets/img/payment/maestro_logo.png" alt="Maestro"/>
      <img *ngIf="isPayPalActive" class="payment-icon" src="assets/img/payment/paypal_logo.png" alt="PayPal"/>
      <div class="{{isPayPalActive ? 'text-position color-text' : 'text-position'}}"><b>{{cardNumber}}</b></div>
      <!--<img class="action-icons" src="assets/img/icons/delete_icon.png" alt="Delete"/>-->
      <img class="action-icons" src="assets/img/icons/edit_icon.png" alt="Edit" (click)="modifyPaymentInformation()"/>
    </div>
  </div>
</div>

<div class="modal-div display" *ngIf="showModifyPaymentInformationWindow">
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow()"></i>
    <div class="flex-modal-inside">
      <img class="auto-ricarica-img" src="assets/img/payment/Ricarica_big.png" alt=""/>
      <div *ngIf="isActive" class="modal-text">Procedendo disattiverai la ricarica automatica e dovrai procedere a
        ricarica manuale ogni volta che ne avrai bisogno
      </div>
      <div *ngIf="!isActive" class="modal-text">Proseguendo il servizio di autoricarica sarà abilitato</div>
      <div class="flex-buttons">
        <button class="modal-window-annulla-button" (click)="hideModalWindow()">ANNULLA</button>
        <button class="modal-window-confirm-button" (click)="deactivateOrActivateAutoricarica()">CONFERMA</button>
      </div>
    </div>
  </div>
</div>
