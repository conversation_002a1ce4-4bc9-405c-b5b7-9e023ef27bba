@import "src/app/shared/styles/colors";
.recharge-sim-title {
  color: $dark-blue;
  font-weight: bold;
  margin-top: 2%;
}

label {
  color: $dark-blue;
  font-weight: bold;
}

option {
  font-weight: bold;
}


input[type="text"]#simNumberTemplate {
  width: 50px;
  text-align: right;
}

input[type="text"]#simNumber {
  outline: none;
}

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}



.form-control {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
}

.control-group-block {
  margin-top: 2%;
}

.button {
  text-align: center;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 5px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.payment-button {
  background-color: $dark-blue;
  color: #ffffff;
}

.importo {
  margin-top: 2%;
  display: flex;
  align-items: center;
  .form-control {
    width: 60%;
    display: inline-block;
  }

  .importo-label {
    display: block;
  }

  .payment-button {
    width: 38.5%;
  }
}
:-ms-input-placeholder {
  color: $menu-border;
}
::-ms-input-placeholder {
  color: $menu-border;
}
::placeholder{
  color: $menu-border;
  opacity: 1;
}

.sisal-payment {
  margin-top: 2%;
  margin-bottom: 2%;

  .sisal-pay {
    background: url("/assets/img/payment/sisalpay.png") no-repeat center;
    background-size: contain;
  }
}

.icon {
  float: left;
  height: 70px;
  padding: 0 20px;
  text-align: center;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

@media screen and (max-width: 991px) {
  .sisal-payment {
    padding-left: 15px;
    margin-bottom: 2%;
  }

}

@media screen and (max-width: 767px) {
  .optima-number-block {
    padding: 0;
    margin-top: 2%;
  }
  .importo {
    display: block;
    margin-top: 2%;

    .form-control {
      width: 100%;
    }

    .importo-label {
      display: block;
    }

    .payment-button {
      margin-top: 2%;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }
    .message-block{
      text-align: center;
    }
  }
  .sisal-payment {
    display: flex;

    .sisal-pay {
      margin: auto;
    }
  }
}

