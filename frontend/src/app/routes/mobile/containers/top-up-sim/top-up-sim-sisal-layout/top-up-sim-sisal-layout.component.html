<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding" [formGroup]="formGroupSisal">
  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 recharge-sim-title">
    <div>Scegli l'importo da ricaricare su una o più SIM Optima.</div>
    <div>Potrai inserire il tuo numero o i numeri Optima dei tuoi amici.</div>
  </div>
  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 control-group-block">


    <div class="col-lg-5 col-md-5 col-sm-5 no-padding">
      <label for="simList">Seleziona una delle tue SIM.</label>
      <select id="simList" class="form-control" formControlName="msisdnId">
        <option value=""></option>
        <option *ngFor="let record of contractRecordsSelect | async" [attr.value]="record.msisdnId"
                [selected]="record.msisdnId===formGroupSisal.controls['msisdnId'].value">
          SIM {{record.msisdnId}}
        </option>
      </select>
    </div>


    <div class="col-lg-5 col-md-7 col-sm-7 col-xs-12 optima-number-block">
      <label for="simNumber">Scrivi il numero Optima che desideri ricaricare.</label>
      <div class="form-inline" style="display: flex">
        <input id="simNumberTemplate" class="form-control form-group" type="text" value="+39" disabled>
        <input id="simNumber" class="form-control form-group" style="width: -webkit-fill-available;" type="number"
               placeholder="Numero Optima" formControlName="optimaNumber">
      </div>
      <span class="text-danger" *ngIf="formGroupSisal.controls['optimaNumber'].hasError('minLength') &&
       (formGroupSisal.controls['optimaNumber'].dirty || formGroupSisal.controls['optimaNumber'].touched)">
        Lunghezza minima 6 caratteri
      </span>
      <span class="text-danger"
            *ngIf="formGroupSisal.controls['optimaNumber'].hasError('maxLength')
            && (formGroupSisal.controls['optimaNumber'].dirty || formGroupSisal.controls['optimaNumber'].touched)">
        Lunghezza massima 10 caratteri
      </span>
      <span class="text-danger" *ngIf="formGroupSisal.controls['optimaNumber'].hasError('numberExistence') &&
       (formGroupSisal.controls['optimaNumber'].dirty || formGroupSisal.controls['optimaNumber'].touched)">
        Il numero selezionato non è un numero appartenente alla rete Optima
      </span>
      <span class="text-danger" *ngIf="(formGroupSisal.controls['msisdnId'].hasError('required') && (formGroupSisal.controls['msisdnId'].dirty || formGroupSisal.controls['msisdnId'].touched))
        || (formGroupSisal.controls['optimaNumber'].hasError('required') && (formGroupSisal.controls['optimaNumber'].dirty || formGroupSisal.controls['optimaNumber'].touched))">
          Inserisci il numero di telefono da ricaricare
      </span>
    </div>


    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding importo">
      <div class="col-lg-5 col-md-5 col-xs-12 no-padding">
        <label class="importo-label" for="voucher">Inserisci il codice voucher Mooney</label>
        <input id="voucher" class="form-control form-group" type="text" formControlName="voucherCode">
        <button class="button payment-button" (click)="replenishAccount()">RICARICA</button>
        <span class="text-danger" *ngIf="formGroupSisal.controls['voucherCode'].hasError('pattern')
            && (formGroupSisal.controls['voucherCode'].dirty || formGroupSisal.controls['voucherCode'].touched)">
            Lunghezza 12 caratteri numerici
        </span>
        <span class="text-danger" *ngIf="formGroupSisal.controls['voucherCode'].hasError('required')
            && (formGroupSisal.controls['voucherCode'].dirty || formGroupSisal.controls['voucherCode'].touched)">
            Inserisci il codice
        </span>
        <span class="text-danger" *ngIf="formGroupSisal.controls['voucherCode'].hasError('invalidVoucher')">
            Il codice inserito non risulta valido
        </span>
        <span class="text-danger" *ngIf="formGroupSisal.controls['voucherCode'].hasError('usedVoucher')">
            Il codice inserito risulta già utilizzato
        </span>
      </div>
    </div>
  </div>
</div>
