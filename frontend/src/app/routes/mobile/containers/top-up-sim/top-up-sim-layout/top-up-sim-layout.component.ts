import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {PaymentService} from '../../../../../common/services/payment/payment.service';
import * as moment from 'moment';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../../common/model/userData.model';
import {Subscription} from 'rxjs/Subscription';
import {select} from '@angular-redux/store';
import {PayPalService} from '../../../../../common/services/paypal/pay-pal-service.service';
import {environment} from '../../../../../../environments/environment';
import {ObservableUtils} from '../../../../../common/utils/ObservableUtils';
import {ActivatePayPalAutoricaricaRequest, ConfirmPaymentModelRequest, NexiPaymentRequest, NexiPaymentResponse} from '../../../../profilePage/model/ConfirmPaymet';
import SubscriptionAutoricaricaRequest, {AutoricaricaInformationResponse} from '../../../models/PaymentAutoricaricaModels';

declare const AxeptaSDKClient: any;

@Component({
  selector: 'app-top-up-sim-layout',
  templateUrl: './top-up-sim-layout.component.html',
  styleUrls: ['./top-up-sim-layout.component.scss']
})
export class TopUpSimLayoutComponent implements OnInit, OnDestroy {

  @select(['mobile', 'contractRecords'])
  contractRecordsSelect: Observable<Array<ContractRecord>>;
  contractRecord: ContractRecord;
  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userData: UserData;
  phoneNumber: string;
  amountOfMoney: string;
  subscriptionId: string;
  isAutoricaricaPage: boolean;
  autoRicaricaInformation: AutoricaricaInformationResponse;
  userInfoSubscription: Subscription;
  mobileStateSubscription: Subscription;

  constructor(public router: Router, protected route: ActivatedRoute, private paymentService: PaymentService,
              private payPalService: PayPalService) {
    this.route.params.subscribe(params => {
      this.phoneNumber = params.id || this.phoneNumber;
      this.amountOfMoney = params.amount || this.amountOfMoney;
    });
    this.isAutoricaricaPage = this.router.url.includes('autoricarica');
    this.mobileStateSubscription = this.contractRecordsSelect.subscribe(contractRecords => {
      for (let i = 0; i < contractRecords.length; i++) {
        if (contractRecords[i].msisdnId === parseInt(this.phoneNumber, 10)) {
          this.contractRecord = contractRecords[i];
          this.paymentService.postPaymentAutoricaricaInformation(this.fillCheckAutoricaricaRequest()).subscribe(response => {
            this.autoRicaricaInformation = response;
          });
        }
      }
    });
    this.userInfoSubscription = this.userInfo.subscribe(userData => this.userData = userData);
  }

  ngOnInit() {
    if (environment.production === true) {
      this.paymentService.loadScript('https://pay.axepta.it/sdk/axepta-pg-redirect.js');
    } else {
      this.paymentService.loadScript('https://pay-sandbox.axepta.it/sdk/axepta-pg-redirect.js');
    }
  }

  fillCheckAutoricaricaRequest() {
    const request = new SubscriptionAutoricaricaRequest();
    request.COD_CLIENTE = localStorage.getItem('clientId');
    request.SUBSCRIPTION_ID = this.contractRecord.id.toString();
    return request;
  }

  fillPaymentByCardRequest() {
    const request = new ConfirmPaymentModelRequest();
    request.SistemaChiamante = '200007';
    request.CF = this.userData.fiscalCode;
    request.PIVA = this.userData.vatNumber;
    request.CodiceCliente = localStorage.getItem('clientId');
    request.AddInfo1 = localStorage.getItem('clientId');
    request.AddInfo2 = this.contractRecord.id.toString();
    request.AddInfo3 = this.phoneNumber;
    return request;
  }

  fillPayPalAutoricaricaRequest() {
    const request = new ActivatePayPalAutoricaricaRequest();
    request.SistemaChiamante = '200007';
    request.CF = this.userData.fiscalCode;
    request.PIVA = this.userData.vatNumber;
    request.CodiceCliente = localStorage.getItem('clientId');
    request.RagioneSociale = this.userData.nameInInvoice;
    request.AddInfo1 = localStorage.getItem('clientId');
    request.AddInfo2 = this.contractRecord.id.toString();
    request.AddInfo3 = this.phoneNumber;
    request.Data = moment().format('DD/MM/YYYY');
    return request;
  }

  payWithCard() {
    if (this.isAutoricaricaPage) {
      this.paymentService.postPaymentAutoricarica(this.fillPaymentByCardRequest()).subscribe(response => {
        const axeptaClient = new AxeptaSDKClient(response.SDK, response.APILicenseKeyEasy);
        axeptaClient.proceedToPayment(response.PaymentId);
      });
    } else {
      const request = new NexiPaymentRequest(
        this.userData.id,
        this.userData.fiscalCode,
        this.userData.vatNumber,
        Number(this.amountOfMoney),
        this.contractRecord.id.toString(),
        this.phoneNumber
      );
      this.paymentService.topUpSimWithNexi(request).subscribe(response => {
        this.createAndSubmitNexiForm(response);
      });
    }
  }

  createAndSubmitNexiForm(response: NexiPaymentResponse) {
    const form = this.buildNexiForm(response);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }

  private buildNexiForm(response: NexiPaymentResponse): HTMLFormElement {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = response.urL_Form;
    const fields = [
      {name: 'alias', value: response.alias},
      {name: 'importo', value: response.importo},
      {name: 'divisa', value: response.divisa},
      {name: 'codTrans', value: response.codtrans},
      {name: 'url', value: response.url},
      {name: 'url_back', value: response.url_back},
      {name: 'urlpost', value: response.urlpost},
      {name: 'mac', value: response.mac}
    ];
    fields.forEach(({name, value}) => this.appendHiddenInput(form, name, value));
    return form;
  }

  private appendHiddenInput(form: HTMLFormElement, name: string, value: string): void {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = name;
    input.value = value;
    form.appendChild(input);
  }

  payWithPaypal() {
    if (this.isAutoricaricaPage) {
      this.paymentService.postPayPalAutoricarica(this.fillPayPalAutoricaricaRequest()).subscribe(response => {
        window.open(response.returnURL);
      });
    } else {
      const request = {
        'TipoPagamento': 'Paypal',
        'SistemaChiamante': 'Selfcare',
        'Data': moment(Date.now()).format('DD/MM/YYYY'),
        'TotalePagamento': parseFloat(this.amountOfMoney),
        'Ricariche': [{
          'ChannelId': '1',
          'Importo': parseFloat(this.amountOfMoney),
          'NumeroTelefono': this.phoneNumber,
          'SubscriptionID': this.contractRecord.id,
        }],
        'CodiceCliente': this.userData.id,
        'RagioneSociale': this.userData.nameInInvoice,
        'CF': this.userData.fiscalCode,
        'PIVA': this.userData.vatNumber
      };
      this.payPalService.postPayPalActivationRicarica(request).subscribe(item =>
        window.open(item.Return_URL));

    }
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.mobileStateSubscription, this.userInfoSubscription]);
  }
}
