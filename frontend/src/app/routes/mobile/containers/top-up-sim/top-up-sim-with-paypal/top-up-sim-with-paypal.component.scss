input[type="text"]#simNumberTemplate {
  width: 50px;
  text-align: right;
}

input[type="text"]#simNumber {
  outline: none;
}

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
.button {
  text-align: center;
  border: 1px solid #36749CFF;
  color: #36749CFF;
  padding: 5px 15px;
  border-radius: 5px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.payment-button {
  background-color: #36749CFF;
  color: #ffffff;
}

.form-control {
  border: 1px solid #c8d9e9;
  border-radius: 5px;
  font-weight: bold;
  color: #36749CFF;
}

.recharge-sim-title {
  color: #36749CFF;
  font-weight: bold;
  margin-top: 2%;
}

.importo {
  margin: 2% 1% 1%;
  display: flex;
  align-items: center;

  .form-control {
    width: 60%;
    display: inline-block;
  }

  .importo-label {
    display: block;
  }

  .payment-button {
    width: 38.5%;
  }
}

.position {
  margin: 2% 1% 1%;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}
option {
  font-weight: bold;
}
.optima-number-block {
  padding: 0;
  margin-top: 2%;
}
@media screen and (max-width: 767px) {
  .importo {
    display: block;
    margin-top: 2%;

    .form-control {
      width: 100%;
    }

    .importo-label {
      display: block;
    }

    .payment-button {
      margin-top: 2%;
      display: block;
      margin-left: auto;
      margin-right: auto;
    }

    .message-block {
      text-align: center;
    }
  }
}
:-ms-input-placeholder {
  color: #c8d9e9;
}
::-ms-input-placeholder {
  color: #c8d9e9;
}
::placeholder{
  color: #c8d9e9;
  opacity: 1;
}
