import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {OptimaYoung} from '../models/OptimaYoungModels';

@Injectable()
export class OptimaYoungService {

  constructor(private http: HttpClient) {
  }

  public getInviteFriendsInfo(): Observable<OptimaYoung> {
    return this.http.get<OptimaYoung>(`/api/optima-young/${localStorage.getItem('clientId')}`);
  }

}
