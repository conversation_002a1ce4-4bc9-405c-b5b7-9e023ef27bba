.modifica {
  background: url("/assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

.red {
  color: $dark-blue;
  font-weight: bold;
  padding-left: 20px;
}

.icon-pdf-load {
  font-size: 30px;
  line-height: 20px;
}

.menu-button {
  height: 40px;
  line-height: 30px;
  display: flex;
  color: $dark-blue;
  padding: 0 10px;
  font-weight: bold;

  a {
    text-decoration: none;
    color: $dark-blue;
    font-size: 14px;
  }
}

button.odds-bg:nth-child(even) {
  background: $menu-background;
  border-top: 1px solid $menu-border;
}
::ng-deep div.mat-menu-panel {
  overflow: auto;
  height: 250px;
  width: 280px;
  margin-top: 50px;
  margin-right: 30px;
}
