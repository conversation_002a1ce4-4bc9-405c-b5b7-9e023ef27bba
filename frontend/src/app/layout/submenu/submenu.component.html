<div *ngIf="currentSubItems.submenu != null" class="main">
  <!-- <div class="top">
     {{userSurname}} {{userName}}
   </div>-->
  <div class="buttons-group">
    <div *ngFor='let item of currentSubItems.submenu' routerLinkActive="active"
         class="link order-{{item.order}}"
         [routerLink]="[item.link]" (click)="switchOpenClose(item.link)">
      <span class='text left'>{{item.text}} </span>
      <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
    </div>
    <div *ngIf="routerDisplay" routerLinkActive="active" class="link order-40"
         [routerLink]="['/faidate/routerConfigurazione/configuraIlTuoRouter']"
         (click)="switchOpenClose('/faidate/routerConfigurazione/configuraIlTuoRouter')">
      <span class='text left'>CONFIGURAZIONE ROUTER</span>
      <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
    </div>
    <div *ngIf="!routerDisplay && aggiornaDisplay" routerLinkActive="active" class="link order-50"
         [routerLink]="['/faidate/routerConfigurazione/aggiorna']"
         (click)="switchOpenClose('/faidate/routerConfigurazione/aggiorna')">
      <div class="text left">
        <span>AGGIORNA IL SOFTWARE DEL ROUTER</span>
        <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
      </div>

    </div>
    <div class="link order-60" *ngIf="hasPasswordPec" (click)="open('https://webmail.pec.it')">
      <span class='text left'>ACCESSO PEC </span>
      <span class="fa fa-angle-right right" aria-hidden="true"></span>
    </div>
    <div class="{{activeMenu?'active':''}} link order-70" id="submenuChatBtn" (click)="openChat()">
      <span class='text left'>  CHATTA CON NOI </span>
      <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
    </div>
    <div *ngIf="businessCustomer" routerLinkActive="active" class="link order-80"
         [routerLink]="['/faidate/richiedi-visita-agente']"
         (click)="switchOpenClose('/faidate/richiedi-visita-agente')">
      <span class='text left'>PRENOTA VISITA AGENTE</span>
      <span class="fa fa-angle-right arrow right" aria-hidden="true"></span>
    </div>

    <!--<div class="order-80 recontact-menu-button" [routerLink]="['/faidate/recontact']">&lt;!&ndash;*ngIf="isDashListUserExist"&ndash;&gt;
      <div class="recontact-button-image"></div>
    </div>-->
    <div *ngIf="isMobile" class="profile-mobile-layout order-{{mobileLayoutOrder}}">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
<div class="row recontact-button-block" *ngIf="(userInfo | async)?.cluster &&
((userInfo | async)?.cluster.value === 'BUSINESS' || (userInfo | async)?.cluster && (userInfo | async)?.cluster.value === 'CONSUMER')"><!-- &lt;!&ndash;*ngIf="isDashListUserExist"&ndash;&gt; -->
  <div class="recontact-button-desktop" [routerLink]="['/faidate/recontact']">
  </div>
</div>
<div class="recontact-button-mobile" *ngIf="isMobile && (userInfo | async)?.cluster &&
((userInfo | async)?.cluster.value === 'BUSINESS' || (userInfo | async)?.cluster && (userInfo | async)?.cluster.value === 'CONSUMER')" [routerLink]="['/faidate/recontact']">
</div>
