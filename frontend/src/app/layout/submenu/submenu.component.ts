import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {MenuService} from '../../core/menu/menu.service';
import {RouteService} from './route.service';
import {ActivatedRoute, NavigationEnd, Router} from '@angular/router';
import {MenuTag} from './submenu.model';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import {UserDataService} from '../../common/services/user-data/userData.service';
import {faiDaTeMobileLayoutOrdersMap} from '../../routes/fai-da-te/config/config';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';
import {ConstantUtil} from '../../utils/ConstantUtil';


@Component({
  selector: 'app-submenu',
  templateUrl: './submenu.component.html',
  styleUrls: ['./submenu.component.scss']
})
export class SubmenuComponent implements OnInit, OnDestroy {

  isMobile = ConstantUtil.isMobile;

  @select(['services', 'activeServices'])
  serviceData: Observable<object>;
  @select(['services', 'services'])
  services: Observable<object>;
  currentSubItems: MenuTag;
  userName: string;
  userSurname: string;
  activeMenu = false;
  @select(['user', 'userInfo'])
  userInfo: Observable<any>;
  userdataSubscription: Subscription;
  servicesSubscription: Subscription;
  routerDisplay = false;
  aggiornaDisplay = false;
  businessCustomer = false;
  hasPasswordPec: string;
  mobileLayoutOrder: number;
  lastRouteUrl: string;

  constructor(public menu: MenuService, private route: ActivatedRoute, privateService: RouteService,
              private userDataService: UserDataService, private router: Router) {
    this.currentSubItems = privateService.getSubmenu(route, menu);
    this.userdataSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userName = userInfo.firstName ? userInfo.firstName : '';
        this.userSurname = userInfo.lastName ? userInfo.lastName : '';
        this.hasPasswordPec = userInfo.hasPasswordPec;
        this.businessCustomer = userInfo.cluster.value === 'BUSINESS';
      }
    });

    this.serviceData.subscribe(service => {
      if (service && (service['ADSL'] || service['VOIP'])) {
        this.routerDisplay = true;
      }
    });
    this.servicesSubscription = this.services.subscribe(data => {
      const temp = Object.values(data).filter(obj => obj['serviceName'] === 'ADSL'
        || obj['serviceName'] === 'VOIP');
      if (temp.length) {
        this.aggiornaDisplay = true;
      }
    });
    router.events.subscribe((val) => {
      if (val instanceof NavigationEnd) {
        this.lastRouteUrl = val.url;
      }
    });
  }

  ngOnInit() {
    this.mobileLayoutOrder = faiDaTeMobileLayoutOrdersMap[this.router.url];
    this.router.events.subscribe(() => {
      this.mobileLayoutOrder = faiDaTeMobileLayoutOrdersMap[this.getPath()];
    });
  }

  getPath(): string {
    if (this.route.parent !== null && this.route.firstChild !== null && this.route.firstChild.firstChild != null &&
      (<BehaviorSubject<any>>this.route.firstChild.firstChild.url).getValue()[0]) {
      return `/${
        (<BehaviorSubject<any>>this.route.parent.url).getValue()[0].path}/${
        (<BehaviorSubject<any>>this.route.firstChild.url).getValue()[0].path}/${(<BehaviorSubject<any>>this.route.firstChild.firstChild.url).getValue()[0].path}`;
    }
    return this.router.url;
  }

  switchOpenClose(routeUrl: string) {
    if (this.isMobile) {
      if (this.lastRouteUrl === routeUrl) {
        this.router.navigateByUrl('/faidate');
      }
    }
  }

  open(link: string) {
    window.open(link);
  }

  openChat() {
    const element: HTMLElement = document.getElementById('submenuChatBtn') as HTMLElement;
    element.classList.add('active');
    const elementOpen: HTMLElement = document.getElementById('ChatModalId') as HTMLElement;
    const elementClose: HTMLElement = document.getElementById('chatCloseId') as HTMLElement;
    if (elementOpen) {
      elementOpen.click();
      this.activeMenu = true;
    } else {
      elementClose.click();
      this.activeMenu = false;
    }
  }

  ngOnDestroy(): void {
    if (this.userdataSubscription) {
      this.userdataSubscription.unsubscribe();
    }
  }

}
