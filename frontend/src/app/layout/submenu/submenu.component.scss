@import "~app/shared/styles/colors";
@import "~app/shared/styles/app-menu";

.recontact-button-block, .recontact-menu-button {
  //display: flex;
  //width: 25%;
  display: initial;

  .recontact-button-desktop {
    padding-bottom: 15px;
    padding-top: 15px;
    height: 100px;
    background: url("../../../assets/img/assistant/dashbutton.png") no-repeat center;
    background-size: contain;
  }

  .recontact-button-desktop img {
    height: 150px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
    // width: 50%;
  }

}
.recontact-button-mobile {
  width: 100%;
  height: 91px;
  display: inline-block;
  margin-top: 2%;
  background: url("../../../assets/img/assistant/dashbutton.png") no-repeat center;
  background-size: contain;
}

.recontact-menu-button {
  display: none;
}

@media only screen and (max-width: 1500px) {
  .buttons-group {
    .text {
      font-size: 16px;
    }

    .arrow {
      font-size: 24px;
    }

    .link {
      padding: 5px 10px 5px 5px;
    }

    .text {
      padding-left: 10px;
    }
  }

}

@media only screen and (max-width: 1300px) {
  .buttons-group {
    .text {
      padding-left: 5px;
    }

    .arrow {
      font-size: 22px;
      margin-top: 1%;
    }

    .link {
      padding: 5px;
    }
  }

  .top {
    font-size: 18px;
  }
}

@media only screen and (max-width: 1150px) {
  .buttons-group {
    .text {
      padding: 0;
      font-size: 12px;
    }

    .arrow {
      font-size: 20px;
      margin-top: 3%;
    }
  }
}

@media only screen and (max-width: 991px) {
  .recontact-menu-button {
    width: 100%;
    display: flex;

    .recontact-button {
      margin-top: 2%;
    }
  }
  .recontact-button-block {
    display: none;
  }
  .container-fluid, .profile-layout {
    padding: 0;
  }
  .buttons-group {
    .profile-mobile-layout {
      display: block;
    }

    .link {
      padding: 5px 20px 5px 15px;
    }

    .text {
      font-size: 18px;
    }

    .arrow {
      font-size: 26px;
      margin-top: 0;
      transform: rotate(90deg);
    }
  }

}
