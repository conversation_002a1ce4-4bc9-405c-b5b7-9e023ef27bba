import {NgModule} from '@angular/core';

import {HeaderComponent} from './header/header.component';
import {FooterComponent} from './footer/footer.component';
import {SharedModule} from '../shared/shared.module';
import {NavigationComponent} from './navigation/navigation.component';
import {CommonModule} from '../common/common.module';
import {SubmenuComponent} from './submenu/submenu.component';
import {ChatModule} from '../chat/chat.module';
import {InvoiceService} from '../routes/invoices/invoice.service';
import {MsgNotificationComponent} from './msgNotification/msgNotification.component';
import {MessageGlobals} from '../common/utils/globalVariables/MessageGlobals';
import {BasicMsgComponent} from './msgNotification/basicMsg/basicMsg.component';
import {ClientNotificationComponent} from './notification/client-notification.component';


@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    ChatModule
  ],
  providers: [InvoiceService, MessageGlobals],
  declarations: [
    HeaderComponent,
    FooterComponent,
    SubmenuComponent,
    NavigationComponent,
    ClientNotificationComponent,
    MsgNotificationComponent,
    BasicMsgComponent
  ],
  exports: [
    HeaderComponent,
    FooterComponent,
    SubmenuComponent,
    NavigationComponent,
    ClientNotificationComponent,
    BasicMsgComponent,
    MsgNotificationComponent
  ]
})
export class LayoutModule {
}
