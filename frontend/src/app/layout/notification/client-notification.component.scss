/deep/ mat-option {
  background-color: transparent !important;
  padding: 0 !important;
  line-height: normal !important;
}

/deep/ .mat-icon {
  width: auto !important;
  height: auto !important;
  vertical-align: middle !important;
}

.client-notification {
  position: relative;

  .notification-bell-container {
    cursor: pointer;
    position: relative;
    width: 40px;
    height: 30px;
    align-self: center;
  }

  .notification-bell {
    width: 50px;
    position: relative;
  }

  .bell-on {
    top: -5px;
  }

  .badge {
    background-color: #ff1919;
    font-size: 10px;
    position: absolute;
    top: -5px;
    left: 15px;
    border-radius: 15px;
  }
}

.notification-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background-color: #fff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
  z-index: 3000;
  display: flex;
  flex-direction: column;
  padding: 30px 40px;
  letter-spacing: 0.15px;

  // Initial state: hidden
  transform: translateX(100%);
  transition: transform 0.7s ease-in-out;

  &.open {
    transform: translateX(0);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 35px;

    .header {
      color: #707070;
      font-size: 15px;
      font-weight: bold;
    }

    .fa-times {
      position: unset;
      right: 30px;
      font-size: 40px;
      top: 0;
      color: #36749d;
      cursor: pointer;
    }
  }

  .panel-content {
    overflow-y: auto;

    .no-notifications {
      color: #367298;
      font-size: 18px;
    }

    mat-option {
      font-size: 15px;
      height: 4em;
      width: 100%;
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #e1e1e1;
      color: #367298;

      &.no-border {
        border-bottom: none !important;
      }

      .title-notification {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 5px;

        .notification-circle {
          color: #FF0100;
          font-size: 10px;
          margin-right: 8px;
        }

        div {
          flex-grow: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        img {
          width: 17px;
          transition: transform 0.5s ease-in-out;
          transform: rotate(0deg);

          &.rotated {
            transform: rotate(180deg);
          }
        }
      }
    }

    .message {
      letter-spacing: 0;
      color: #367298;
      font-size: 15px;
      text-align: left;
      border-bottom: 1px solid #e1e1e1;
      padding: 10px 0;

      // Initial state: hidden
      max-height: 0;
      overflow: hidden;
      opacity: 0;

      &.expanded {
        animation: expandMessage 1.5s ease forwards;
      }

      @keyframes expandMessage {
        0% {
          max-height: 0;
          opacity: 0;
        }
        100% {
          max-height: 500px;
          opacity: 1;
        }
      }
    }
  }
}

.modal-header-info {
  text-align: left;
  color: #36749d;
  font-size: 22px;
  margin-bottom: 20px;
  font-weight: bold;
}

.modal-text {
  text-align: left;
  color: #36749d;
  font-size: 18px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding: 60px;
  width: 680px;
}

.fa-times {
  position: absolute;
  right: 30px;
  font-size: 45px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}
