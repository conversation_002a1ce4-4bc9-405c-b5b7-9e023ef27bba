section{
  max-height: 300px;
  background: white;
  right: 0;
  width: 25.5%;
  position: fixed;
  top: 100px;
  z-index: 99;
  border: 1px solid #b6cce3;
  border-top: none;
  border-radius: 0 0 10px 10px;
}
.block{
  width: 100%;
  padding: 7px;
  border-top:1px solid #b6cce3;
  border-bottom:1px solid #b6cce3;
  background:#f0f5f9;
}
.readed{
  background: white;
}
.important{
  background: rgba(226, 81, 59, 0.44);
}
@media only screen and (max-width: 1660px) {
  section {
    top: 85px;
  }
}
@media only screen and (max-width: 1370px) {
  section {
    top: 75px;
  }
}
@media only screen and (max-width: 980px) {
  section {
  display: none;
  }
}
