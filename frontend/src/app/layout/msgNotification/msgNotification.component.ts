import {Component, Input, OnChanges, SimpleChanges} from '@angular/core';
import Msg from '../../common/model/Msg';
import {Contabile} from '../../routes/invoices/invoice.model';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';


@Component({
  selector: 'app-msg-notification',
  templateUrl: './msgNotification.component.html',
  styleUrls: ['./msgNotification.component.scss']
})
export class MsgNotificationComponent implements OnChanges {
  price: number;

  @select(['contabile', 'contabile'])
  contabile: Observable<Contabile>;
  @Input() msgs: Array<Msg> = [];

  constructor() {
    this.contabile.subscribe(contabile => {
      if (contabile && contabile.totaleScoperto) {
        this.price = contabile.totaleScoperto;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
  }
}
