import 'rxjs/add/observable/throw';
import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {NgRedux} from '@angular-redux/store';
import 'rxjs/add/operator/skip';
import {Observable} from 'rxjs/Observable';

import {ChatActions} from '../../../redux/chat/actions';

import ViewMessage from '../../model/ViewMessage';
import {ChatInitializationService} from '../../service/chat-initialization/chat-initialization.service';
import {CIChatMessageType} from '../../model/CIChatMessageType';
import ChatUser from '../../model/ChatUser';
import NoUserFoundError from '../../errors/NoUserFoundError';

@Component({
  selector: 'app-optima-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, OnDestroy {

  showChatWindow = false;

  formGroup: FormGroup;

  messages: Array<ViewMessage> = [];

  isLoading = false;

  unableWrite: boolean;

  isOperatorTyping: Observable<boolean>;

  private messageSession;

  @Input('chatUser')
  private chatUser: ChatUser;

  private chat$ = this.ngRedux.select(['chat', 'show']);

  constructor(
    private chatActions: ChatActions,
    private ngRedux: NgRedux<any>,
    private chatInitializationService: ChatInitializationService,
    private formBuilder: FormBuilder
  ) {
    this.formGroup = this.formBuilder.group({
      message: [null, Validators.required]
    });


  }

  ngOnInit() {
    this.chat$
      .skip(1) // skip first init value;
      .subscribe(
        (data: boolean) => {
          if (data) {
            this.setShowChat();
          } else {
            this.setCloseChat();
          }
        }
      );
  }

  ngOnDestroy(): void {
  }

  closeChat() {
    this.chatActions.hideChat();
    const element: HTMLElement = document.getElementById( 'submenuChatBtn') as  HTMLElement;
    element.classList.remove('active');
  }
  showChat() {
    this.chatActions.showChat();
  }

  setShowChat() {
    if (!this.chatUser) {
      throw new NoUserFoundError();
    }
    this.chatInitializationService.initChat(this.chatUser);
    this.messages = [];
    // this.isLoading = this.chatInitializationService.getLoadingStatus();
    this.chatInitializationService.getLoadingStatus().subscribe((data: any) => {
      this.isLoading = data;
      // debugger
      // console.log(data);
    });
    this.messageSession = this.chatInitializationService.messages().subscribe((message) => {
      if (message) {
        this.messages.push(message);
        this.unableWrite = message.message === 'Ciao! Stiamo per metterti in contatto con il primo operatore disponibile.';
      }
    });
    this.isOperatorTyping = this.chatInitializationService.isOperatorTyping();
    this.showChatWindow = true;
  }
  setCloseChat() {
    this.messages = [];
    this.chatInitializationService.closeChat();
    this.messageSession.unsubscribe();
    this.showChatWindow = false;
  }

  sendMessage() {
    if (this.formGroup.valid) {
      this.chatInitializationService.sendTextMessage(this.formGroup.value.message);
      this.formGroup.reset();
    }
  }

  userIsNotTyping() {
    this.chatInitializationService.changeUserTypingStatus(false);
  }

  onTypeMessage($event) {
    if ($event.keyCode === 13) {
      this.sendMessage();
    }
    if (!this.chatInitializationService.isUserTyping()) {
      this.chatInitializationService.changeUserTypingStatus(true);
    }
  }

  isCustomer(message: ViewMessage): boolean {
    return message.type === CIChatMessageType.CHAT_MESSAGE_FROM_CUSTOMER;
  }


}
