import { Injectable } from '@angular/core';
import ViewMessage from '../../model/ViewMessage';
import ChatMessage from '../../model/response/ChatMessage';
import { CIChatMessageType } from '../../model/CIChatMessageType';
import * as moment from 'moment';

const messageTypeToSenderMap = {
  [CIChatMessageType.CHAT_MESSAGE_FROM_CUSTOMER]: 'Tu',
  [CIChatMessageType.CHAT_MESSAGE_FROM_AGENT]: 'Operatore',
  [CIChatMessageType.COMFORT_MESSAGE]: 'Sistema',
  [CIChatMessageType.SESSION_DISCONNECTED_BY_AGENT]: 'Optima ChatLive',
};

@Injectable()
export class MessageService {

  constructor() {
  }

  static systemMessage(message: string): ViewMessage {
    const systemMessage = new ViewMessage();
    systemMessage.sender = 'Optima chatlive';
    systemMessage.message = message;
    systemMessage.time = MessageService.formatDate(new Date().getTime());
    return systemMessage;
  }

  static failedSystemMessage(): ViewMessage {
    const systemMessage = new ViewMessage();
    systemMessage.sender = 'Optima ChatLive';
    systemMessage.message = 'Al momento non ci sono operatori disponibili.\n' +
      'Ti ricordiamo che il servizio di assistenza chat è disponibile dal lunedì al venerdì dalle 09:00 alle 18:30. \n' +
      'Puoi parlare con Alan il nostro assistente virtuale';
    systemMessage.time = MessageService.formatDate(new Date().getTime());
    return systemMessage;
  }

  static viewMessage(message: ChatMessage): ViewMessage {
    if (message) {
      const viewMessage = new ViewMessage();
      viewMessage.message = message.chatMessage;
      viewMessage.sender = messageTypeToSenderMap[message.chatMessageType];
      viewMessage.type = message.chatMessageType;
      viewMessage.time = MessageService.formatDate(message.writeTime.milliseconds);
      return viewMessage;
    }
    return null;
  }

  static formatDate(date: number) {
    return moment(date).format('HH:mm:ss');
  }

}
