import 'rxjs/add/observable/of';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import ChatSkillSet from '../../model/response/ChatSkillset';
import CIDateTime from '../../model/response/CIDateTime';
import { ChatService } from '../chat/chat.service';
import MultipleChatMessage from '../../model/response/MultipleChatMessage';
import { SetupTextChatResponse } from '../../model/response/SetupTextChatResponse';
import { MessageService } from '../message/message.service';
import ViewMessage from '../../model/ViewMessage';
import Message from '../../model/request/Message';
import { CIChatMessageType } from '../../model/CIChatMessageType';
import { ChatSession } from '../../model/ChatSession';
import ChatUser from '../../model/ChatUser';

@Injectable()
export class ChatInitializationService {


  private chatSession: ChatSession;

  private chatSessionSubscription;

  private userLastMessageSubscription;

  constructor(private chatService: ChatService) {
  }

  initChat(user: ChatUser) {
    this.chatSession = new ChatSession();
    this.chatSession.isLoading.next(true);
    this.userLastMessageSubscription = this.chatSession.userLastMessage.flatMap(message => {
      return this.chatService.sendChatMessage(message)
        .flatMap(() => {
          return this.chatService.getHistory(this.chatSession.sessionKey, this.chatSession.lastReadTime.getValue());
        });
    }).flatMap(this.handleChatHistory).subscribe();

    this.chatSessionSubscription = this.chatService.getAnonymousSessionKey()
      .flatMap(this.updateAnonymousCustomerId(user))
      .flatMap(this.readSkillSetByCustomerId)
      .flatMap(this.skillSetReadingResponseProcessor)
      .flatMap(this.setupTextChat)
      .flatMap(this.processSetupChatStatus)
      .flatMap(this.getOnHoldComfortMessages)
      .flatMap(() => {
        this.chatSession.lastReadTime.next(new Date().getTime());
        return this.chatService.getHistory(this.chatSession.sessionKey, this.chatSession.lastReadTime.getValue());
      })
      .flatMap(() => Observable.timer(0, 5000))
      .flatMap(() => this.chatService.updateAliveTime(this.chatSession.sessionKey, this.chatSession.isUserTyping.getValue()))
      .flatMap(this.updateAliveTimeHandler)
      .flatMap(() => {
        return this.chatService.getHistory(this.chatSession.sessionKey, this.chatSession.lastReadTime.getValue());
      })
      .flatMap(this.handleChatHistory)
      .subscribe(
        () => {
        },
        err => {
          if (!this.chatSession.agentAccepted.getValue()) {
            this.chatSession.isLoading.next(false);
            this.chatSession.message.next(MessageService.failedSystemMessage());
          }
        },
      );

    return this.chatSessionSubscription;
  }

  getLoadingStatus(): Observable<boolean> {
    return this.chatSession.isLoading.asObservable();
  }

  messages(): Observable<ViewMessage> {
    return this.chatSession.message.asObservable();
  }

  changeUserTypingStatus(status: boolean) {
    if (this.chatSession) {
      this.chatSession.isUserTyping.next(status);
    }
  }

  isUserTyping(): boolean {
    if (this.chatSession) {
      return this.chatSession.isUserTyping.getValue();
    }
  }

  isOperatorTyping(): Observable<boolean> {
    return this.chatSession.isOperatorTyping.asObservable();
  }

  private updateAnonymousCustomerId = (user: ChatUser) => (sessionKey): Observable<object> => {
    this.chatSession.sessionKey = sessionKey;
    return this.chatService.getAndUpdateAnonymousCustomerID(this.chatSession.sessionKey, user);
  }

  private readSkillSetByCustomerId = (customerId): Observable<ChatSkillSet> => {
    this.chatSession.contactId = customerId['getAndUpdateAnonymousCustomerIDResult'];
    return this.chatService.readSkillSetByName(this.chatSession.sessionKey);
  }

  private setupTextChat = (skillSet: ChatSkillSet) => {
    return this.chatService.setupTextChat(this.chatSession.sessionKey, skillSet);
  }

  processSetupChatStatus = (setupResponse: SetupTextChatResponse) => {
    if (!setupResponse || !setupResponse.requestTextChatResult || setupResponse.requestTextChatResult === -1) {
      this.chatSession.message.next(MessageService.failedSystemMessage());
      return Observable.throw('Please try again later...');
    }
    this.chatSession.contactId = setupResponse.requestTextChatResult;
    return this.chatService.updateAliveTime(this.chatSession.sessionKey, false);
  }

  handleChatHistory = (messages: MultipleChatMessage) => {
    const {agentAccepted, isLoading, lastReadTime, isOperatorTyping} = this.chatSession;
    if (!agentAccepted.getValue() && messages.listOfChatMessages) {
      agentAccepted.next(true);
      isLoading.next(false);
    }
    isOperatorTyping.next(messages.isWriting);
    lastReadTime.next(messages.lastReadTime.milliseconds);
    if (messages.listOfChatMessages && messages.listOfChatMessages.cichatMessageReadType) {
      messages.listOfChatMessages.cichatMessageReadType.forEach((message) => {
        this.chatSession.message.next(MessageService.viewMessage(message));
        if (message.chatMessageType === CIChatMessageType.SESSION_DISCONNECTED_BY_AGENT) {
          this.closeChat();
        }
      });
    }
    return Observable.of(messages);
  }

  getOnHoldComfortMessages = () => {
    return this.chatService.getOnHoldComfortMessages(this.chatSession.sessionKey, this.chatSession.contactId);  // if (heartbeatTime < 0) && (AgentAccepted  == false)
  }

  skillSetReadingResponseProcessor = (skillSet: ChatSkillSet) => {
    this.chatSession.skillSet = skillSet;
    if (skillSet.tag !== null && skillSet.tag !== '') {
      return this.chatService.getWebOnHoldURLs(this.chatSession.sessionKey, skillSet.tag).flatMap((response) => {
        // TODO figure out what to do with response
        return Observable.of(skillSet);
      });
    }
    return Observable.of(skillSet);
  }

  updateAliveTimeHandler = (dateTime: CIDateTime) => {
    if (dateTime.milliseconds < 0 && !this.chatSession.agentAccepted.getValue()) {
      this.chatSession.isLoading.next(false);
      if (!this.chatSession.message.getValue()) {
        this.chatSession.message.next(MessageService.systemMessage('Ciao siamo qui per aiutarti, stiamo per metterti in contatto con un nostro esperto '));
      }
      // TODO check if chat using getOnHoldComfortMessages
      // return this.getOnHoldComfortMessages().flatMap((response) => {
      //   response.forEach((message) => {
      //     this.message.next(MessageService.systemMessage(message.message));
      //   });
      //   return Observable.of(dateTime);
      // });
    }
    return Observable.of(dateTime);
  }

  closeChat() {
    if (this.chatSession && this.chatSession.sessionKey && this.chatSession.sessionKey.sessionKey) {
      const {agentAccepted, sessionKey, contactId} = this.chatSession;
      this.chatSessionSubscription.unsubscribe();
      this.userLastMessageSubscription.unsubscribe();

      if (!agentAccepted.getValue()) {
        this.chatService.removeChatContactFromWaitingQueue(sessionKey, '', contactId).subscribe();
      }

      if (agentAccepted.getValue() && this.chatSession.contactId > 0) {
        const textMessage = new Message();
        textMessage.contactID = contactId;
        textMessage.sessionKey = sessionKey.sessionKey;
        textMessage.chatMessageType = CIChatMessageType.SESSION_DISCONNECTED_BY_CUSTOMER;
        textMessage.message = 'Session Disconnected';
        this.chatService.sendChatMessage(textMessage)
          .flatMap(() => this.chatService.getCustomerInfo(sessionKey, contactId))
          .flatMap((customerInfo) => this.chatService.logoff(sessionKey, contactId, customerInfo.username))
          .subscribe();
      }
      this.chatSession = null;
    }
  }

  sendTextMessage(message: string) {
    const textMessage = new Message();
    textMessage.contactID = this.chatSession.contactId;
    textMessage.sessionKey = this.chatSession.sessionKey.sessionKey;
    textMessage.chatMessageType = CIChatMessageType['CHAT_MESSAGE_FROM_CUSTOMER'];
    textMessage.message = message;
    this.chatSession.userLastMessage.next(textMessage);
  }

}
