import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { Subject } from 'rxjs/Subject';
import SessionKey from './response/SessionKey';
import ChatSkillSet from './response/ChatSkillset';
import ViewMessage from './ViewMessage';
import Message from './request/Message';

export class ChatSession {

  isLoading = new BehaviorSubject(false);

  sessionKey: SessionKey;

  skillSet: ChatSkillSet;

  contactId: number;

  aliveTime = new BehaviorSubject(0);

  lastReadTime = new BehaviorSubject(0);

  message = new BehaviorSubject<ViewMessage>(null);

  agentAccepted = new BehaviorSubject(false);

  userLastMessage = new Subject<Message>();

  isUserTyping = new BehaviorSubject(false);

  isOperatorTyping = new BehaviorSubject(false);
}
