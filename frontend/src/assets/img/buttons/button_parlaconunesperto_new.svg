<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.0.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Livello_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 117.1 113.1" style="enable-background:new 0 0 117.1 113.1;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);fill:url(#SVGID_3_);}
	.st1{clip-path:url(#SVGID_5_);fill:url(#SVGID_6_);}
	.st2{fill:#36749C;}
	.st3{fill:#B4CBE1;}
	.st4{fill:#FFFFFF;}
	.st5{filter:url(#Adobe_OpacityMaskFilter);}
	.st6{clip-path:url(#SVGID_8_);fill:url(#SVGID_10_);}
	.st7{clip-path:url(#SVGID_8_);mask:url(#SVGID_9_);fill:url(#SVGID_11_);}
</style>
<g>
	<g>
		<defs>
			<path id="SVGID_1_" d="M17.3,63.4c0,23,18.7,41.7,41.7,41.7c23,0,41.7-18.7,41.7-41.7c0-23-18.7-41.7-41.7-41.7
				C36,21.7,17.3,40.4,17.3,63.4"/>
		</defs>
		<clipPath id="SVGID_2_">
			<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
		</clipPath>
		
			<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-237.9876" y1="623.6686" x2="-236.9876" y2="623.6686" gradientTransform="matrix(0 -83.3735 -83.3735 0 52056.3672 -19736.8145)">
			<stop  offset="0" style="stop-color:#176391"/>
			<stop  offset="5.543620e-03" style="stop-color:#176391"/>
			<stop  offset="0.7366" style="stop-color:#36749C"/>
			<stop  offset="1" style="stop-color:#36749C"/>
		</linearGradient>
		<rect x="17.3" y="21.7" class="st0" width="83.4" height="83.4"/>
	</g>
	<g>
		<defs>
			<path id="SVGID_4_" d="M19.5,63.4c0-21.8,17.7-39.5,39.5-39.5s39.5,17.7,39.5,39.5s-17.7,39.5-39.5,39.5S19.5,85.1,19.5,63.4
				 M17.3,63.4c0,23,18.7,41.7,41.7,41.7s41.7-18.7,41.7-41.7S81.9,21.7,58.9,21.7S17.3,40.4,17.3,63.4"/>
		</defs>
		<clipPath id="SVGID_5_">
			<use xlink:href="#SVGID_4_"  style="overflow:visible;"/>
		</clipPath>
		
			<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-237.9876" y1="623.6686" x2="-236.9876" y2="623.6686" gradientTransform="matrix(0 -83.3731 -83.3731 0 52056.1055 -19736.7148)">
			<stop  offset="0" style="stop-color:#36749C"/>
			<stop  offset="0.2634" style="stop-color:#36749C"/>
			<stop  offset="0.9945" style="stop-color:#176391"/>
			<stop  offset="1" style="stop-color:#176391"/>
		</linearGradient>
		<rect x="17.3" y="21.7" class="st1" width="83.4" height="83.4"/>
	</g>
	<g>
		<path class="st2" d="M7.4,40.7c0.2-0.5,0.5-0.9,0.7-1.2C8.4,39.2,8.7,39,9,38.9s0.6-0.2,1-0.2c0.3,0,0.7,0.1,1,0.2
			c0.3,0.2,0.6,0.4,0.9,0.6c0.2,0.2,0.4,0.5,0.5,0.8c0.1,0.3,0.1,0.7,0.1,1.1c0,0.4-0.2,0.8-0.4,1.3l-0.4,0.9l2.5,1.1l-0.6,1.5
			l-7.1-3.1L7.4,40.7z M10.9,42.3c0.2-0.5,0.2-0.8,0.1-1.2c-0.1-0.3-0.4-0.6-0.8-0.7c-0.2-0.1-0.3-0.1-0.5-0.1c-0.2,0-0.3,0-0.5,0.1
			c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.2,0.3-0.3,0.6l-0.4,0.9l2.4,1.1L10.9,42.3z"/>
	</g>
	<g>
		<path class="st2" d="M19.4,35l-0.8,1.2c-0.1,0.2-0.3,0.3-0.6,0.3l-2.8,0c-0.1,0-0.2,0-0.3,0.1c-0.1,0-0.1,0.1-0.2,0.2l-0.3,0.5
			L17,39l-0.8,1.4l-6.6-4l1.3-2.1c0.3-0.5,0.6-0.8,0.9-1.1c0.3-0.3,0.6-0.5,0.9-0.6c0.3-0.1,0.6-0.1,0.9-0.1c0.3,0,0.6,0.2,0.9,0.3
			c0.2,0.1,0.4,0.3,0.6,0.5c0.2,0.2,0.3,0.4,0.4,0.6c0.1,0.2,0.1,0.5,0.2,0.7c0,0.2,0,0.5-0.1,0.8c0.1-0.1,0.2-0.1,0.3-0.1
			c0.1,0,0.2-0.1,0.4-0.1L19.4,35z M13.9,36.1c0.1-0.2,0.2-0.4,0.3-0.6c0-0.2,0-0.4,0-0.5c0-0.2-0.1-0.3-0.2-0.4
			c-0.1-0.1-0.2-0.2-0.4-0.3c-0.3-0.2-0.6-0.2-0.9-0.1c-0.3,0.1-0.6,0.4-0.8,0.8l-0.4,0.7l2.1,1.3L13.9,36.1z"/>
	</g>
	<g>
		<path class="st2" d="M15.7,29.3l1.6,1.3l1.6-2l0.9,0.8l-1.6,2l1.6,1.3l2.1-2.6l1,0.8l-3.1,3.9l-6.1-4.9l3.1-3.9l1,0.8L15.7,29.3z"
			/>
	</g>
	<g>
		<path class="st2" d="M23,19.8l5.3,5.7l-0.6,0.6c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0-0.2,0-0.3,0l-6.2-1c0.1,0.1,0.2,0.2,0.3,0.3
			c0.1,0.1,0.2,0.2,0.2,0.2l3.1,3.3l-1,1l-5.3-5.7l0.6-0.6c0,0,0.1-0.1,0.1-0.1c0,0,0.1,0,0.1-0.1c0,0,0.1,0,0.1,0c0,0,0.1,0,0.2,0
			l6.2,1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3l-3-3.3L23,19.8z"/>
	</g>
	<g>
		<path class="st2" d="M33.5,17.3c0.3,0.5,0.5,1,0.6,1.5c0.1,0.5,0.1,1,0,1.5c-0.1,0.5-0.3,0.9-0.6,1.4c-0.3,0.4-0.7,0.8-1.2,1.1
			c-0.5,0.3-1,0.5-1.5,0.6c-0.5,0.1-1,0.1-1.5,0c-0.5-0.1-0.9-0.3-1.4-0.6c-0.4-0.3-0.8-0.7-1.1-1.2c-0.3-0.5-0.5-1-0.6-1.5
			c-0.1-0.5-0.1-1,0-1.5s0.3-1,0.6-1.4s0.7-0.8,1.2-1.1c0.5-0.3,1-0.5,1.5-0.6c0.5-0.1,1-0.1,1.5,0c0.5,0.1,0.9,0.3,1.4,0.6
			C32.9,16.5,33.2,16.8,33.5,17.3z M32.2,18.2c-0.2-0.3-0.5-0.6-0.7-0.8c-0.3-0.2-0.5-0.4-0.8-0.4c-0.3-0.1-0.6-0.1-0.9,0
			c-0.3,0.1-0.6,0.2-0.9,0.4c-0.3,0.2-0.5,0.4-0.7,0.7c-0.2,0.3-0.3,0.5-0.3,0.8s0,0.6,0.1,0.9c0.1,0.3,0.2,0.7,0.5,1
			c0.2,0.3,0.5,0.6,0.7,0.8c0.3,0.2,0.5,0.4,0.8,0.4c0.3,0.1,0.6,0.1,0.9,0c0.3-0.1,0.6-0.2,0.9-0.4c0.3-0.2,0.5-0.4,0.7-0.7
			c0.2-0.3,0.3-0.5,0.3-0.8c0-0.3,0-0.6-0.1-0.9C32.5,18.9,32.4,18.6,32.2,18.2z"/>
	</g>
	<g>
		<path class="st2" d="M38.6,12.1l-2.1,1l2.8,5.9l-1.5,0.7L35,13.8l-2.1,1l-0.6-1.2L38,11L38.6,12.1z"/>
	</g>
	<g>
		<path class="st2" d="M47.8,15.9l-1.2,0.4c-0.1,0-0.3,0-0.4,0s-0.2-0.1-0.3-0.2l-1-1.3l-2.9,0.9l0,1.6c0,0.1,0,0.2-0.1,0.3
			c-0.1,0.1-0.2,0.2-0.3,0.2l-1.2,0.4L41,10l1.6-0.5L47.8,15.9z M44.3,14l-1.5-2c-0.1-0.1-0.2-0.2-0.3-0.4c-0.1-0.1-0.2-0.3-0.3-0.5
			c0,0.2,0,0.4,0,0.6c0,0.2,0,0.3,0,0.5l-0.1,2.5L44.3,14z"/>
	</g>
	<g>
		<path class="st2" d="M54.8,13.6l0.2,1.3l-4.6,0.6l-1.1-7.7l1.6-0.2l0.9,6.4L54.8,13.6z"/>
	</g>
	<g>
		<path class="st2" d="M62.5,14.8l-1.2,0c-0.1,0-0.3,0-0.3-0.1c-0.1-0.1-0.2-0.1-0.2-0.3L60.2,13l-3.1,0l-0.5,1.5
			c0,0.1-0.1,0.2-0.2,0.2s-0.2,0.1-0.3,0.1l-1.3,0l3-7.8l1.6,0L62.5,14.8z M59.8,11.8l-0.9-2.4c-0.1-0.1-0.1-0.3-0.2-0.4
			c-0.1-0.2-0.1-0.4-0.2-0.5c0,0.2-0.1,0.4-0.2,0.6c-0.1,0.2-0.1,0.3-0.2,0.4l-0.8,2.4L59.8,11.8z"/>
	</g>
	<g>
		<path class="st2" d="M70.6,14.2c0.1,0,0.2,0.1,0.2,0.1l0.5,0.8c-0.4,0.3-0.8,0.6-1.3,0.7c-0.5,0.1-1.1,0.1-1.7,0
			c-0.6-0.1-1.1-0.3-1.5-0.6c-0.4-0.3-0.8-0.6-1-1c-0.3-0.4-0.4-0.9-0.5-1.4c-0.1-0.5-0.1-1,0-1.6c0.1-0.4,0.2-0.7,0.3-1.1
			c0.1-0.3,0.3-0.6,0.5-0.9c0.2-0.3,0.5-0.5,0.7-0.7c0.3-0.2,0.6-0.3,0.9-0.5c0.3-0.1,0.6-0.2,1-0.2c0.3,0,0.7,0,1.1,0.1
			c0.3,0.1,0.5,0.1,0.8,0.2c0.2,0.1,0.5,0.2,0.7,0.3c0.2,0.1,0.4,0.3,0.5,0.4c0.2,0.2,0.3,0.3,0.4,0.5l-0.7,0.6c0,0-0.1,0.1-0.1,0.1
			c-0.1,0-0.1,0-0.2,0s-0.2-0.1-0.2-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.3-0.2-0.4-0.3c-0.2-0.1-0.4-0.2-0.7-0.2
			c-0.3-0.1-0.6-0.1-0.9,0s-0.5,0.2-0.8,0.4c-0.2,0.2-0.4,0.4-0.6,0.7c-0.2,0.3-0.3,0.7-0.4,1.1c-0.1,0.4-0.1,0.8,0,1.1
			c0.1,0.3,0.2,0.6,0.3,0.9s0.4,0.5,0.6,0.6c0.2,0.2,0.5,0.3,0.8,0.3c0.2,0,0.3,0.1,0.5,0.1s0.3,0,0.4,0c0.1,0,0.3-0.1,0.4-0.1
			c0.1,0,0.2-0.1,0.4-0.2c0,0,0.1,0,0.1-0.1C70.5,14.2,70.6,14.2,70.6,14.2z"/>
	</g>
	<g>
		<path class="st2" d="M80.6,11.3L78,18.6l-1.5-0.5l1.1-3.2l-3.2-1.1l-1.1,3.2l-1.5-0.5l2.6-7.3l1.5,0.5l-1.1,3.1l3.2,1.1l1.1-3.1
			L80.6,11.3z"/>
	</g>
	<g>
		<path class="st2" d="M80.7,20l-1.5-0.7l3.4-7l1.5,0.7L80.7,20z"/>
	</g>
	<g>
		<path class="st2" d="M87.9,24.2l-1.1-0.7c-0.1-0.1-0.2-0.2-0.2-0.3c0-0.1-0.1-0.2,0-0.3l0.3-1.5l-2.6-1.6l-1.2,1
			c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1l-1.1-0.7l6.7-5l1.4,0.9L87.9,24.2z M87.1,20.2l0.5-2.5c0-0.1,0.1-0.3,0.1-0.5
			s0.1-0.4,0.2-0.5c-0.1,0.1-0.3,0.3-0.4,0.4c-0.1,0.1-0.3,0.2-0.4,0.3l-2,1.6L87.1,20.2z"/>
	</g>
	<g>
		<path class="st2" d="M99.8,24.3l-5.2,5.8l-1.1-0.9l3.2-3.6c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.1,0.2-0.2,0.3-0.2l-4.5,1.7
			c-0.2,0.1-0.5,0.1-0.7-0.1l-0.2-0.2c-0.1-0.1-0.2-0.2-0.2-0.3c0-0.1,0-0.2,0-0.3l1.2-4.6c-0.1,0.1-0.2,0.2-0.2,0.3
			c-0.1,0.1-0.1,0.2-0.2,0.2l-3.2,3.6l-1.1-0.9l5.2-5.8l0.9,0.8c0.1,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1c0,0,0,0.1,0,0.1
			c0,0,0,0.1,0,0.2l-1.2,4.5c0,0.1-0.1,0.3-0.1,0.4c0,0.1-0.1,0.3-0.1,0.4c0.1-0.1,0.3-0.1,0.4-0.2s0.3-0.1,0.4-0.2l4.4-1.6
			c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0.1c0,0,0.1,0.1,0.1,0.1L99.8,24.3z"/>
	</g>
	<g>
		<path class="st2" d="M99.6,36.5l-0.8-1c-0.1-0.1-0.1-0.2-0.1-0.3c0-0.1,0-0.2,0.1-0.3l0.8-1.3l-1.9-2.4l-1.5,0.5
			c-0.1,0-0.2,0-0.3,0s-0.2-0.1-0.3-0.2l-0.8-1l8-2.4l1,1.3L99.6,36.5z M100.3,32.6l1.4-2.1c0.1-0.1,0.2-0.2,0.2-0.4
			c0.1-0.1,0.2-0.3,0.3-0.5c-0.2,0.1-0.4,0.2-0.5,0.2c-0.2,0.1-0.3,0.1-0.4,0.1l-2.4,0.8L100.3,32.6z"/>
	</g>
	<g>
		<path class="st2" d="M107.8,37.8l-1.2-2l-5.6,3.3l-0.8-1.4l5.6-3.3l-1.2-2l1.1-0.7l3.2,5.3L107.8,37.8z"/>
	</g>
	<g>
		<path class="st2" d="M104.9,47.3l-0.5-1.1c-0.1-0.1-0.1-0.2,0-0.4c0-0.1,0.1-0.2,0.2-0.3l1.2-1.1l-1.2-2.8l-1.6,0.1
			c-0.1,0-0.2,0-0.3-0.1s-0.2-0.1-0.2-0.3l-0.5-1.2l8.3-0.3l0.7,1.5L104.9,47.3z M106.5,43.7l1.9-1.7c0.1-0.1,0.2-0.2,0.3-0.3
			c0.1-0.1,0.3-0.2,0.4-0.4c-0.2,0-0.4,0.1-0.6,0.1c-0.2,0-0.3,0-0.5,0l-2.5,0.2L106.5,43.7z"/>
	</g>
	<g>
		<path class="st3" d="M73,52.3H61.5l4.3-4.3c0.3-0.3,0.3-0.7,0-1c-0.3-0.3-0.7-0.3-1,0l-5.5,5.5c-0.3,0.3-0.3,0.7,0,1l5.5,5.5
			c0.1,0.1,0.3,0.2,0.5,0.2c0.2,0,0.4-0.1,0.5-0.2c0.3-0.3,0.3-0.7,0-1l-4.3-4.3H73c2.7,0,4.8,2.1,4.8,4.8c0,2.7-2.1,4.8-4.8,4.8
			c-0.4,0-0.7,0.3-0.7,0.7c0,0.4,0.3,0.7,0.7,0.7c3.5,0,6.2-2.7,6.2-6.2C79.2,55,76.5,52.3,73,52.3"/>
		<path class="st4" d="M42.2,46c-0.5,0.7-0.8,1.5-1,2.2c-2,6.2-0.1,12.7,6.4,22.3c9,13.2,18.1,14.1,18.5,14.1c0.8,0.1,1.6,0,2.4-0.1
			c0.5-0.1,2.7-1.2,4.6-2.2c1.1-0.8,1.4-2.2,0.7-3.2l-5-7.2c-0.7-1-2.2-1.3-3.3-0.5l-4.2,2.9l-0.5-0.4c-0.2-0.1-0.4-0.3-0.6-0.5
			c-1.8-1.5-4.2-3.5-6.6-6.9c-2.3-3.4-3.3-6.4-4.1-8.6l-0.4-1.3l4.2-2.9c0.5-0.4,0.9-0.9,1-1.6c0.1-0.6,0-1.2-0.3-1.7l-5-7.3
			c-0.7-1-2.1-1.3-3.2-0.6C44.3,43.9,42.5,45.5,42.2,46"/>
		<path class="st3" d="M66.7,86.4c-0.3,0-0.5,0-0.8,0c-0.4,0-10.3-1-19.8-14.9c-6.9-10.1-8.8-17-6.7-23.8c0.3-0.9,0.7-1.7,1.2-2.6
			c0.6-0.9,3.5-3.4,4.1-3.9l0.1-0.1c1.9-1.3,4.5-0.8,5.7,1l5,7.2c0.6,0.9,0.8,1.9,0.6,3c-0.2,1.1-0.8,2.1-1.8,2.7l-3.1,2.1
			c0,0,0,0,0,0c0.7,2.1,1.7,5,3.8,8.2c2.2,3.2,4.5,5.1,6.2,6.6c0,0,0,0,0,0l3.1-2.1c1.9-1.3,4.5-0.8,5.7,1l5,7.2
			c1.3,1.8,0.7,4.4-1.2,5.7l-0.1,0c-0.7,0.4-4.1,2.2-5.1,2.4C68.1,86.3,67.4,86.4,66.7,86.4 M45.9,42.5c-1.6,1.3-3.4,3-3.7,3.4
			c-0.5,0.7-0.8,1.5-1,2.2c-2,6.2-0.1,12.7,6.4,22.3c9,13.2,18.1,14.1,18.5,14.1c0.8,0.1,1.6,0,2.4-0.1c0.5-0.1,2.7-1.2,4.6-2.2
			c1.1-0.8,1.4-2.2,0.7-3.2l-5-7.2c-0.7-1-2.2-1.3-3.3-0.5l-4.2,2.9l-0.5-0.4c-0.2-0.1-0.4-0.3-0.6-0.5c-1.8-1.5-4.2-3.5-6.6-6.9
			c-2.3-3.4-3.3-6.4-4.1-8.6l-0.4-1.3l4.2-2.9c0.5-0.4,0.9-0.9,1-1.6c0.1-0.6,0-1.2-0.3-1.7l-5-7.3C48.5,42.1,47,41.8,45.9,42.5"/>
	</g>
	<g>
		<defs>
			<path id="SVGID_7_" d="M21.8,63.4c0-20.5,16.7-37.2,37.2-37.2c20.5,0,37.2,16.7,37.2,37.2s-16.7,37.2-37.2,37.2
				C38.4,100.5,21.8,83.9,21.8,63.4 M19.5,63.4c0,21.8,17.7,39.5,39.5,39.5s39.5-17.7,39.5-39.5c0-21.8-17.7-39.5-39.5-39.5
				S19.5,41.6,19.5,63.4"/>
		</defs>
		<clipPath id="SVGID_8_">
			<use xlink:href="#SVGID_7_"  style="overflow:visible;"/>
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="19.5" y="23.9" width="78.9" height="78.9">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="19.5" y="23.9" width="78.9" height="78.9" id="SVGID_9_">
			<g class="st5">
				
					<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-237.9869" y1="623.6686" x2="-236.9869" y2="623.6686" gradientTransform="matrix(0 -78.9061 -78.9061 0 49270.1992 -18675.8125)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1.560910e-02" style="stop-color:#EEEEEE"/>
					<stop  offset="7.192684e-02" style="stop-color:#B7B7B7"/>
					<stop  offset="0.1301" style="stop-color:#868686"/>
					<stop  offset="0.189" style="stop-color:#5D5D5D"/>
					<stop  offset="0.2487" style="stop-color:#3B3B3B"/>
					<stop  offset="0.3094" style="stop-color:#212121"/>
					<stop  offset="0.3716" style="stop-color:#0F0F0F"/>
					<stop  offset="0.4363" style="stop-color:#040404"/>
					<stop  offset="0.5073" style="stop-color:#000000"/>
					<stop  offset="0.5594" style="stop-color:#050505"/>
					<stop  offset="0.6203" style="stop-color:#131313"/>
					<stop  offset="0.6857" style="stop-color:#2B2B2B"/>
					<stop  offset="0.7543" style="stop-color:#4C4C4C"/>
					<stop  offset="0.8254" style="stop-color:#767676"/>
					<stop  offset="0.8985" style="stop-color:#AAAAAA"/>
					<stop  offset="0.972" style="stop-color:#E6E6E6"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<rect x="19.5" y="23.9" class="st6" width="78.9" height="78.9"/>
			</g>
		</mask>
		
			<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="-237.9869" y1="623.6686" x2="-236.9869" y2="623.6686" gradientTransform="matrix(0 -78.9061 -78.9061 0 49270.1992 -18675.8125)">
			<stop  offset="0" style="stop-color:#B4CBE1"/>
			<stop  offset="0.1251" style="stop-color:#A8C2D8"/>
			<stop  offset="0.3071" style="stop-color:#9DBBD0"/>
			<stop  offset="0.5073" style="stop-color:#99B8CD"/>
			<stop  offset="0.6936" style="stop-color:#9EBBD1"/>
			<stop  offset="0.9089" style="stop-color:#ACC5DB"/>
			<stop  offset="1" style="stop-color:#B4CBE1"/>
		</linearGradient>
		<rect x="19.5" y="23.9" class="st7" width="78.9" height="78.9"/>
	</g>
</g>
</svg>
