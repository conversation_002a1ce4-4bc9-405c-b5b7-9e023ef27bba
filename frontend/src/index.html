<!doctype html>
<html lang="en">

<head>
  <script type="text/javascript">
    var _iub = _iub || [];
    var currentPath = window.location.pathname;
    if (!currentPath.includes('optima-young')) {
      _iub.csConfiguration = {
        "askConsentAtCookiePolicyUpdate": true,
        "cookiePolicyInOtherWindow": true,
        "countryDetection": true,
        "enableLgpd": true,
        "enableUspr": true,
        "floatingPreferencesButtonColor": "#F2F3F7",
        "floatingPreferencesButtonDisplay": "anchored-center-right",
        "lgpdAppliesGlobally": false,
        "perPurposeConsent": true,
        "siteId": 3254151,
        "cookiePolicyId": 93523417,
        "lang": "it",
        "banner": {
          "acceptButtonColor": "#98C23A",
          "acceptButtonDisplay": true,
          "backgroundColor": "#F2F3F7",
          "closeButtonDisplay": false,
          "customizeButtonCaptionColor": "#333333",
          "customizeButtonColor": "#C6C6C6",
          "customizeButtonDisplay": true,
          "explicitWithdrawal": true,
          "fontSizeBody": "12px",
          "listPurposes": true,
          "linksColor": "#333333",
          "position": "float-bottom-right",
          "rejectButtonColor": "#98C23A",
          "rejectButtonDisplay": true,
          "slideDown": false,
          "textColor": "#333333"
        }
      };
    }</script>
  <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3254151.js"></script>
  <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
  <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>
  <meta charset="utf-8">

  <title id="page-title">Area Clienti | Optima Italia</title>
  <meta name="description" content="Effettua il login all'area clienti Optima per gestire i tuoi servizi. Non hai un account? Effettua la tua registrazione oggi stesso per scoprire tutti i vantaggi!">
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.location.pathname === '/prospect/registration-status') {
        document.getElementById('page-title').textContent = 'Optima Italia - Stato attivazione';
      } else {
        document.getElementById('page-title').textContent = 'Area Clienti | Optima Italia';
      }
    });
  </script>

  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <script src='https://www.google.com/recaptcha/api.js?hl=it'></script>
  <link rel="icon" type="image/svg+xml" href="favicon1.ico"/>
  <link rel="canonical" href="https://areaclienti.optimaitalia.com/" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <script src="./assets/scripts/browser-detect.js"></script>
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=**********-10"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '**********-10');
  </script>



  <!-- Google Tag Manager -->
 <!--
  <script>(function (w, d, s, l, i) {
    w[l] = w[l] || [];
    w[l].push({
      'gtm.start':
        new Date().getTime(), event: 'gtm.js'
    });
    var f = d.getElementsByTagName(s)[0],
      j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
    j.async = true;
    j.src =
      'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
    f.parentNode.insertBefore(j, f);
  })(window, document, 'script', 'dataLayer', 'GTM-WMLKGHN');</script>
  -->
  <!-- End Google Tag Manager -->
</head>

<body>
<app-root></app-root>

<!--<noscript>
  <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WMLKGHN"
          height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>-->
<!-- End Google Tag Manager (noscript) -->
</body>

</html>
