{"name": "ng2angle", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "install": "npm run napa", "start": "yarn && ng serve --proxy-config proxy.conf.json", "build": "yarn && ng build --prod", "test": "yarn && ng test", "lint": "ng lint", "modernizr": "modernizr -c modernizr-config.json -d src/modernizr.js", "e2e": "ng e2e", "napa": "napa"}, "private": true, "napa": {}, "dependencies": {"@angular-redux/router": "^7.0.0", "@angular-redux/store": "^7.1.1", "@angular/animations": "5.2.0", "@angular/cdk": "^5.2.5", "@angular/common": "5.2.0", "@angular/compiler": "5.2.0", "@angular/core": "5.2.0", "@angular/forms": "5.2.0", "@angular/http": "5.2.0", "@angular/material": "^5.2.5", "@angular/platform-browser": "5.2.0", "@angular/platform-browser-dynamic": "5.2.0", "@angular/router": "5.2.0", "@auth0/angular-jwt": "^1.1.0", "@ngx-translate/core": "9.1.1", "@ngx-translate/http-loader": "2.0.1", "ag-grid": "16.0.1", "ag-grid-angular": "16.0.0", "angular-bootstrap-md": "^6.1.1", "angular-media-queries": "^0.6.1", "angular-recaptcha": "^4.2.0", "angular-tree-component": "7.0.0", "angular2-jwt": "^0.2.3", "angular2-text-mask": "8.0.4", "angular2-toaster": "5.0.0", "angular5-social-login": "^1.0.9", "bootstrap": "3.3.7", "chart.js": "2.7.0", "chartjs-plugin-annotation": "^0.5.7", "classlist.js": "^1.1.20150312", "codemirror": "5.35.0", "core-js": "2.4.1", "easy-pie-chart": "2.1.7", "enhanced-resolve": "3.3.0", "float": "^1.0.2", "font-awesome": "^4.7.0", "fullcalendar": "3.8.2", "hammerjs": "^2.0.8", "image-map-resizer": "^1.0.10", "immutable": "^3.8.2", "intl": "1.2.5", "jwt-decode": "^2.2.0", "lodash": "4.17.5", "modernizr": "3.5.0", "moment": "2.20.1", "ng-redux": "^4.0.3", "ng2-charts": "1.6.0", "ng2-dnd": "5.0.2", "ng2-file-upload": "1.3.0", "ng2-select": "2.0.0", "ng2-validation": "4.2.0", "ngx-bootstrap": "2.0.2", "ngx-chips": "1.6.5", "ngx-color-picker": "5.3.3", "ngx-editor": "^3.3.0", "ngx-filter-pipe": "^2.1.0", "ngx-infinite-scroll": "0.8.3", "ramda": "^0.25.0", "redux": "^3.7.2", "redux-logger": "^3.0.6", "redux-saga": "^0.16.2", "roboto-fontface": "0.9.0", "rxjs": "5.5.6", "save": "^2.3.2", "screenfull": "3.3.2", "simple-line-icons": "2.4.1", "spinkit": "1.2.5", "sweetalert": "1.1.3", "ts-helpers": "1.1.1", "weather-icons": "github:erikflowers/weather-icons", "web-animations-js": "2.2.1", "yarn": "^1.6.0", "zone.js": "0.8.19"}, "devDependencies": {"@angular/cli": "^1.7.1", "@angular/compiler-cli": "5.2.0", "@angular/language-service": "5.2.0", "@types/codemirror": "0.0.55", "@types/jasmine": "2.8.3", "@types/jasminewd2": "2.0.2", "@types/lodash": "4.14.104", "@types/node": "6.0.60", "codelyzer": "4.0.1", "es6-promise": "4.2.5", "jasmine-core": "2.8.0", "jasmine-spec-reporter": "4.2.1", "karma": "2.0.0", "karma-chrome-launcher": "2.2.0", "karma-cli": "1.0.1", "karma-coverage-istanbul-reporter": "1.2.1", "karma-jasmine": "1.1.0", "karma-jasmine-html-reporter": "0.2.2", "karma-read-json": "1.1.0", "loaders.css": "0.1.2", "napa": "3.0.0", "promise-polyfill": "8.1.0", "protractor": "5.1.2", "raf-polyfill": "1.0.0", "ts-node": "4.1.0", "tslint": "5.9.1", "typescript": "2.5.3", "webdriver-manager": "10.2.5"}}