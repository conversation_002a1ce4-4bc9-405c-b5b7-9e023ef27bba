<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <artifactId>selfCare</artifactId>
    <groupId>com.optimaitalia</groupId>
    <version>0.0.1</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>frontend</artifactId>

  <properties>
    <node.version>v8.11.1</node.version>
    <npm.version>v5.6.0</npm.version>
    <frontend-maven-plugin.version>1.5</frontend-maven-plugin.version>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.6.0</version>
        <executions>
          <execution>
            <id>exec-npm-install</id>
            <phase>generate-sources</phase>
            <configuration>
              <executable>yarn</executable>
              <arguments>
                <argument>build</argument>
              </arguments>
            </configuration>
            <goals>
              <goal>exec</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.0.2</version>
        <configuration>
          <classesDirectory>target</classesDirectory>
          <includes>
            <include>static/**</include>
          </includes>
        </configuration>
      </plugin>
    </plugins>
  </build>


</project>
