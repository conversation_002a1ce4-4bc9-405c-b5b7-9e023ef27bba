package com.optima.common.services;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.models.wrappers.email.EmailMessage;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface EmailService {

    ResponseEntity sendEmail(EmailMessage emailMessage, String to);

    ResponseEntity sendEmailMessageToSupport(EmailMessage message, Boolean differentIncident);

    ResponseEntity sendEmailWithAttachmentToSupport(MultipartFile[] files, String message, String subjectHeader, String subject, String category, String utNumber, String bills, String email);

    ResponseEntity sendRequestForIntegratedSolution(EmailMessage emailMessage, String uid) throws ValidateException;

    ResponseEntity sendRequestForTuttoInUno(EmailMessage emailMessage, String uid) throws ValidateException;

    ResponseEntity sendRequestForBookVisitAgent(EmailMessage emailMessage);
}
