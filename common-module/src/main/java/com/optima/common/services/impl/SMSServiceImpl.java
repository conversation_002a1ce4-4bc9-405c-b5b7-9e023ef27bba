package com.optima.common.services.impl;

import com.optima.common.models.wrappers.sms.SMSEntity;
import com.optima.common.models.wrappers.sms.SendSmsResponse;
import com.optima.common.services.SMSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class SMSServiceImpl implements SMSService {

    @Value("${restdata.urls.smsService}")
    private String smsServiceUrl;

    private final RestTemplate restTemplate;

    @Autowired
    private SMSServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public SendSmsResponse sendSMS(SMSEntity smsEntity) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SMSEntity> request = new HttpEntity<>(smsEntity, headers);
        return restTemplate.postForObject(smsServiceUrl, request, SendSmsResponse.class);
    }
}