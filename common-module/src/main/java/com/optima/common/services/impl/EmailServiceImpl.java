package com.optima.common.services.impl;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.models.wrappers.email.EmailMessage;
import com.optima.common.services.EmailService;
import com.optima.common.validators.OvalValidator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

@Service
public class EmailServiceImpl implements EmailService {

    private static final Logger logger = LogManager.getLogger(EmailServiceImpl.class);
    private final Session session;

    private final OvalValidator validator;

    @Value("${optima.mail.service.support.email}")
    private String servicesSupportEmail;

    @Value("${optima.mail.service.support.email.different.type}")
    private String servicesSupportEmailForDifferentIncident;

    @Value("${optima.mail.service.support.email.condominio}")
    private String servicesSupportEmailForCondominio;

    @Value("${optima.mail.service.integrated.solution.email}")
    private String integratedSolutionEmail;

    @Value("${optima.mail.service.integrated.solution.email.giaClienti}")
    private String integratedSolutionEmailGiaClienti;

    @Value("${optima.mail.service.sender.email}")
    private String senderEmail;

    @Value("${optima.mail.service.business.agent}")
    private String businessAgent;

    @Value("${optima.mail.service.business.agent.martinaDiNardo}")
    private String martinaDiNardo;

    public EmailServiceImpl(Session session, OvalValidator validator) {
        this.session = session;
        this.validator = validator;
    }

    @Override
    public ResponseEntity sendEmailMessageToSupport(EmailMessage emailMessage, Boolean differentIncident) {
        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(this.senderEmail));
            message.setRecipients(Message.RecipientType.TO,
                    differentIncident ? InternetAddress.parse(servicesSupportEmailForDifferentIncident) :
                            emailMessage.getUserType() != null && emailMessage.getUserType().equals("Condominio") ?
                            InternetAddress.parse(servicesSupportEmailForCondominio) : InternetAddress.parse(servicesSupportEmail));
            message.setSubject(emailMessage.getSubjectHeader());
            message.setDisposition(MimeBodyPart.INLINE);
            StringBuilder content = new StringBuilder()
                    .append(emailMessage.getSubject())
                    .append(System.lineSeparator())
                    .append(emailMessage.getMessage())
                    .append(System.lineSeparator());
            if (emailMessage.getBills() != null && emailMessage.getBills().length() > 0) {
                content.append(emailMessage.getBills());
            }
            if (emailMessage.getCategory() != null) {
                content.append(emailMessage.getCategory())
                        .append(System.lineSeparator());
            }
            if (emailMessage.getUtNumber() != null) {
                content.append(emailMessage.getUtNumber())
                        .append(System.lineSeparator());
            }
            message.setContent(content.toString(), "text/html; charset=ISO-8859-1");
            Transport.send(message);
            logger.info("Email has been sent.");
        } catch (MessagingException e) {
            logger.error("Error while trying to send email. Error: {}", e);
            return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
        }
        return new ResponseEntity(HttpStatus.OK);
    }

    @Override
    public ResponseEntity sendEmailWithAttachmentToSupport(MultipartFile[] files, String text, String subjectHeader, String subject, String category, String utNumber, String bills, String email) {
        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(this.senderEmail));
            if (email == null) {
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(servicesSupportEmail));
            } else {
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(email));
            }
            message.setSubject(subjectHeader);
            message.setDisposition(MimeBodyPart.INLINE);

            Multipart multipart = new MimeMultipart();
            MimeBodyPart textPart = new MimeBodyPart();

            Arrays.stream(files).forEach(file -> {
                if (file != null) {
                    try {
                        MimeBodyPart attachmentPart = new MimeBodyPart();
                        DataSource dataSource = new ByteArrayDataSource(file.getBytes(), "application/x-any");
                        attachmentPart.setFileName(file.getOriginalFilename());
                        attachmentPart.setDataHandler(new DataHandler(dataSource));
                        multipart.addBodyPart(attachmentPart);
                    } catch (MessagingException | IOException e) {
                        logger.error("Error while trying to send email. Error: {}", e);
                    }
                }
            });
            StringBuilder messageText = new StringBuilder()
                    .append(subject)
                    .append(System.lineSeparator())
                    .append(text)
                    .append(System.lineSeparator());
            if (bills != null && bills.length() > 0) {
                messageText.append(System.lineSeparator()).append(bills);
            }
            if (category != null) {
                messageText.append(category)
                        .append(System.lineSeparator());
            }
            if (utNumber != null) {
                messageText.append(utNumber)
                        .append(System.lineSeparator());
            }
            textPart.setText(messageText.toString());
            multipart.addBodyPart(textPart);
            message.setContent(multipart, "text/html; charset=ISO-8859-1");

            Transport.send(message);
            logger.info("Email has been sent.");
        } catch (MessagingException e) {
            logger.error("Error while trying to send email. Error: {}", e);
            return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
        }
        return new ResponseEntity(HttpStatus.OK);
    }

    @Override
    public ResponseEntity sendEmail(EmailMessage emailMessage, String to) {
        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(this.senderEmail));
            message.setRecipients(Message.RecipientType.TO,
                    InternetAddress.parse(to));
            message.setDisposition(MimeBodyPart.INLINE);
            message.setSubject(emailMessage.getSubject());
            message.setContent(emailMessage.getMessage(), "text/html; charset=ISO-8859-1");
            Transport.send(message);
            logger.info("Email has been sent.");
        } catch (MessagingException e) {
            logger.error("Error while trying to send email. Error: {}", e);
            return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
        }
        return new ResponseEntity(HttpStatus.OK);
    }

    @Override
    public ResponseEntity sendRequestForIntegratedSolution(EmailMessage emailMessage, String uid) throws ValidateException {
        validator.validate(emailMessage);
        return this.sendEmail(emailMessage, integratedSolutionEmail);


    }

    @Override
    public ResponseEntity sendRequestForTuttoInUno(EmailMessage emailMessage, String uid) throws ValidateException {
        //List<String> to = salesInfo.getSalesMail(uid);
        //if(!to.isEmpty()) {
        //    // to.forEach(email -> this.sendEmail(emailMessage, email));
        //    return this.sendEmail(emailMessage,to.get(0));
        //}
        //else{
        emailMessage.setSubject("Richiesta conversione da Sciolto ad integrato");
        return this.sendEmail(emailMessage, integratedSolutionEmailGiaClienti);
        //}
    }

    @Override
    public ResponseEntity sendRequestForBookVisitAgent(EmailMessage emailMessage) {
        if (Objects.equals(emailMessage.getRecipient(), "giaClienti")) {
            if (this.sendEmail(emailMessage, integratedSolutionEmailGiaClienti).getStatusCodeValue() == 200) {
                logger.info("The email has been successfully <NAME_EMAIL>");
                return new ResponseEntity(HttpStatus.OK);
            } else {
                logger.error("Error while trying to send <NAME_EMAIL>.");
                return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
            }
        } else {
            if (this.sendEmail(emailMessage, integratedSolutionEmailGiaClienti).getStatusCodeValue() == 200) {
                if (this.sendEmail(emailMessage, businessAgent).getStatusCodeValue() == 200) {
                    if (this.sendEmail(emailMessage, martinaDiNardo).getStatusCodeValue() == 200) {
                        logger.info("The emails have been successfully sent");
                        return new ResponseEntity(HttpStatus.OK);
                    } else {
                        logger.error("Error while trying to send third email.");
                        return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
                    }
                } else {
                    logger.error("Error while trying to send second email.");
                    return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
                }
            } else {
                logger.error("Error while trying to send first email.");
                return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
            }
        }
    }
}
