package com.optima.common.models.wrappers;


import com.fasterxml.jackson.annotation.JsonProperty;

public class ResponseResult {
    private String code;
    private String description;

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("codice")
    public void setCode(String code) {
        this.code = code;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("descrizione")
    public void setDescription(String description) {
        this.description = description;
    }
}
