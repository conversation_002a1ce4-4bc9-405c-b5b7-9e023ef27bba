chat:
  ccmm:
    machine:
      address: assistenza-pre.optimaitalia.com
spring:
  datasource:
    url: jdbc:h2:mem:selfCare
    username: sa
    password:
    driver-class-name: org.h2.Driver
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.SQLServerDialect
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
    show-sql: true
    hibernate:
      ddl-auto: update
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
server:
  tomcat:
    max-swallow-size: -1
optima:
  security:
      token:
        secret: Wm5EZWlmbFhMSmpidUhEYTVCSzA5eWZ1SzJRYTpheUJNNG5JVHc5dlFVY0RUc1U0ZFhmUDVlQ0lh
        wso2:
          secret: dcf00f531b8a836415163d5ac37da345c68d8c62310139c50dee32ec044422a7
      permitted-resources:
        - '/'
        - '/*.**'
        - '/assets/**'
        - '/home/<USER>'
        - '/login/**'
        - '/profile/**'
        - '/invoices/**'
        - '/faidate/**'
        - '/support/**'
        - '/moduli/**'
        - '/isAdmin'
        - '/assistant'
        - '/product/**'
        - '/passa-tutto-in-uno/**'
        - '/api/mobile/product/description'
        - '/api/android/**'
        - '/api/mobile/tarifferInternazionali'
        - '/api/mobile/getTariffeNazionali'
        - '/short/data'
        - '/api/notifica/mdp/new'
