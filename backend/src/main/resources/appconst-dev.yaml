restdata:
  token: basic SzFwUDlRQkpJcWU4RnJYZ3kwekVkWWszZ3JnYTpYMFlnbExWYmllQTE1QW1WQmJ1NkJHY0dxUjhh
  urls:
    userauthenticate: http://172.16.91.49:8300/api/governance.customer.AuthenticateUser-API
    userauthenticateFake: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.AuthenticateUser-API/fake
    smsService: http://wso2-esb.optimaitalia.com:8300/api/governance.communications.Sms-API
    token: https://api-pp2.optimaitalia.com:8493/token
    chart: https://api-pp1.optimaitalia.com:8493/public/customer/getInfoServiziCliente/{clientId}
    userdata: https://api-pp1.optimaitalia.com:8493/public/customer/{clientId}/detail
    contracts: https://api-pp2.optimaitalia.com:8493/public/contract/v1.0.0/getContrattiByParams
    invoices: http://api-pp2.optimaitalia.com:8530/public/customer/v1.0.0/getInvoiceByClienteFatt
    services: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/{clientId}/users
    incident-event: https://api-pp2.optimaitalia.com:8493/public/customer/v1.0.0/incidentEvent
    spedizionetestata: https://api-pp1.optimaitalia.com:8493/public/shippings/v1.0.0/search
    spedizione: https://api-pp2.optimaitalia.com:8493/public/shippings/v1.0.0/detail
    formAnswers: http://************:8300/api/governance.support.document.RicercaModulo-API
    account: http://api-pp1.optimaitalia.com:8530/public/customer/v1.0.0/accounting/{clientId}
    сlientiBundle: https://api-pp1.optimaitalia.com:8493/public/offer/v1.0.0/getClientiBundleAll
    сontoRelax: https://api-pp2.optimaitalia.com:8493/public/offer/v1.0.0/getContoRelax
    modalitaPagamento: https://api-pp1.optimaitalia.com:8493/public/customer/getIbanSDD/{clientId}
    getPDF: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.GetFatturaNc-API?relUrl=<>&tipo=COMPLETA
    offers-data: https://api-pp1.optimaitalia.com:8493/public/offer/getDatiOfferta
    segnalazione: https://api-pp1.optimaitalia.com:8493/public/incident/v1.0.0/getByCustomer/{clientId}
    electricity-pod-details: http://wso2-esb.optimaitalia.com:8300/api/governance.electricity.GetPODDetails-API
    electricity-point-adjustments: https://api-pp1.optimaitalia.com:8493/public/electricity/v1.0.0/getConguaglioDetail
    voice-client-cli: https://api-pp1.optimaitalia.com:8493/public/voice/v1.0.0/getVoceCliCliente/{clientId}
    adsl-client-cli: https://api-pp1.optimaitalia.com:8493/public/broadband/v1.0.0/getLineeClienteADSL/{clientId}
    gas-pod-details: https://api-pp1.optimaitalia.com:8493/public/gas/v1.0.0/getInfoPdrByPdr
    gas-pdr-additional-data: https://api-pp1.optimaitalia.com:8493/public/gas/v1.0.0/getAltriDatiPdrByPdr
    gas-point-adjustments: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.gas.GetConguagliPuntiGas-API
    contract-transparency-pdf-data: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/getOfferteTlc
    communication-email-info: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/email/filtered/getByAccountId/
    recommendations-blocks: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/getRaccomandateBlocco/{clientId}
#    router: https://api.optimaitalia.com:8493/public/broadband/v1.0.0/getLineeClienteADSL/{clientId}
#    schedaTecnicaLinea: https://api.optimaitalia.com:8493/public/broadband/v1.0.0/getSchedaTecnicaLinea/{IdLinea}
    causal-group: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.offer.PiDettaglioFatturatoFind-API
    check-tariff: http://www.optimaitalia.com/toolallegatoillustrativo/ajax.php
    router: https://api-pp1.optimaitalia.com:8493/public/broadband/v1.0.0/getLineeClienteADSL/{clientId}
    schedaTecnicaLinea: https://api-pp1.optimaitalia.com:8493/public/broadband/v1.0.0/getSchedaTecnicaLinea/{IdLinea}
    getSchedaTecnicaVoce: https://api-pp1.optimaitalia.com:8493/public/voice/v1.0.0/getSchedaTecnicaVoce
    сomunicazioniNote: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/getNote/{clientId}
    initial-bonus-progress: https://api-pp1.optimaitalia.com:8493/public/offer/v1.0.0/getAndamentoContoRelaxEBonusIniziale
    salesMail: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetSalesByCliente-API/{clientId}
    richiediDilazione: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.customer.VerificaDilazioneExt-API
    saveRichiediDilazione: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.customer.RichiestaDilazioneExt-API
    dilazioneCliente: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetDilazioneCliente-API/{clientId}
    amazon-prime-block-display-url: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.docomo.IsEligible-API/{clientId}
    amazon-prime-create-subscriber-url: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.docomo.CreateSubscriber-API/{clientId}
    amazon-prime-subscribe-url: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.docomo.Subscribe-API/{clientId}
    amazon-prime-unsubscribe-url: http://wso2-esb-pp1.optimaitalia.com:8300/api/governance.docomo.Unsubscribe-API/{clientId}
	reportingAttachment: https://api.optimaitalia.com:8493/public/incident/v1.0.0/creaNota
mobile:
  service:
    url:
      sim-per-client: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Contratti-API
      residuo: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Wallet-API
      sim-details: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.SubscriptionDetails-API
      product: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Product-API
      product-activations: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.ProductActivations-API
      product-offer: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.IVR-API/prodotto
      product-activations-external: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.ProductActivations-API
      traffic-details: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.CallLogs-API
      change-tariff: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.CambioPianoTariffario-API
      change-options: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.CambioOpzioni-API
      modify-balance: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Ricarica-API
      contrattiMobile: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Contratti-API?customerId={clientId}
      prodPurchPromo: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.ProdPurchPromo-API?productPurchaseId={productId}
      app-offer-5G: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.AddOn-API/vendibilita
  properties:
    ricarica-sim:
      url:
        success: http://localhost:4200/faidate/servizi-attivi
        default: http://localhost:4200/faidate/servizi-attivi
user:
  change:
    key: 2314640E-072C-478F-A949-88F748026C43
    change-personal-data-url: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/variazioneAnagrafica
    change-shipping-type-url: https://api-pp1.optimaitalia.com:8493/public/customer/v1.0.0/shippingType
    origin: 200007
