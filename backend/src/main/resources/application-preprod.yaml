chat:
  ccmm:
    machine:
      address: ***********
spring:
  datasource:
    url: **********************************************************************************************
    username: ucraina
    password: ucraina$$2020
    driver-class-name: net.sourceforge.jtds.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.SQLServerDialect
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
    show-sql: true
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.SQLServer2012Dialect
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
server:
  tomcat:
    max-swallow-size: -1
optima:
  security:
    token:
      secret: Wm5EZWlmbFhMSmpidUhEYTVCSzA5eWZ1SzJRYTpheUJNNG5JVHc5dlFVY0RUc1U0ZFhmUDVlQ0lh
      prolonged:
        expiration-time: 86400000
      wso2:
        secret: dcf00f531b8a836415163d5ac37da345c68d8c62310139c50dee32ec044422a7
        expiration-time: 86400000
    permitted-resources:
      - '/'
      - '/*.**'
      - '/assets/**'
      - '/home/<USER>'
      - '/login/**'
      - '/profile/**'
      - '/invoices/**'
      - '/faidate/**'
      - '/support/**'
      - '/moduli/**'
      - '/isAdmin'
      - '/assistant'
      - '/product/**'
      - '/passa-tutto-in-uno/**'
      - '/api/mobile/product/description'
      - '/api/android/**'
      - '/api/mobile/tarifferInternazionali'
      - '/api/mobile/getTariffeNazionali'
      - '/short/data'
      - '/payment'
      - '/myoptima/**'
      - '/api/tariff/**'
      - '/api/otp/**'
      - '/api/ip/**'
      - '/api/mnp/**'
      - '/api/chat/**'
      - '/api/notifica/mdp/**'
      - '/api/notifications'
      - '/api/notifications/{clientId}'
      - '/api/checkIfUserAlreadyRestoredPassword'
      - '/api/getAllUsersWithTheSameEmail'
      - '/api/nagios/**'
      - '/api/energytrainer/**'
      - '/api/user/**'
      - '/psw/setPassword'
      - '/psw/isExpired'
      - '/internal/**'
      - '/forgotPassword/**'
      - '/api/resetPassword/**'
      - '/api/voucher-card'
      - '/api/prospect/**'
      - '/api/prospect/information/**'
      - '/prospect/registration'
      - '/prospect/login'
      - '/api/optima-young/**'
      - '/api/health'


