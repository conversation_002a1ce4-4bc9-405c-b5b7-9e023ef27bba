optima:
  api-host: https://api.optimaitalia.com
restdata:
  token: basic Wm5EZWlmbFhMSmpidUhEYTVCSzA5eWZ1SzJRYTpheUJNNG5JVHc5dlFVY0RUc1U0ZFhmUDVlQ0lh
  urls:
    userauthenticate: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.AuthenticateUser-API
    userauthenticateFake: https://api.optimaitalia.com:8493/public/customer/v1.0.0/authenticateUser
    smsService: http://wso2-esb.optimaitalia.com:8300/api/governance.communications.Sms-API
    token: ${optima.api-host}:8493/token
    chart: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetInfoServiziCliente-API/{clientId}
    #*****************************************
    #userdata: https://api.optimaitalia.com:8493/public/customer/{clientId}/detail
    #CACHE
    userdata: http://mi-areaclienti.optimaitalia.com:13000/profilo?idCliente={clientId}
    #****************************************
    contracts: http://wso2-esb.optimaitalia.com:8300/api/governance.contracts.GetContrattiByParams-API/withCheck

    #*****************************************
    #invoices: https://api.optimaitalia.com:8493/public/customer/v1.0.0/getInvoiceByClienteFatt
    #CACHE
    invoices: http://mi-areaclienti.optimaitalia.com:13000/fatture
    #*****************************************

    #*****************************************
    #services: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.UltimoStatoUtenze-API/{clientId}/0
    #CACHE
    services: http://mi-areaclienti.optimaitalia.com:13000/ultimoStatoUtenza?idCliente={clientId}
    #*****************************************

    incident-event: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.IncidentEvent-API
    #*****************************************
    #spedizionetestata: https://api.optimaitalia.com:8493/public/shippings/v1.0.0/search
    #CACHE
    spedizionetestata: http://mi-areaclienti.optimaitalia.com:13000/spedizioni
    #*****************************************

    #*****************************************
    #spedizione: https://api.optimaitalia.com:8493/public/shippings/v1.0.0/detail
    #CACHE
    spedizione: http://mi-areaclienti.optimaitalia.com:13000/spedizioniDettaglio
    #*****************************************

    billing-center: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/SYS_PROV_PA_Selfcare
    business-agent: http://wso2-esb.optimaitalia.com:8300/api/governance.sale.GetAgentiAssegnatiTargetCliente-API
    formAnswers: http://wso2-esb:8300/api/governance.support.document.RicercaModulo-API
    account: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetSituazioneContabileCliente-API/{clientId}
    сlientiBundle: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetClientiBundleAll-API
    sciolto: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.Bundle-API?gruppoVendita=CVM&idcluster={cluster}&crossSelling=S&codiceBundle={offer}
    generalSciolito: http://wso2-esb.optimaitalia.com:8300/api/governance.campaign.IsInLastTarget-API/1265?customerId={clientId}
    сontoRelax: https://api.optimaitalia.com:8493/public/offer/v1.0.0/getContoRelax
    modalitaPagamento: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetIbanSDD-API/{clientId}
    сredit-policy-status: http://wso2-esb.optimaitalia.com:8300/api/governance.Credito.getUltimoStatoCreditPolicy-API/{clientId}
    getPDF: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.GetFile-API?relUrl=
    getFatturaNc: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.GetFatturaNc-API?tipo=COMPLETA&relUrl=
    #*****************************************
    #offers-data: https://api.optimaitalia.com:8493/public/offer/getDatiOfferta/smart
    #CACHE
    offers-data: http://mi-areaclienti.optimaitalia.com:13000/contoRelax
    #*****************************************

    mnpPostActivation: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.MnpPostActivation-API?createIncident=true&origine=selfcare
    mnpUploadFiles: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.UploadFile-API
    payPalActivation: http://wso2-esb.optimaitalia.com:8300//api/governance.payment.Paypal-API/creaOrdine
    segnalazione: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.Incident-API/ByCustomer/{clientId}
    electricity-pod-details: http://wso2-esb.optimaitalia.com:8300/api/governance.electricity.GetPODDetails-API
    electricity-autolettura-2g: http://wso2-esb.optimaitalia.com:8300/api/governance.eos.GetInfoPod-API
    electricity-point-adjustments: http://wso2-esb.optimaitalia.com:8300/api/governance.electricity.GetConguaglioDetail-API
    electricity-details-by-hours: http://wso2-esb.optimaitalia.com:8300/api/governance.optimapower.GetConsumiDettagliOre-API
    voice-client-cli: http://wso2-esb.optimaitalia.com:8300/api/governance.voice.GetVoceCliCliente-API/{clientId}
    adsl-client-cli: http://wso2-esb.optimaitalia.com:8300/api/governance.broadband.GetLineeClienteADSL-API/{clientId}
    gas-pod-details: http://wso2-esb.optimaitalia.com:8300/api/governance.gas.GetInfoPdrByPdr-API
    gas-pdr-additional-data: http://wso2-esb.optimaitalia.com:8300/api/governance.gas.GetAltriDatiPdrByPdr-API
    gas-point-adjustments: http://wso2-esb.optimaitalia.com:8300/api/governance.gas.GetConguagliPuntiGas-API
    contract-transparency-pdf-data: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.OfferteTlcAttiveByCliente-API
    communication-email-info: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.Email-API/GetByAccountIdFiltered/
    recommendations-blocks: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.RaccomandateBlocco-API
    router: http://wso2-esb.optimaitalia.com:8300/api/governance.broadband.GetLineeClienteADSL-API/{clientId}
    schedaTecnicaLinea: http://wso2-esb.optimaitalia.com:8300/api/governance.broadband.GetSchedaTecnicaLinea-API/{IdLinea}
    initial-bonus-progress: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetAndamentoContoRelaxEBonusIniziale-API
    check-tariff: http://www.optimaitalia.com/toolallegatoillustrativo/ajax.php
    grantModalitaPagamento: http://wso2-esb.optimaitalia.com:8300/api/governance.integration.Execute-API
    getSchedaTecnicaVoce: http://wso2-esb.optimaitalia.com:8300/api/governance.voice.GetSchedaTecnicaVoce-API
    сomunicazioniNote: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetNote-API/{clientId}
    salesMail: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetSalesByCliente-API/{clientId}
    dilazioneCliente: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetDilazioneCliente-API/{clientId}
    richiediDilazione: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.VerificaDilazioneExt-API
    saveRichiediDilazione: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.RichiestaDilazioneExt-API
    recontact-request-url: http://wso2-esb.optimaitalia.com:8300/api/governance.avaya.AppendContactsToCampaign-API/async
    customer-cluster-info-url: http://wso2-esb.optimaitalia.com:8300/api/governance.integration.GetClusterByUser-API
    amazon-prime-block-display-url: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.IsEligible-API/{clientId}
    amazon-prime-data: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.IsEligible-API/v2/{clientId}
    amazon-prime-create-subscriber-url: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.CreateSubscriber-API/{clientId}
    amazon-prime-subscribe-url: http://wso2-esb.optimaitalia.com:8300/api/governance.services.provisioning.Dynamic-API/amazonDocomo/AmazonActivationUrl/{clientId}
    amazon-prime-unsubscribe-url: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.Unsubscribe-API/{clientId}
    reportingAttachment: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.CreaNota-API
    meseOff: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.PianificazionePromo-API
    change-promo-mese: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.ChangePromoMeseOff-API
    pagamentoFlessibile: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.VerificaFatturaFlessibile-API/{customer_id}
    condominio: http://wso2-esb.optimaitalia.com:8300/api/governance.services.crm.Dynamic-API/crm/customer/{clientId}/condomini
    сondominioFiscalCode: http://wso2-esb.optimaitalia.com:8300/api/governance.dwh.Integration.ExecuteQuery-API/SYS_DWH_GetAmministratoreCondominio
    auditLog: http://wso2-esb.optimaitalia.com:8300/api/governance.support.monitoring.SystemMonitoring-API/auditLog
    voucher-card-status: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.GetDetailsByCodice-API?codice=
    voucher-card-redeem: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.AssociaVaucherCliente-API
    voucher-card-user-information: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.GetAssociazioni-API?idCliente={clientId}&idTipoCard={typeCard}
    voucher-card-contracts: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/USR_PROV_vContrattiByParams
    voucher-card-sim-balance: http://wso2-esb.optimaitalia.com:8300/api/governance.dwh.Integration.ExecuteQuery-API/SYS_SimCreditoResiduo
    voucher-card-top-up-sim: http://wso2-esb.optimaitalia.com:8300/api/governance.services.provisioning.Dynamic-API/mobile/createGenericOperation.json
    porta-un-amico: http://wso2-esb.optimaitalia.com:8300/api/governance.crm.CouponOptimaCampus-API?customerId=
    app-offer-5G: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.AddOn-API/vendibilita
    #prospect users
    contracts-by-cf: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/SYS_PROV_vContrattiPerCF
    prospect-email-support: http://wso2-esb.optimaitalia.com:8300/api/governance.communications.EmailStream-API
    prospect-file-upload: http://wso2-esb.optimaitalia.com:8300/api/governance.contracts.Upload-API/{fileName}/{contractId}/test/test/1/1
mobile:
  service:
    url:
      #*****************************************
      #contratti-mobile: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/contratti
      #CACHE
      user-geolocation: http://api.ipstack.com/check?access_key=********************************
      sim-per-client: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Subscription-API
      #*****************************************************************************
      residuo: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Wallet-API
      #sim-details: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/subscription/details
      sim-details: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.SubscriptionDetails-API
      sim-balance: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Wallet-API
      product: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Product-API?idCanaleVendita=26
      product-offer: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.IVR-API/prodotto
      product-activations: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.ProductActivations-API
      product-activations-external: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.ProductActivations-API
      traffic-details: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.CallLogs-API
      change-tariff: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.CambioPianoTariffario-API
      change-options: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.CambioOpzioni-API
      modify-balance: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/ricarica
      ricarica-sisal: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.IVR-API/ricarica
      #*****************************************
      #contratti-mobile: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/contratti
      #CACHE
      #contrattiMobile: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Contratti-API?customerId={clientId}
      contrattiMobile: http://mi-areaclienti.optimaitalia.com:13000/contrattiMobile?idCliente={clientId}
      #*****************************************
      prodPurchPromo: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.ProdPurchPromo-API?productPurchaseId={productId}
      mobile-operators: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.MobileOperators-API?visible=true

  properties:
    ricarica-sim:
      url:
        success: http://areaclienti.optimaitalia.com/faidate/servizi-attivi
        default: http://areaclienti.optimaitalia.com/faidate/servizi-attivi

user:
  change:
    key: 2314640E-072C-478F-A949-88F748026C43
    change-personal-data-url: ${optima.api-host}:8493/public/customer/v1.0.0/variazioneAnagrafica
    change-password-identification-url: http://wso2-esb.optimaitalia.com:8300/api/governance.crm.IdPwdChange-API
    change-shipping-type-url: ${optima.api-host}:8493/public/customer/v1.0.0/shippingType
    billing-address-change-url: ${optima.api-host}:8493/public/customer/v1.0.0/billingAddressChange
    recipient-code-change-url: ${optima.api-host}:8493/public/customer/customDestCodeChange
    origin: 200007
security:
  sso-token: https://api.optimaitalia.com:8493/public/secured/myoptima/validateUser/{clientId}
mese:
  codicePromo: PR_34
