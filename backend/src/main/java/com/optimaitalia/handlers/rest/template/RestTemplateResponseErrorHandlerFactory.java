package com.optimaitalia.handlers.rest.template;


import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.function.Consumer;
import java.util.function.Supplier;

import java.util.Map;

public interface RestTemplateResponseErrorHandlerFactory {

    RestTemplateResponseErrorHandler get(HttpStatus httpStatus);

    static RestTemplateResponseErrorHandlerFactory factory(Consumer<Builder> consumer) {
        Map<HttpStatus, RestTemplateResponseErrorHandler> map = new HashMap<>();
        consumer.accept(map::put);
        return map::get;
    }
}
