package com.optimaitalia.handlers.rest.template;

import com.optima.security.service.impl.SecurityServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.client.ClientHttpResponse;

public class RestTemplateUnauthorizedResponseHandler implements RestTemplateResponseErrorHandler {

    private static final Logger logger = LogManager.getLogger(RestTemplateUnauthorizedResponseHandler.class);

    /**
     * This handler remove old services access token.
     * Known situation when token hasn't expired, but remote services dont accept it.
     */
    @Override
    public void handleResponseError(ClientHttpResponse response) {
        logger.error("Unauthorized. Removing old access token...");
        SecurityServiceImpl.clear();
    }
}
