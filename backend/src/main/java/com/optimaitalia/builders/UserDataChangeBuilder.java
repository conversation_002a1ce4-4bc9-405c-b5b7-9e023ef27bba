package com.optimaitalia.builders;

import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.wrappers.user.requests.Change;
import com.optimaitalia.model.wrappers.user.requests.UserDataChangeRequest;

import java.util.function.Function;

public interface UserDataChangeBuilder<T extends UserDataChangeRequest, R extends Change> {

    R build(T request);

    void addBuilder(IncidentEventCategory key, Function<T, R> converter);
}
