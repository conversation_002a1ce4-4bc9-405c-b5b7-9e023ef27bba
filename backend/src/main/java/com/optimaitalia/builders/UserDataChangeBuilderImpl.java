package com.optimaitalia.builders;

import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.wrappers.user.requests.Change;
import com.optimaitalia.model.wrappers.user.requests.Changeable;
import com.optimaitalia.model.wrappers.user.requests.UserDataChangeRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

public class UserDataChangeBuilderImpl<T extends UserDataChangeRequest, R extends Change & Changeable> implements UserDataChangeBuilder<T, R> {

    private Map<IncidentEventCategory, Function<T, R>> factory = new HashMap<>();

    @Override
    public R build(T request) {
        Function<T, R> function = factory.get(request.getIncidentEventCategory());
        if (function != null) {
            return function.apply(request);
        }
        throw new RuntimeException("No converter found.");
    }

    @Override
    public void addBuilder(IncidentEventCategory key, Function<T, R> converter) {
        this.factory.put(key, converter);
    }
}
