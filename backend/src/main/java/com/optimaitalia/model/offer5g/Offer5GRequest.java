package com.optimaitalia.model.offer5g;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Offer5GRequest {
    private Long idCliente;
    private Long subscriptionId;
    private Integer canaleVendita;

    public Offer5GRequest() {
    }

    public Offer5GRequest(Long idCliente, Long subscriptionId) {
        this.idCliente = idCliente;
        this.subscriptionId = subscriptionId;
        this.canaleVendita = 27; // Hardcoded as per requirements
    }

    @JsonProperty("idCliente")
    public Long getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(Long idCliente) {
        this.idCliente = idCliente;
    }

    @JsonProperty("subscriptionId")
    public Long getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(Long subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty("canaleVendita")
    public Integer getCanaleVendita() {
        return canaleVendita;
    }

    public void setCanaleVendita(Integer canaleVendita) {
        this.canaleVendita = canaleVendita;
    }
}
