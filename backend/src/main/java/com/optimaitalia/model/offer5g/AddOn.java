package com.optimaitalia.model.offer5g;

import com.fasterxml.jackson.annotation.JsonProperty;

public class AddOn {
    private String codice;
    private String descrizione;
    private String codiceOfferta;
    private Double costoAttivazione;
    private Double canoneMese;
    private Integer numRichiesteAttivazioni;
    private Boolean richiestaInCorso;

    @JsonProperty("codice")
    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    @JsonProperty("descrizione")
    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @JsonProperty("codiceOfferta")
    public String getCodiceOfferta() {
        return codiceOfferta;
    }

    public void setCodiceOfferta(String codiceOfferta) {
        this.codiceOfferta = codiceOfferta;
    }

    @JsonProperty("costoAttivazione")
    public Double getCostoAttivazione() {
        return costoAttivazione;
    }

    public void setCostoAttivazione(Double costoAttivazione) {
        this.costoAttivazione = costoAttivazione;
    }

    @JsonProperty("canoneMese")
    public Double getCanoneMese() {
        return canoneMese;
    }

    public void setCanoneMese(Double canoneMese) {
        this.canoneMese = canoneMese;
    }

    @JsonProperty("numRichiesteAttivazioni")
    public Integer getNumRichiesteAttivazioni() {
        return numRichiesteAttivazioni;
    }

    public void setNumRichiesteAttivazioni(Integer numRichiesteAttivazioni) {
        this.numRichiesteAttivazioni = numRichiesteAttivazioni;
    }

    @JsonProperty("richiestaInCorso")
    public Boolean getRichiestaInCorso() {
        return richiestaInCorso;
    }

    public void setRichiestaInCorso(Boolean richiestaInCorso) {
        this.richiestaInCorso = richiestaInCorso;
    }
}
