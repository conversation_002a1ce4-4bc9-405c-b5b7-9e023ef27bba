package com.optimaitalia.model.offer5g;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class Sim {
    private String msisdnId;
    private String subscriptionId;
    private String dataProssimoRinnovo;
    private List<AddOn> addOnAviable;
    private List<AddOn> addOnActive;

    @JsonProperty("msisdnId")
    public String getMsisdnId() {
        return msisdnId;
    }

    public void setMsisdnId(String msisdnId) {
        this.msisdnId = msisdnId;
    }

    @JsonProperty("subscriptionId")
    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty("dataProssimoRinnovo")
    public String getDataProssimoRinnovo() {
        return dataProssimoRinnovo;
    }

    public void setDataProssimoRinnovo(String dataProssimoRinnovo) {
        this.dataProssimoRinnovo = dataProssimoRinnovo;
    }

    @JsonProperty("addOnAviable")
    public List<AddOn> getAddOnAviable() {
        return addOnAviable;
    }

    public void setAddOnAviable(List<AddOn> addOnAviable) {
        this.addOnAviable = addOnAviable;
    }

    @JsonProperty("addOnActive")
    public List<AddOn> getAddOnActive() {
        return addOnActive;
    }

    public void setAddOnActive(List<AddOn> addOnActive) {
        this.addOnActive = addOnActive;
    }
}
