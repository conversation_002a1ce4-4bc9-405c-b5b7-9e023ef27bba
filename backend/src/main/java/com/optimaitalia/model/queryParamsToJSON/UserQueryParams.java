package com.optimaitalia.model.queryParamsToJSON;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UserQueryParams {

    private Long clientId;

    public UserQueryParams(Long clientId) {
        this.clientId = clientId;
    }

    @JsonProperty("IdCliente")
    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }


}
