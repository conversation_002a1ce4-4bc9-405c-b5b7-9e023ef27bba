package com.optimaitalia.model.queryParamsToJSON;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Criteria {

    private UserQueryParams userQueryParams;

    public Criteria(Long clientId) {
        this.userQueryParams = new UserQueryParams(clientId);
    }

    @JsonProperty("Criteria")
    public UserQueryParams getUserQueryParams() {
        return userQueryParams;
    }

    public void setUserQueryParams(UserQueryParams userQueryParams) {
        this.userQueryParams = userQueryParams;
    }
}
