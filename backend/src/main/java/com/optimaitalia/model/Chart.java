package com.optimaitalia.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Chart {

    private String serviceName;

    private Boolean isActive;

    @JsonProperty("serviceName")
    public String getServiceName() {
        return serviceName;
    }

    @JsonProperty("TipoServizio")
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @JsonProperty("isActive")
    public Boolean getActive() {
        return isActive;
    }

    @JsonProperty("Attivo")
    public void setActive(Boolean active) {
        isActive = active;
    }
}
