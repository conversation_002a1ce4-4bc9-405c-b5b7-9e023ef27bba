package com.optimaitalia.model.db;

import javax.persistence.*;

@Entity
@Table(name = "notifica_mdp")
public class NotificaMdp {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "client_id")
    private Long clientId;

    @Column(name = "notification_text")
    private String notificationText;

    @Column(name = "is_displayed")
    private Boolean isDisplayedNotificationText;

    @Column(name = "redirect_url")
    private String redirectUrl;

    @Column(name = "header_text")
    private String headerText;

    @Column(name = "notification_text_marker")
    private String textMarker;

    @Transient
    private String notificationTextFirstPart;

    @Transient
    private String notificationTextSecondPart;

    public Long getId() {
        return id;
    }

    public NotificaMdp setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public NotificaMdp setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public String getNotificationText() {
        return notificationText;
    }

    public NotificaMdp setNotificationText(String notificationText) {
        this.notificationText = notificationText;
        return this;
    }

    public Boolean getIsDisplayedNotificationText() {
        return isDisplayedNotificationText;
    }

    public NotificaMdp setIsDisplayedNotificationText(Boolean isDisplayedNotificationText) {
        this.isDisplayedNotificationText = isDisplayedNotificationText;
        return this;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public NotificaMdp setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
        return this;
    }

    public String getHeaderText() {
        return headerText;
    }

    public NotificaMdp setHeaderText(String headerText) {
        this.headerText = headerText;
        return this;
    }

    public String getTextMarker() {
        return textMarker;
    }

    public void setTextMarker(String textMarker) {
        this.textMarker = textMarker;
    }

    public String getNotificationTextFirstPart() {
        return notificationTextFirstPart;
    }

    public void setNotificationTextFirstPart(String notificationTextFirstPart) {
        this.notificationTextFirstPart = notificationTextFirstPart;
    }

    public String getNotificationTextSecondPart() {
        return notificationTextSecondPart;
    }

    public void setNotificationTextSecondPart(String notificationTextSecondPart) {
        this.notificationTextSecondPart = notificationTextSecondPart;
    }
}

