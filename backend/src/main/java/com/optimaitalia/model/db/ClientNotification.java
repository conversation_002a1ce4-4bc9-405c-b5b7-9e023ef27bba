package com.optimaitalia.model.db;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.OptimaJsonDateDeserializer;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "сlient_notification")
public class ClientNotification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @NotNull
    @Column(name = "client_id", nullable = false)
    private Long clientId;

    @NotNull
    @NotEmpty
    @Column(name = "title", nullable = false)
    private String title;

    @NotNull
    @NotEmpty
    @Column(name = "message", nullable = false)
    private String message;

    @NotNull
    @JsonDeserialize(using = OptimaJsonDateDeserializer.class)
    @Column(name = "insert_date", nullable = false, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date insertDate;

    @JsonDeserialize(using = OptimaJsonDateDeserializer.class)
    @Column(name = "read_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date readDate;

    @Column(name = "text_marker")
    private String textMarker;

    @Column(name = "redirect_url")
    private String redirectUrl;

    @Column(name = "duration")
    private Integer duration;

    @Column(name = "expire_date")
    private Date expire_date;

    @PrePersist
    private void onCreate() {
        this.insertDate = new Date();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ClientNotification)) return false;
        ClientNotification that = (ClientNotification) o;
        return clientId.equals(that.clientId) && message.equals(that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId, message);
    }
}
