package com.optimaitalia.model.charts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.List;

@Getter
public class ChartInformation {

    @JsonProperty("DescrizioneEsito")
    private String descrizioneEsito;

    @JsonProperty("Esito")
    private Integer esito;

    @JsonProperty("StackTrace")
    private String stackTrace;

    @JsonProperty("ListaConsumiDettagliOre")
    private List<ListaConsumiDettagliOre> listaConsumiDettagliOre;

}
