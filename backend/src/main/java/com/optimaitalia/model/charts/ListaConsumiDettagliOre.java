package com.optimaitalia.model.charts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class ListaConsumiDettagliOre {
    @JsonProperty("dataConsumo")
    private String dataConsumo;

    @JsonProperty("ora1")
    private Double ora1;

    @JsonProperty("ora2")
    private Double ora2;

    @JsonProperty("ora3")
    private Double ora3;

    @JsonProperty("ora4")
    private Double ora4;

    @JsonProperty("ora5")
    private Double ora5;

    @JsonProperty("ora6")
    private Double ora6;

    @JsonProperty("ora7")
    private Double ora7;

    @JsonProperty("ora8")
    private Double ora8;

    @JsonProperty("ora9")
    private Double ora9;

    @JsonProperty("ora10")
    private Double ora10;

    @JsonProperty("ora11")
    private Double ora11;

    @JsonProperty("ora12")
    private Double ora12;

    @JsonProperty("ora13")
    private Double ora13;

    @JsonProperty("ora14")
    private Double ora14;

    @JsonProperty("ora15")
    private Double ora15;

    @JsonProperty("ora16")
    private Double ora16;

    @JsonProperty("ora17")
    private Double ora17;

    @JsonProperty("ora18")
    private Double ora18;

    @JsonProperty("ora19")
    private Double ora19;

    @JsonProperty("ora20")
    private Double ora20;

    @JsonProperty("ora21")
    private Double ora21;

    @JsonProperty("ora22")
    private Double ora22;

    @JsonProperty("ora23")
    private Double ora23;

    @JsonProperty("ora24")
    private Double ora24;

    @JsonProperty("IdConsumo")
    private String idConsumo;

    @JsonProperty("ka")
    private Integer ka;

    @JsonProperty("kp")
    private Integer kp;

    @JsonProperty("kr")
    private Integer kr;
}
