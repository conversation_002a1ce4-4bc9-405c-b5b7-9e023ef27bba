package com.optimaitalia.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ShipmentAddress {

    private String address;

    private String postalCode;

    private String common;

    @JsonProperty("address")
    public String getAddress() {
        return address;
    }

    @JsonProperty("indirizzo")
    public void setAddress(String address) {
        this.address = address;
    }

    @JsonProperty("postalCode")
    public String getPostalCode() {
        return postalCode;
    }

    @JsonProperty("cap")
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    @JsonProperty("common")
    public String getCommon() {
        return common;
    }

    @JsonProperty("comune")
    public void setCommon(String common) {
        this.common = common;
    }
}
