package com.optimaitalia.model.optimaResponseRequest;

public class IsUserResponse {
    private Boolean available;
    private String description;
    private Boolean success;

    public IsUserResponse(Boolean available, String description, Boolean success) {
        this.available = available;
        this.description = description;
        this.success = success;
    }

    public Boolean getAvailable() {
        return available;
    }

    public void setAvailable(Boolean available) {
        this.available = available;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
