package com.optimaitalia.model.forgotPassword;

public class ForgotPasswordResponse {
    private Integer status;
    private String message;

    public ForgotPasswordResponse() {
        this.status = null;
        this.message = null;
    }

    public ForgotPasswordResponse(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
