package com.optimaitalia.model.wrappers.mobile.products;

public class ProductRecordsRequest {

    private Integer productTypeId;

    private Integer tariffPlanId;

    private Integer idCluster;

    private Long subscriptionId;

    private Integer purchaseChannelId;


    public Integer getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Integer productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Integer getTariffPlanId() {
        return tariffPlanId;
    }

    public void setTariffPlanId(Integer tariffPlanId) {
        this.tariffPlanId = tariffPlanId;
    }

    public Integer getIdCluster() {
        return idCluster;
    }

    public void setIdCluster(Integer idCluster) {
        this.idCluster = idCluster;
    }

    public Long getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(Long subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public Integer getPurchaseChannelId() {
        return purchaseChannelId;
    }

    public void setPurchaseChannelId(Integer purchaseChannelId) {
        this.purchaseChannelId = purchaseChannelId;
    }
}
