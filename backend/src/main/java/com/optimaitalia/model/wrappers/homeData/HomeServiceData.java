package com.optimaitalia.model.wrappers.homeData;

import com.fasterxml.jackson.annotation.JsonProperty;

public class HomeServiceData {

    @JsonProperty("tuttoInUno")
    private Double tuttoInUno;

    @JsonProperty("contoRelax")
    private Double contoRelax;

    public Double getTuttoInUno() {
        return tuttoInUno;
    }

    public void setTuttoInUno(Double tuttoInUno) {
        this.tuttoInUno = tuttoInUno;
    }

    public Double getContoRelax() {
        return contoRelax;
    }

    public void setContoRelax(Double contoRelax) {
        this.contoRelax = contoRelax;
    }
}
