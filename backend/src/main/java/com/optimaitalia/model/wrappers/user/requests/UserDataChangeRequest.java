package com.optimaitalia.model.wrappers.user.requests;

import com.optimaitalia.model.enums.IncidentEventCategory;
import net.sf.oval.constraint.Digits;
import net.sf.oval.constraint.Email;
import net.sf.oval.constraint.Length;
import net.sf.oval.constraint.NotNull;

import javax.validation.constraints.NotEmpty;


public class UserDataChangeRequest {

    @NotNull
    private Long clientId;

    @NotNull
    private IncidentEventCategory incidentEventCategory;

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public IncidentEventCategory getIncidentEventCategory() {
        return incidentEventCategory;
    }

    public void setIncidentEventCategory(IncidentEventCategory incidentEventCategory) {
        this.incidentEventCategory = incidentEventCategory;
    }

}
