package com.optimaitalia.model.wrappers.mnp;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class Change {

    private Integer changeType;
    private String portingNumber;
    /*    private String portingICCID;
        private String companyName;
        private String vatNumber;*/
    private String firstName;
    private String lastName;
    private String fiscalCode;
    private String sourceSupplierCode;
    private String sourceSupplierDesc;
    private String sourceContractType;
    private String documentType;
    private String documentNumber;
    private Boolean creditTransfer;
    private Boolean partialCheck;
    /*private Boolean theftFlag;*/
    private String dataCut;
    /*private Integer subscriptionId;*/
    private String msisdn;
    /*private String applyDate;*/
    // private Integer changeId;
    private List<File> files;
}
