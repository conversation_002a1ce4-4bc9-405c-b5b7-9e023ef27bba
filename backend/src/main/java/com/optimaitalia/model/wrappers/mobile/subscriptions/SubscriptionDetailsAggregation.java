package com.optimaitalia.model.wrappers.mobile.subscriptions;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SubscriptionDetailsAggregation {

    private String groupName;
    private Double liveAmount;
    private Counter counter;
    private List<Activation> activations;

    @JsonProperty("groupName")
    public String getGroupName() {
        return groupName;
    }

    @JsonProperty("gruppo")
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Double getLiveAmount() {
        return liveAmount;
    }

    public void setLiveAmount(Double liveAmount) {
        this.liveAmount = liveAmount;
    }

    public Counter getCounter() {
        return counter;
    }

    public void setCounter(Counter counter) {
        this.counter = counter;
    }

    public List<Activation> getActivations() {
        return activations;
    }

    public void setActivations(List<Activation> activations) {
        this.activations = activations;
    }
}
