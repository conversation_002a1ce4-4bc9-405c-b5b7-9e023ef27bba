package com.optimaitalia.model.wrappers.voucherCard;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class Entity {
    private String codiceCoupon;
    private String tipoCard;
    private Double importCoupon;
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    private Date dataAttivazione;
    private Double creditoResiduo;
    private Long idCliente;
    private Long idClienteAssociato;
    private Long idContrattoAssociato;
    private Integer idFatturaAssociato;
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    private Date dataStipula;
    private String indirizzo;
    private String pod;
    private String pdr;
    private String sim;
    private Integer numeroFattura;
    private Double importoTipoCardInFattura;
    private String fatturaRiferimento;
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    private Date dataEmissione;
    private String downloadUrl;
    private Long idGruppo;
}
