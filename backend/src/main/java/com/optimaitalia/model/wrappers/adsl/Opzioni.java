package com.optimaitalia.model.wrappers.adsl;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

@Data
public class Opzioni {

    @JsonAlias("Codice")
    public String codice;

    @JsonAlias("CostoAttivazione")
    public Double costoAttivazione;

    @JsonAlias("CostoDisattivazione")
    public Double costoDisattivazione;

    @JsonAlias("CostoMese")
    public Double costoMese;

    @JsonAlias("DataFineValidita")
    public Object dataFineValidita;

    @JsonAlias("DataInizioValidita")
    public Object dataInizioValidita;

    @JsonAlias("Descrizione")
    public String descrizione;

    @JsonAlias("Id")
    public Integer id;
}
