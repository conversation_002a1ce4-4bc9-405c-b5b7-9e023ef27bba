package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.OfferDateDeserializer;

import java.util.Date;
import java.util.List;

public class Offer {

    private Integer offerType;

    private List<Adsl> adsl;

    private Double canoneMensile;

    private Double canoneStabilizzato;

    private List<Ee> ee;

    private String tipoCommercialeDescrizione;

    private Object gas;

    private Double saldoContoRelax;

    @JsonDeserialize(using = OfferDateDeserializer.class)
    private Date scadenzaAnnoContrattuale;

    private List<Voce> voce;

    private List<Mobile> mobile;

    private List<Assicurazione> assicurazione;

    private List<CoverCare> coverCare;

    private Long billingId;

    private Long idInvoiceSaldoPrevisionale;

    private Double valoreSaldoPrevisionale;

    private Object valueLastSaldoCr;

    @JsonProperty("offerType")
    public Integer getOfferType() {
        return offerType;
    }

    @JsonProperty("tipologiaOfferta")
    public void setOfferType(Integer offerType) {
        this.offerType = offerType;
    }

    public Long getIdInvoiceSaldoPrevisionale() {
        return idInvoiceSaldoPrevisionale;
    }

    public void setIdInvoiceSaldoPrevisionale(Long idInvoiceSaldoPrevisionale) {
        this.idInvoiceSaldoPrevisionale = idInvoiceSaldoPrevisionale;
    }

    public Double getValoreSaldoPrevisionale() {
        return valoreSaldoPrevisionale;
    }

    public void setValoreSaldoPrevisionale(Double valoreSaldoPrevisionale) {
        this.valoreSaldoPrevisionale = valoreSaldoPrevisionale;
    }

    public Object getValueLastSaldoCr() {
        return valueLastSaldoCr;
    }

    public void setValueLastSaldoCr(Object valueLastSaldoCr) {
        this.valueLastSaldoCr = valueLastSaldoCr;
    }

    public List<Adsl> getAdsl() {
        return adsl;
    }

    public void setAdsl(List<Adsl> adsl) {
        this.adsl = adsl;
    }

    public Double getCanoneMensile() {
        return canoneMensile;
    }

    public void setCanoneMensile(Double canoneMensile) {
        this.canoneMensile = canoneMensile;
    }

    public Double getCanoneStabilizzato() {
        return canoneStabilizzato;
    }

    public void setCanoneStabilizzato(Double canoneStabilizzato) {
        this.canoneStabilizzato = canoneStabilizzato;
    }

    public List<Ee> getEe() {
        return ee;
    }

    public void setEe(List<Ee> ee) {
        this.ee = ee;
    }

    public Object getGas() {
        return gas;
    }

    public String getTipoCommercialeDescrizione() {
        return tipoCommercialeDescrizione;
    }

    public void setTipoCommercialeDescrizione(String tipoCommercialeDescrizione) {
        this.tipoCommercialeDescrizione = tipoCommercialeDescrizione;
    }

    public void setGas(Object gas) {
        this.gas = gas;
    }

    public Double getSaldoContoRelax() {
        return saldoContoRelax;
    }

    public void setSaldoContoRelax(Double saldoContoRelax) {
        this.saldoContoRelax = saldoContoRelax;
    }

    public Date getScadenzaAnnoContrattuale() {
        return scadenzaAnnoContrattuale;
    }

    public void setScadenzaAnnoContrattuale(Date scadenzaAnnoContrattuale) {
        this.scadenzaAnnoContrattuale = scadenzaAnnoContrattuale;
    }

    public List<Voce> getVoce() {
        return voce;
    }

    public void setVoce(List<Voce> voce) {
        this.voce = voce;
    }

    @JsonProperty("billingId")
    public Long getBillingId() {
        return billingId;
    }

    @JsonProperty("idFatt")
    public void setBillingId(Long billingId) {
        this.billingId = billingId;
    }

    public List<Mobile> getMobile() {
        return mobile;
    }

    public void setMobile(List<Mobile> mobile) {
        this.mobile = mobile;
    }

    public List<Assicurazione> getAssicurazione() {
        return assicurazione;
    }

    public void setAssicurazione(List<Assicurazione> assicurazione) {
        this.assicurazione = assicurazione;
    }

    public List<CoverCare> getCoverCare() {
        return coverCare;
    }

    public void setCoverCare(List<CoverCare> coverCare) {
        this.coverCare = coverCare;
    }
}
