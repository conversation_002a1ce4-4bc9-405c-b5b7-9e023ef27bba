package com.optimaitalia.model.wrappers.mobile.conracts;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;

public class LastStatusChange {

//    private Object balance;

    private Long id;

    private Date occurredOn;

//    private Object oldStatus;

    private Long oldStatusId;

//    private Object reason;

    private Status status;

    private Long statusId;

    private Long subscriptionId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getOccurredOn() {
        return occurredOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setOccurredOn(Date occurredOn) {
        this.occurredOn = occurredOn;
    }

    public Long getOldStatusId() {
        return oldStatusId;
    }

    public void setOldStatusId(Long oldStatusId) {
        this.oldStatusId = oldStatusId;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public Long getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(Long subscriptionId) {
        this.subscriptionId = subscriptionId;
    }
}



