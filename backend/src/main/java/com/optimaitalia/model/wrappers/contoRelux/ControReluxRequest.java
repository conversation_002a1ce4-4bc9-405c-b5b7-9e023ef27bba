package com.optimaitalia.model.wrappers.contoRelux;

import lombok.Data;

@Data
public class ControReluxRequest {

    private Long clientId;

    private  String fatt;

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getFatt() {
        return fatt;
    }

    public void setFatt(String fatt) {
        this.fatt = fatt;
    }
}
