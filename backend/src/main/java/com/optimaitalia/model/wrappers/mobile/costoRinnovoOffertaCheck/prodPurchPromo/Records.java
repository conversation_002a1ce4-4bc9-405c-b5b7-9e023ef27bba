package com.optimaitalia.model.wrappers.mobile.costoRinnovoOffertaCheck.prodPurchPromo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.TariffDetailsDateDeserializer;

import java.util.Date;

public class Records {

    private Promotion promotion;

    @JsonDeserialize(using = TariffDetailsDateDeserializer.class)
    private Date terminatedOn;

    public Promotion getPromotion() {
        return promotion;
    }

    public void setPromotion(Promotion promotion) {
        this.promotion = promotion;
    }

    public Date getTerminatedOn() {
        return terminatedOn;
    }

    public void setTerminatedOn(Date terminatedOn) {
        this.terminatedOn = terminatedOn;
    }
}
