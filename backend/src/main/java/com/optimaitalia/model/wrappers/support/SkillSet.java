package com.optimaitalia.model.wrappers.support;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.NonNull;
import net.sf.oval.constraint.NotEmpty;


public class SkillSet {

    @NonNull
    private Integer id;

    @NotEmpty
    private String description;

    @JsonValue
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
