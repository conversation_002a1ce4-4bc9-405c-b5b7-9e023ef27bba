package com.optimaitalia.model.wrappers.mobile;

import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

public class TrafficDetailsRequest {

    @NotNull
    @NotEmpty
    private String msisdnId;

    @NotNull
    @NotEmpty
    private String fromDate;

    @NotNull
    @NotEmpty
    private String toDate;

    public String getMsisdnId() {
        return msisdnId;
    }

    public void setMsisdnId(String msisdnId) {
        this.msisdnId = msisdnId;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }
}
