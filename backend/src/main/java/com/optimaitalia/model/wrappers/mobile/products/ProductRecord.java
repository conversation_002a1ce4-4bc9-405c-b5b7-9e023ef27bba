package com.optimaitalia.model.wrappers.mobile.products;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.model.wrappers.mobile.conracts.ProductOption;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ProductRecord {

    private Long id;

    private Double activationPrice;

    private Boolean allowedAsBonus;

    private Date createdOn;

    private String name;

    private Integer parentId;

    private ProductMapping productMapping;

    private List<ProductOption> productOptions;

    private Integer renewalPeriod;

    private BigDecimal renewalPrice;

    private TariffPlan tariffPlan;

    private RenewalPeriodUnit renewalPeriodUnit;

    private Integer tariffPlanId;

    private Integer validityPeriod;

    private List<ProductIncompatibility> productIncompatibilities;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Double getActivationPrice() {
        return activationPrice;
    }

    public void setActivationPrice(Double activationPrice) {
        this.activationPrice = activationPrice;
    }

    public Boolean getAllowedAsBonus() {
        return allowedAsBonus;
    }

    public void setAllowedAsBonus(Boolean allowedAsBonus) {
        this.allowedAsBonus = allowedAsBonus;
    }

    public Date getCreatedOn() {
        return createdOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getRenewalPeriod() {
        return renewalPeriod;
    }

    public void setRenewalPeriod(Integer renewalPeriod) {
        this.renewalPeriod = renewalPeriod;
    }

    public BigDecimal getRenewalPrice() {
        return renewalPrice;
    }

    public void setRenewalPrice(BigDecimal renewalPrice) {
        this.renewalPrice = renewalPrice;
    }

    public TariffPlan getTariffPlan() {
        return tariffPlan;
    }

    public void setTariffPlan(TariffPlan tariffPlan) {
        this.tariffPlan = tariffPlan;
    }

    public Integer getTariffPlanId() {
        return tariffPlanId;
    }

    public void setTariffPlanId(Integer tariffPlanId) {
        this.tariffPlanId = tariffPlanId;
    }

    public Integer getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(Integer validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public List<ProductIncompatibility> getProductIncompatibilities() {
        return productIncompatibilities;
    }

    public void setProductIncompatibilities(List<ProductIncompatibility> productIncompatibilities) {
        this.productIncompatibilities = productIncompatibilities;
    }

    public ProductMapping getProductMapping() {
        return productMapping;
    }

    public void setProductMapping(ProductMapping productMapping) {
        this.productMapping = productMapping;
    }

    public List<ProductOption> getProductOptions() {
        return productOptions;
    }

    public void setProductOptions(List<ProductOption> productOptions) {
        this.productOptions = productOptions;
    }

    public RenewalPeriodUnit getRenewalPeriodUnit() {
        return renewalPeriodUnit;
    }

    public void setRenewalPeriodUnit(RenewalPeriodUnit renewalPeriodUnit) {
        this.renewalPeriodUnit = renewalPeriodUnit;
    }
}
