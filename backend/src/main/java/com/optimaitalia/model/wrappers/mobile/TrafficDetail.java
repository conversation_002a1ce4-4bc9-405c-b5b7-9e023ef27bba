package com.optimaitalia.model.wrappers.mobile;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.TariffDetailsDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

public class TrafficDetail {

    private String service;

    private String calledNumber;

    @JsonDeserialize(using = TariffDetailsDateDeserializer.class)
    private Date answerTime;

    @JsonDeserialize(using = TariffDetailsDateDeserializer.class)
    private Date disconnectTime;

    private BigDecimal amount;

    private Integer duration;

    private Double usedMegabytes;

    private String destination;


    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getCalledNumber() {
        return calledNumber;
    }

    public void setCalledNumber(String calledNumber) {
        this.calledNumber = calledNumber;
    }


    public Date getAnswerTime() {
        return answerTime;
    }


    public void setAnswerTime(Date answerTime) {
        this.answerTime = answerTime;
    }

    public Date getDisconnectTime() {
        return disconnectTime;
    }


    public void setDisconnectTime(Date disconnectTime) {
        this.disconnectTime = disconnectTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }



    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public Double getUsedMegabytes() {
        return usedMegabytes;
    }

    public void setUsedMegabytes(Double usedMegabytes) {
        this.usedMegabytes = usedMegabytes;
    }
}
