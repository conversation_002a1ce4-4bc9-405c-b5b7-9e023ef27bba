package com.optimaitalia.model.wrappers.mobile.conracts;

import com.optimaitalia.model.wrappers.mobile.subscriptions.Counter;

public class Option {

    private Integer id;

    private Double amount;

    private Counter counter;

    private Double liveAmount;

    private Integer counterId;

    private Boolean dynamicAmount;

    private String name;

    private Boolean solelyAvailable;

    private Unit unit;

    private Integer unitId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Counter getCounter() {
        return counter;
    }

    public void setCounter(Counter counter) {
        this.counter = counter;
    }

    public Double getLiveAmount() {
        return liveAmount;
    }

    public void setLiveAmount(Double liveAmount) {
        this.liveAmount = liveAmount;
    }

    public Integer getCounterId() {
        return counterId;
    }

    public void setCounterId(Integer counterId) {
        this.counterId = counterId;
    }

    public Boolean getDynamicAmount() {
        return dynamicAmount;
    }

    public void setDynamicAmount(Boolean dynamicAmount) {
        this.dynamicAmount = dynamicAmount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getSolelyAvailable() {
        return solelyAvailable;
    }

    public void setSolelyAvailable(Boolean solelyAvailable) {
        this.solelyAvailable = solelyAvailable;
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
}
