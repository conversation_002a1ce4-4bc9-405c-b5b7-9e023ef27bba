package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ActivatePayPalAutoricaricaRequest {
    @JsonProperty("TipoPagamento")
    private String TipoPagamento;
    @JsonProperty("SistemaChiamante")
    private String SistemaChiamante;
    @JsonProperty("Data")
    private String Data;
    @JsonProperty("TotalePagamento")
    private Integer TotalePagamento;
    @JsonProperty("Vendite")
    private String Vendite;
    @JsonProperty("Fatture")
    private String Fatture;
    @JsonProperty("Dilazioni")
    private String Dilazioni;
    @JsonProperty("Ricariche")
    private String Ricariche;
    @JsonProperty("URL_OK")
    private String URL_OK;
    @JsonProperty("URL_KO")
    private String URL_KO;
    @JsonProperty("CodiceCliente")
    private String CodiceCliente;
    @JsonProperty("RagioneSociale")
    private String RagioneSociale;
    @JsonProperty("PIVA")
    private String PIVA;
    @JsonProperty("CF")
    private String CF;
    @JsonProperty("Tokenize")
    private String Tokenize;
    @JsonProperty("IdOrdineEsterno")
    private String IdOrdineEsterno;
    @JsonProperty("UrlCallback")
    private String UrlCallback;
    @JsonProperty("CodificaCliente")
    private String CodificaCliente;
    @JsonProperty("AddInfo1")
    private String AddInfo1;
    @JsonProperty("AddInfo2")
    private String AddInfo2;
    @JsonProperty("AddInfo3")
    private String AddInfo3;
    @JsonProperty("TipoOperazione")
    private String TipoOperazione;
}
