package com.optimaitalia.model.wrappers.adsl;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class PodDetail {
    @JsonAlias("CostoAttivazione")
    public Double costoAttivazione;

    @JsonAlias("CostoDisattivazione")
    public Double costoDisattivazione;

    @JsonAlias("NumeroDiAppoggio")
    public String numeroDiAppoggio;

    @JsonAlias("NumeroRate")
    public Integer numeroRate;

    @JsonAlias("ProgressivoDiFatturazione")
    public Integer progressivoDiFatturazione;

    @JsonAlias("Opzioni")
    public List<Opzioni> opzioni;

    @JsonAlias("Rate")
    public List<String> rate;

    @JsonAlias("IdLinea")
    public Integer idLinea;

    @JsonAlias("StatoOperazione")
    public String statoOperazione;

    @JsonAlias("TipoOperazione")
    public String tipoOperazione;

    @JsonAlias("Stato")
    public String stato;

    @JsonAlias("NumeroRisorsa")
    public String numeroRisorsa;

    @JsonAlias("NumeroContatto")
    public String numeroContatto;

    @JsonAlias("IdProdottoAttivo")
    public Integer idProdottoAttivo;

    @JsonAlias("ProfiloProdottoAttivo")
    public String profiloProdottoAttivo;

    @JsonAlias("Sede")
    public Sede sede;

    @JsonAlias("IdTipoLinea")
    public Integer idTipoLinea;

    @JsonAlias("DescrizioneTipoLinea")
    public String descrizioneTipoLinea;

    @JsonAlias("IdContratto")
    public Integer idContratto;

    @JsonAlias("TipoContratto")
    public String tipoContratto;

    @JsonAlias("DataAttivazione")
    public String dataAttivazione;

    @JsonAlias("DataDisattivazione")
    public String dataDisattivazione;

    @JsonAlias("DataRichiestaAttivazione")
    public String dataRichiestaAttivazione;

    @JsonAlias("DataRichiestaDisattivazione")
    public String dataRichiestaDisattivazione;

    @JsonAlias("CodiceMigrazione")
    public String codiceMigrazione;

    @JsonAlias("CodiceMigrazionePrec")
    public String codiceMigrazionePrec;

    @JsonAlias("IdCedente")
    public String idCedente;

    @JsonAlias("IdVolturante")
    public String idVolturante;

    @JsonAlias("ProgressivoFatturazione")
    public Integer progressivoFatturazione;

    @JsonAlias("Naked")
    public Boolean naked;

    @JsonAlias("Migrazione")
    public String migrazione;

    @JsonAlias("CodUnivocoLinea")
    public String codUnivocoLinea;

    @JsonAlias("TipoxDSL")
    public String tipoxDSL;

    @JsonAlias("CliCollegati")
    public List<CLiCollegati> cliCollegati;

    @JsonAlias("DataDac")
    public String dataDac;
}
