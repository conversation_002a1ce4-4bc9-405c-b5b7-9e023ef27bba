package com.optimaitalia.model.wrappers.offer.request;

import com.optimaitalia.model.enums.AccountType;
import com.optimaitalia.model.enums.Service;
import net.sf.oval.constraint.Length;
import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

public class CheckTariffRequest {

    @NotNull
    private Service service;

    @NotNull
    private AccountType type;

//    @NotNull
//    @NotEmpty
//    @Length(min = 6, max = 6)
    private String tariffCode;

    public Service getService() {
        return service;
    }

    public void setService(Service service) {
        this.service = service;
    }

    public AccountType getType() {
        return type;
    }

    public void setType(AccountType type) {
        this.type = type;
    }

    public String getTariffCode() {
        return tariffCode;
    }

    public void setTariffCode(String tariffCode) {
        this.tariffCode = tariffCode;
    }
}
