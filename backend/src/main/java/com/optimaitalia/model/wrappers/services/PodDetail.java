package com.optimaitalia.model.wrappers.services;

import lombok.Data;

@Data
public class PodDetail {

    private Integer aliquotaIva;

    private Object aliquotaIvaAgevolata;

    private String appartienePI;

    private Integer cliente;

    private String codiceFiscale;

    private Object dataCambio;

    private String dataEsportazione;

    private String dataInizioPrelievo;

    private Object dataRecesso;

    private String descrizione;

    private Object disdettaPrec;

    private Integer fasce;

    private Object fineValidita;

    private Boolean flagMercatoLibero;

    private String grossista;

    private Integer idFatt;

    private Integer idIva;

    private Object idIvaAgevolata;

    private Integer idSedeFatturazione;

    private Integer idSedeOperativa;

    private Integer idTipoUso;

    private String inizioValidita;

    private Integer misuratore;

    private String partitaIva;

    private Double potDisp;

    private Integer potImp;

    private Double prelievoMedioGiornaliero;

    private Integer punto;

    private String sedeOperativa;

    private String subentro;

    private String tariffaTrasporto;

    private String tariffaTrasportoCode;

    private String tariffaTrasportoDesc;

    private Integer tensAlim;

    private Integer tensione;

    private String tensioneAlimentazioneDesc;

    private String tensioneDesc;

    private String tipo;

    private Object tipoOfferta;

    private String tipoUso;


}
