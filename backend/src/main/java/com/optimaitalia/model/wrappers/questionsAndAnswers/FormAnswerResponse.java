package com.optimaitalia.model.wrappers.questionsAndAnswers;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class FormAnswerResponse {

    private String positionNextOption;

    private Integer codice;

    private String descrizione;

    private List<FormAnswer> searchResponse;

    @JsonProperty("positionNextOption")
    public String getPositionNextOption() {
        return positionNextOption;
    }

    @JsonProperty("PositionNextOption")
    public void setPositionNextOption(String positionNextOption) {
        this.positionNextOption = positionNextOption;
    }

    @JsonProperty("codice")
    public Integer getCodice() {
        return codice;
    }

    @JsonProperty("Codice")
    public void setCodice(Integer codice) {
        this.codice = codice;
    }

    @JsonProperty("descrizione")
    public String getDescrizione() {
        return descrizione;
    }

    @JsonProperty("Descrizione")
    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @JsonProperty("searchResponse")
    public List<FormAnswer> getSearchResponse() {
        return searchResponse;
    }

    @JsonProperty("SearchResponse")
    public void setSearchResponse(List<FormAnswer> searchResponse) {
        this.searchResponse = searchResponse;
    }
}
