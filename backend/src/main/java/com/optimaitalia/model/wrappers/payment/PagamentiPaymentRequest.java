package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PagamentiPaymentRequest {

    @JsonProperty("SistemaChiamante")
    private String SistemaChiamante;

    @JsonProperty("CodiceCliente")
    private String CodiceCliente;

    @JsonProperty("CodificaCliente")
    private String CodificaCliente;

    @JsonProperty("CF")
    private String CF;

    @JsonProperty("PIVA")
    private String PIVA;

    @JsonProperty("TipoOperazione")
    private String TipoOperazione;

    @JsonProperty("URLNotify")
    private String URLNotify;

    @JsonProperty("URLError")
    private String URLError;

    @JsonProperty("URLCallbackChiam")
    private String URLCallbackChiam;

    @JsonProperty("IdOrdine")
    private String IdOrdine;

    @JsonProperty("AddInfo1")
    private String AddInfo1;

    @JsonProperty("AddInfo2")
    private String AddInfo2;

    @JsonProperty("AddInfo3")
    private String AddInfo3;

}
