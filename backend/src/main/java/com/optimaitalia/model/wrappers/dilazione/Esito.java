package com.optimaitalia.model.wrappers.dilazione;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class Esito {

    private String descrizione;

    private Integer esito;

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @JsonProperty("esito")
    public Integer getEsito() {
        return esito;
    }

    @JsonProperty("codice")
    public void setEsito(Integer esito) {
        this.esito = esito;
    }
}
