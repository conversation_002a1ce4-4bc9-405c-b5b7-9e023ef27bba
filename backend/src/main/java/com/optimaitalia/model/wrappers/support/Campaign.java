package com.optimaitalia.model.wrappers.support;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Campaign {

    FROM_9_10(4, "Fascia oraria 09-10"),
    FROM_10_11(5, "Fascia oraria 10-11"),
    FROM_11_12(6, "Fascia oraria 11-12"),
    FROM_12_13(7, "Fascia oraria 12-13"),
    FROM_13_14(8, "Fascia oraria 13-14"),
    FROM_14_15(9, "Fascia oraria 14-15"),
    FROM_15_16(10, "Fascia oraria 15-16"),
    FROM_16_17(11, "Fascia oraria 16-17"),
    FROM_17_18(12, "Fascia oraria 17-18"),
    FROM_18_19(13, "Fascia oraria 18-19");

    private final Integer campaignId;

    private final String description;

    Campaign(Integer campaignId, String description) {
        this.campaignId = campaignId;
        this.description = description;
    }

    private static Map<String, Campaign> FORMAT_MAP = Stream
            .of(Campaign.values())
            .collect(Collectors.toMap(s -> s.campaignId.toString(), Function.identity()));

    @JsonCreator
    public static Campaign fromString(String string) {
        return Optional
                .ofNullable(FORMAT_MAP.get(string))
                .orElseThrow(() -> new IllegalArgumentException(string));
    }


    @JsonValue
    public Integer getId() {
        return campaignId;
    }

    public String getDescription() {
        return description;
    }


    @Override
    public String toString() {
        return campaignId + " = " + description;
    }
}
