package com.optimaitalia.model.wrappers.homeData;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ListContoRelax {

    @JsonProperty("AnnoCompetenza")
    private Integer annoCompetenza;

    @JsonProperty("MeseCompetenza")
    private Integer meseCompetenza;

    @JsonProperty("Valore")
    private Double valore;

    public Integer getAnnoCompetenza() {
        return annoCompetenza;
    }

    public void setAnnoCompetenza(Integer annoCompetenza) {
        this.annoCompetenza = annoCompetenza;
    }

    public Integer getMeseCompetenza() {
        return meseCompetenza;
    }

    public void setMeseCompetenza(Integer meseCompetenza) {
        this.meseCompetenza = meseCompetenza;
    }

    public Double getValore() {
        return valore;
    }

    public void setValore(Double valore) {
        this.valore = valore;
    }
}
