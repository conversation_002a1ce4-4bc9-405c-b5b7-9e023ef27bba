package com.optimaitalia.model.wrappers.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;

import java.util.Date;

public class CustomerEmailInfo {

    private String objectId;

    private String subject;

    private String tipoImpegno;

    private String statoImpegno;

    private String priorita;

    private String autore;

    private Date dataCreazione;

    private Date dataScadenza;

    private Date fineEffettiva;

    private String tema;

    private String idOfferta;

    private String sender;

    @JsonProperty("objectId")
    public String getObjectId() {
        return objectId;
    }

    @JsonProperty("ObjectId")
    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    @JsonProperty("subject")
    public String getSubject() {
        return subject;
    }

    @JsonProperty("Subject")
    public void setSubject(String subject) {
        this.subject = subject;
    }

    @JsonProperty("type")
    public String getTipoImpegno() {
        return tipoImpegno;
    }

    @JsonProperty("TipoImpegno")
    public void setTipoImpegno(String tipoImpegno) {
        this.tipoImpegno = tipoImpegno;
    }

    @JsonProperty("status")
    public String getStatoImpegno() {
        return statoImpegno;
    }

    @JsonProperty("StatoImpegno")
    public void setStatoImpegno(String statoImpegno) {
        this.statoImpegno = statoImpegno;
    }

    @JsonProperty("priority")
    public String getPriorita() {
        return priorita;
    }

    @JsonProperty("Priorità")
    public void setPriorita(String priorita) {
        this.priorita = priorita;
    }

    @JsonProperty("author")
    public String getAutore() {
        return autore;
    }

    @JsonProperty("Autore")
    public void setAutore(String autore) {
        this.autore = autore;
    }

    @JsonProperty("creationDate")
    public Date getDataCreazione() {
        return dataCreazione;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataCreazione")
    public void setDataCreazione(Date dataCreazione) {
        this.dataCreazione = dataCreazione;
    }

    @JsonProperty("expiryDate")
    public Date getDataScadenza() {
        return dataScadenza;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataScadenza")
    public void setDataScadenza(Date dataScadenza) {
        this.dataScadenza = dataScadenza;
    }

    @JsonProperty("fineEffettiva")
    public Date getFineEffettiva() {
        return fineEffettiva;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("FineEffettiva")
    public void setFineEffettiva(Date fineEffettiva) {
        this.fineEffettiva = fineEffettiva;
    }

    @JsonProperty("theme")
    public String getTema() {
        return tema;
    }

    @JsonProperty("Tema")
    public void setTema(String tema) {
        this.tema = tema;
    }

    @JsonProperty("offerId")
    public String getIdOfferta() {
        return idOfferta;
    }

    @JsonProperty("IdOfferta")
    public void setIdOfferta(String idOfferta) {
        this.idOfferta = idOfferta;
    }

    @JsonProperty("sender")
    public String getSender() {
        return sender;
    }

    @JsonProperty("Sender")
    public void setSender(String sender) {
        this.sender = sender;
    }
}
