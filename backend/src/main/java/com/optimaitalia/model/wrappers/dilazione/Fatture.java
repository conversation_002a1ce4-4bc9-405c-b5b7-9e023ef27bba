package com.optimaitalia.model.wrappers.dilazione;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;

import java.util.Date;

public class Fatture {

    private Date dataDocumento;

    private Date dataScadenza;

    private Integer idInvoice;

    private Integer lineId;

    private Integer numeroDocumento;

    private Double scopertoDocumento;

    private Double totaleDocumento;

    private Integer transid;

    public Date getDataDocumento() {
        return dataDocumento;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataDocumento(Date dataDocumento) {
        this.dataDocumento = dataDocumento;
    }

    public Date getDataScadenza() {
        return dataScadenza;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataScadenza(Date dataScadenza) {
        this.dataScadenza = dataScadenza;
    }

    public Integer getIdInvoice() {
        return idInvoice;
    }

    public void setIdInvoice(Integer idInvoice) {
        this.idInvoice = idInvoice;
    }

    public Integer getLineId() {
        return lineId;
    }

    public void setLineId(Integer lineId) {
        this.lineId = lineId;
    }

    public Integer getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(Integer numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public Double getScopertoDocumento() {
        return scopertoDocumento;
    }

    public void setScopertoDocumento(Double scopertoDocumento) {
        this.scopertoDocumento = scopertoDocumento;
    }

    public Double getTotaleDocumento() {
        return totaleDocumento;
    }

    public void setTotaleDocumento(Double totaleDocumento) {
        this.totaleDocumento = totaleDocumento;
    }

    public Integer getTransid() {
        return transid;
    }

    public void setTransid(Integer transid) {
        this.transid = transid;
    }

}
