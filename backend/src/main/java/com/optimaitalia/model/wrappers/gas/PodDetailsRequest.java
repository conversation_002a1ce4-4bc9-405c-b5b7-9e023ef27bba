package com.optimaitalia.model.wrappers.gas;

import net.sf.oval.constraint.NotNull;

public class PodDetailsRequest {

    @NotNull
    private String pdr;

    @NotNull
    private String cliente;

    public String getPdr() {
        return pdr;
    }

    public void setPdr(String pdr) {
        this.pdr = pdr;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }
}
