package com.optimaitalia.model.wrappers.mobile.subscriptions;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class Activation {

    private String type;

    private Double amount;

    private Double liveAmount;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date activatedOn;
}
