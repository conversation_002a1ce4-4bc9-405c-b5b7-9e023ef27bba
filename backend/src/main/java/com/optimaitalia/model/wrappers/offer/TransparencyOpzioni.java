package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TransparencyOpzioni {

    private Long cliente;

    private Integer fatt;

    private Long idContratto;

    private Boolean offerteAttive;

    private String uriSp;

    @JsonProperty("cliente")
    public Long getCliente() {
        return cliente;
    }

    @JsonProperty("Cliente")
    public void setCliente(Long cliente) {
        this.cliente = cliente;
    }

    @JsonProperty("fatt")
    public Integer getFatt() {
        return fatt;
    }

    @JsonProperty("Fatt")
    public void setFatt(Integer fatt) {
        this.fatt = fatt;
    }

    @JsonProperty("idContratto")
    public Long getIdContratto() {
        return idContratto;
    }

    @JsonProperty("IDContratto")
    public void setIdContratto(Long idContratto) {
        this.idContratto = idContratto;
    }

    @JsonProperty("offerteAttive")
    public Boolean getOfferteAttive() {
        return offerteAttive;
    }

    @JsonProperty("OfferteAttive")
    public void setOfferteAttive(Boolean offerteAttive) {
        this.offerteAttive = offerteAttive;
    }

    @JsonProperty("uriSp")
    public String getUriSp() {
        return uriSp;
    }

    @JsonProperty("UriSP")
    public void setUriSp(String uriSp) {
        this.uriSp = uriSp;
    }
}
