package com.optimaitalia.model.wrappers.dilazione;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

public class Rate {

    private Date dataPagamento;

    private Date dataScadenza;

    private String idRata;

    private BigDecimal importoPagato;

    private BigDecimal importoRata;

    private Integer lineId;

    private Integer numeroRata;

    public Date getDataPagamento() {
        return dataPagamento;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataScadenza() {
        return dataScadenza;
    }

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setDataScadenza(Date dataScadenza) {
        this.dataScadenza = dataScadenza;
    }

    public String getIdRata() {
        return idRata;
    }

    public void setIdRata(String idRata) {
        this.idRata = idRata;
    }

    public BigDecimal getImportoPagato() {
        return importoPagato;
    }

    public BigDecimal getImportoRata() {
        return importoRata;
    }

    public void setImportoRata(BigDecimal importoRata) {
        this.importoRata = importoRata;
    }

    public void setImportoPagato(BigDecimal importoPagato) {
        this.importoPagato = importoPagato;
    }

    public Integer getLineId() {
        return lineId;
    }

    public void setLineId(Integer lineId) {
        this.lineId = lineId;
    }

    public Integer getNumeroRata() {
        return numeroRata;
    }

    public void setNumeroRata(Integer numeroRata) {
        this.numeroRata = numeroRata;
    }

    public Integer getStatoPagamentoRata() {
        return statoPagamentoRata;
    }

    public void setStatoPagamentoRata(Integer statoPagamentoRata) {
        this.statoPagamentoRata = statoPagamentoRata;
    }

    private Integer statoPagamentoRata;

}
