package com.optimaitalia.model.wrappers.segnalazione;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Signal {
    @JsonProperty("IncidentId")
    private String incidentId;
    private String customerid;
    @JsonProperty("OwningUser")
    private String owningUser;
    @JsonProperty("СaseOriginCode")
    private Float caseOriginCode;
    @JsonProperty("Title")
    private String title;
    @JsonProperty("AccountId")
    private String accountId;
    @JsonProperty("Description")
    private String description;
    @JsonProperty("TicketNumber")
    private String ticketNumber;
    @JsonProperty("IncidentStageCode")
    private Float incidentStageCode;
    @JsonProperty("StateCode")
    private Float stateCode;
    @JsonProperty("StatusCode")
    private Float statusCode;
    @JsonProperty("opt_triplettaid")
    private String opt_triplettaid;
    @JsonProperty("opt_casopadreid")
    private String opt_casopadreid;
    @JsonProperty("Opt_CodaId")
    private String opt_CodaId;
    @JsonProperty("Opt_AnagraficaCaratteristicaCasoIdId")
    private String opt_AnagraficaCaratteristicaCasoIdId;
    @JsonProperty("Opt_Tipoprodotto")
    private Float opt_Tipoprodotto;
    @JsonProperty("Opt_Servizio")
    private Float opt_Servizio;
    @JsonProperty("Opt_Figlioinorigine")
    private Float opt_Figlioinorigine;
    @JsonProperty("Opt_EsitoChiusuraId")
    private String opt_EsitoChiusuraId;
    @JsonProperty("Opt_ServizioDescrizione")
    private String opt_ServizioDescrizione;
    @JsonProperty("Opt_CasoPadreDescrizione")
    private String opt_CasoPadreDescrizione;
    @JsonProperty("Opt_CasoPadreTicketNumber")
    private String opt_CasoPadreTicketNumber;
    @JsonProperty("Opt_TipoProdottoDescrizione")
    private String opt_TipoProdottoDescrizione;
    @JsonProperty("Opt_CaratterizzazioneDescrizione")
    private String opt_CaratterizzazioneDescrizione;
    @JsonProperty("Opt_PropietarioDescrizione")
    private String opt_PropietarioDescrizione;
    @JsonProperty("Opt_OrigineCasoDescrizione")
    private String opt_OrigineCasoDescrizione;
    @JsonProperty("Opt_EsitoChiusuraDescrizione")
    private String opt_EsitoChiusuraDescrizione;
    @JsonProperty("Opt_Datacontrollo")
    private String opt_Datacontrollo;
    @JsonProperty("Opt_Utenze")
    private String opt_Utenze;
    @JsonProperty("Opt_Fatture")
    private String opt_Fatture;
    @JsonProperty("OueueTypeCode")
    private String oueueTypeCode;
    @JsonProperty("PrimaryUserId")
    private String primaryUserId;
    @JsonProperty("Opt_link_clienti")
    private String opt_link_clienti;
    @JsonProperty("IncidentDetails")
    private String incidentDetails;
    @JsonProperty("Activities")
    private String activities;
    @JsonProperty("Opt_Clibloccato")
    private String opt_Clibloccato;
    @JsonProperty("Opt_AnaDettaglioGuastoId")
    private String opt_AnaDettaglioGuastoId;
    @JsonProperty("Opt_dta_email")
    private String opt_dta_email;
    @JsonProperty("Opt_AnaEsitoNocId")
    private String opt_AnaEsitoNocId;
    @JsonProperty("CreatedByName")
    private String createdByName;
    @JsonProperty("ModifiedByName")
    private String modifiedByName;
    @JsonProperty("CreatedOn")
    private String createdOn;
    @JsonProperty("ModifiedOn")
    private String modifiedOn;
    @JsonProperty("CreatedBy")
    private String createdBy;
    @JsonProperty("ModifiedBy")
    private String modifiedBy;
    @JsonProperty("DeletionStateCode")
    private Float deletionStateCode;
    @JsonProperty("DescrizioneTripletta")
    private String descrizioneTripletta;

    public String getIncidentId() {
        return incidentId;
    }

    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }

    public String getCustomerid() {
        return customerid;
    }

    public void setCustomerid(String customerid) {
        this.customerid = customerid;
    }

    public String getOwningUser() {
        return owningUser;
    }

    public void setOwningUser(String owningUser) {
        this.owningUser = owningUser;
    }

    public Float getCaseOriginCode() {
        return caseOriginCode;
    }

    public void setCaseOriginCode(Float caseOriginCode) {
        this.caseOriginCode = caseOriginCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(String ticketNumber) {
        this.ticketNumber = ticketNumber;
    }

    public Float getIncidentStageCode() {
        return incidentStageCode;
    }

    public void setIncidentStageCode(Float incidentStageCode) {
        this.incidentStageCode = incidentStageCode;
    }

    public Float getStateCode() {
        return stateCode;
    }

    public void setStateCode(Float stateCode) {
        this.stateCode = stateCode;
    }

    public Float getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Float statusCode) {
        this.statusCode = statusCode;
    }

    public String getOpt_triplettaid() {
        return opt_triplettaid;
    }

    public void setOpt_triplettaid(String opt_triplettaid) {
        this.opt_triplettaid = opt_triplettaid;
    }

    public String getOpt_casopadreid() {
        return opt_casopadreid;
    }

    public void setOpt_casopadreid(String opt_casopadreid) {
        this.opt_casopadreid = opt_casopadreid;
    }

    public String getOpt_CodaId() {
        return opt_CodaId;
    }

    public void setOpt_CodaId(String opt_CodaId) {
        this.opt_CodaId = opt_CodaId;
    }

    public String getOpt_AnagraficaCaratteristicaCasoIdId() {
        return opt_AnagraficaCaratteristicaCasoIdId;
    }

    public void setOpt_AnagraficaCaratteristicaCasoIdId(String opt_AnagraficaCaratteristicaCasoIdId) {
        this.opt_AnagraficaCaratteristicaCasoIdId = opt_AnagraficaCaratteristicaCasoIdId;
    }

    public Float getOpt_Tipoprodotto() {
        return opt_Tipoprodotto;
    }

    public void setOpt_Tipoprodotto(Float opt_Tipoprodotto) {
        this.opt_Tipoprodotto = opt_Tipoprodotto;
    }

    public Float getOpt_Servizio() {
        return opt_Servizio;
    }

    public void setOpt_Servizio(Float opt_Servizio) {
        this.opt_Servizio = opt_Servizio;
    }

    public Float getOpt_Figlioinorigine() {
        return opt_Figlioinorigine;
    }

    public void setOpt_Figlioinorigine(Float opt_Figlioinorigine) {
        this.opt_Figlioinorigine = opt_Figlioinorigine;
    }

    public String getOpt_EsitoChiusuraId() {
        return opt_EsitoChiusuraId;
    }

    public void setOpt_EsitoChiusuraId(String opt_EsitoChiusuraId) {
        this.opt_EsitoChiusuraId = opt_EsitoChiusuraId;
    }

    public String getOpt_ServizioDescrizione() {
        return opt_ServizioDescrizione;
    }

    public void setOpt_ServizioDescrizione(String opt_ServizioDescrizione) {
        this.opt_ServizioDescrizione = opt_ServizioDescrizione;
    }

    public String getOpt_CasoPadreDescrizione() {
        return opt_CasoPadreDescrizione;
    }

    public void setOpt_CasoPadreDescrizione(String opt_CasoPadreDescrizione) {
        this.opt_CasoPadreDescrizione = opt_CasoPadreDescrizione;
    }

    public String getOpt_CasoPadreTicketNumber() {
        return opt_CasoPadreTicketNumber;
    }

    public void setOpt_CasoPadreTicketNumber(String opt_CasoPadreTicketNumber) {
        this.opt_CasoPadreTicketNumber = opt_CasoPadreTicketNumber;
    }

    public String getOpt_TipoProdottoDescrizione() {
        return opt_TipoProdottoDescrizione;
    }

    public void setOpt_TipoProdottoDescrizione(String opt_TipoProdottoDescrizione) {
        this.opt_TipoProdottoDescrizione = opt_TipoProdottoDescrizione;
    }

    public String getOpt_CaratterizzazioneDescrizione() {
        return opt_CaratterizzazioneDescrizione;
    }

    public void setOpt_CaratterizzazioneDescrizione(String opt_CaratterizzazioneDescrizione) {
        this.opt_CaratterizzazioneDescrizione = opt_CaratterizzazioneDescrizione;
    }

    public String getOpt_PropietarioDescrizione() {
        return opt_PropietarioDescrizione;
    }

    public void setOpt_PropietarioDescrizione(String opt_PropietarioDescrizione) {
        this.opt_PropietarioDescrizione = opt_PropietarioDescrizione;
    }

    public String getOpt_OrigineCasoDescrizione() {
        return opt_OrigineCasoDescrizione;
    }

    public void setOpt_OrigineCasoDescrizione(String opt_OrigineCasoDescrizione) {
        this.opt_OrigineCasoDescrizione = opt_OrigineCasoDescrizione;
    }

    public String getOpt_EsitoChiusuraDescrizione() {
        return opt_EsitoChiusuraDescrizione;
    }

    public void setOpt_EsitoChiusuraDescrizione(String opt_EsitoChiusuraDescrizione) {
        this.opt_EsitoChiusuraDescrizione = opt_EsitoChiusuraDescrizione;
    }

    public String getOpt_Datacontrollo() {
        return opt_Datacontrollo;
    }

    public void setOpt_Datacontrollo(String opt_Datacontrollo) {
        this.opt_Datacontrollo = opt_Datacontrollo;
    }

    public String getOpt_Utenze() {
        return opt_Utenze;
    }

    public void setOpt_Utenze(String opt_Utenze) {
        this.opt_Utenze = opt_Utenze;
    }

    public String getOpt_Fatture() {
        return opt_Fatture;
    }

    public void setOpt_Fatture(String opt_Fatture) {
        this.opt_Fatture = opt_Fatture;
    }

    public String getOueueTypeCode() {
        return oueueTypeCode;
    }

    public void setOueueTypeCode(String oueueTypeCode) {
        this.oueueTypeCode = oueueTypeCode;
    }

    public String getPrimaryUserId() {
        return primaryUserId;
    }

    public void setPrimaryUserId(String primaryUserId) {
        this.primaryUserId = primaryUserId;
    }

    public String getOpt_link_clienti() {
        return opt_link_clienti;
    }

    public void setOpt_link_clienti(String opt_link_clienti) {
        this.opt_link_clienti = opt_link_clienti;
    }

    public String getIncidentDetails() {
        return incidentDetails;
    }

    public void setIncidentDetails(String incidentDetails) {
        this.incidentDetails = incidentDetails;
    }

    public String getActivities() {
        return activities;
    }

    public void setActivities(String activities) {
        this.activities = activities;
    }

    public String getOpt_Clibloccato() {
        return opt_Clibloccato;
    }

    public void setOpt_Clibloccato(String opt_Clibloccato) {
        this.opt_Clibloccato = opt_Clibloccato;
    }

    public String getOpt_AnaDettaglioGuastoId() {
        return opt_AnaDettaglioGuastoId;
    }

    public void setOpt_AnaDettaglioGuastoId(String opt_AnaDettaglioGuastoId) {
        this.opt_AnaDettaglioGuastoId = opt_AnaDettaglioGuastoId;
    }

    public String getOpt_dta_email() {
        return opt_dta_email;
    }

    public void setOpt_dta_email(String opt_dta_email) {
        this.opt_dta_email = opt_dta_email;
    }

    public String getOpt_AnaEsitoNocId() {
        return opt_AnaEsitoNocId;
    }

    public void setOpt_AnaEsitoNocId(String opt_AnaEsitoNocId) {
        this.opt_AnaEsitoNocId = opt_AnaEsitoNocId;
    }

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public String getModifiedByName() {
        return modifiedByName;
    }

    public void setModifiedByName(String modifiedByName) {
        this.modifiedByName = modifiedByName;
    }

    public String getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(String createdOn) {
        this.createdOn = createdOn;
    }

    public String getModifiedOn() {
        return modifiedOn;
    }

    public void setModifiedOn(String modifiedOn) {
        this.modifiedOn = modifiedOn;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public Float getDeletionStateCode() {
        return deletionStateCode;
    }

    public void setDeletionStateCode(Float deletionStateCode) {
        this.deletionStateCode = deletionStateCode;
    }


    public String getDescrizioneTripletta() {
        return descrizioneTripletta;
    }

    public void setDescrizioneTripletta(String descrizioneTripletta) {
        this.descrizioneTripletta = descrizioneTripletta;
    }
}