package com.optimaitalia.model.wrappers.user.requests;

import net.sf.oval.constraint.*;

import javax.validation.constraints.NotEmpty;

public class PersonalDataChangeRequest extends UserDataChangeRequest {
    @NotNull
    @NotEmpty
    @Email(when = "groovy:_this.incidentEventCategory.getValue()=='Variazione E-mail'")
    @Digits(when = "groovy:_this.incidentEventCategory.getValue()=='Variazione Numero di telefono'")
    private String value;


    @MaxLength(50)
    private String oldValue;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }
}
