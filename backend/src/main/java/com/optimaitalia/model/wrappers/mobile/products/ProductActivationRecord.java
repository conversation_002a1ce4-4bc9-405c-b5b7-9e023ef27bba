package com.optimaitalia.model.wrappers.mobile.products;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.math.BigDecimal;
import java.util.Date;

public class ProductActivationRecord {

    private Long id;

    private Date purchasedOn;

    private Date expiresOn;

    private Date expiredOn;

    private Boolean active;

    private ProductRecord product;

    private BigDecimal chargedAmount;

    private Long parentId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getPurchasedOn() {
        return purchasedOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setPurchasedOn(Date purchasedOn) {
        this.purchasedOn = purchasedOn;
    }

    public Date getExpiresOn() {
        return expiresOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setExpiresOn(Date expiresOn) {
        this.expiresOn = expiresOn;
    }

    public Date getExpiredOn() {
        return expiredOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setExpiredOn(Date expiredOn) {
        this.expiredOn = expiredOn;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public ProductRecord getProduct() {
        return product;
    }

    public void setProduct(ProductRecord product) {
        this.product = product;
    }

    public BigDecimal getChargedAmount() {
        return chargedAmount;
    }

    public void setChargedAmount(BigDecimal chargedAmount) {
        this.chargedAmount = chargedAmount;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}
