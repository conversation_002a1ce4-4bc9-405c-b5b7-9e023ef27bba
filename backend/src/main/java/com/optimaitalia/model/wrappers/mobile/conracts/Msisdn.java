package com.optimaitalia.model.wrappers.mobile.conracts;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;

public class Msisdn {

    private Long id;

    private Date addedOn;

    private Object msisdnStsCh;

    private Integer msisdnStsChId;

    private Integer msisdnSubRangeId;

    private String type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getAddedOn() {
        return addedOn;
    }

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setAddedOn(Date addedOn) {
        this.addedOn = addedOn;
    }

    public Object getMsisdnStsCh() {
        return msisdnStsCh;
    }

    public void setMsisdnStsCh(Object msisdnStsCh) {
        this.msisdnStsCh = msisdnStsCh;
    }

    public Integer getMsisdnStsChId() {
        return msisdnStsChId;
    }

    public void setMsisdnStsChId(Integer msisdnStsChId) {
        this.msisdnStsChId = msisdnStsChId;
    }

    public Integer getMsisdnSubRangeId() {
        return msisdnSubRangeId;
    }

    public void setMsisdnSubRangeId(Integer msisdnSubRangeId) {
        this.msisdnSubRangeId = msisdnSubRangeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
