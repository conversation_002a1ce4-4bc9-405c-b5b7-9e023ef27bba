package com.optimaitalia.model.wrappers.voice;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;
import lombok.Data;

import java.util.Date;

@Data
public class Opzioni {

    public Integer idOpzione;

    public Integer codiceOpzioneBill;

    public String descrizioneOpzione;

    public String statoOpz;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public Date dataInizioValiditaOpz;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public Date dataFineValiditaOpz;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public Date dataRichiestaAttivazione;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public Date dataRichiestaDisattivazione;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public Date dataInvioCarrier;

}
