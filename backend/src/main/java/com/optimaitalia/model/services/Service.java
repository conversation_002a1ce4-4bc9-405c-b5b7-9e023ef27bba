package com.optimaitalia.model.services;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


public class Service {

    private Long id;

    private String name;

    private List<Utility> utilities;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("idServizio")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("serviceName")
    public String getName() {
        return name;
    }

    @JsonProperty("codiceServizio")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("utilities")
    public List<Utility> getUtilities() {
        return utilities;
    }

    @JsonProperty("utenze")
    public void setUtilities(List<Utility> utilities) {
        this.utilities = utilities;
    }
}
