package com.optimaitalia.model.services;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;

public class Utility {

    private Long id;

    private String utNumber;

    private String userCrmId;

    private Boolean integratedProduct;

    private String status;

    private Date startDate;

    private Date endDate;

    private Date firstActivationDate;

    private String scrap;

    private AdditionalInfo additionalInfo;

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("idUtenza")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("userCrmId")
    public String getUserCrmId() {
        return userCrmId;
    }

    @JsonProperty("idUtenzaCrm")
    public void setUserCrmId(String userCrmId) {
        this.userCrmId = userCrmId;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("statoUtenza")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("startDate")
    public Date getStartDate() {
        return startDate;
    }

    @JsonProperty("inizioValidita")
    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("endDate")
    public Date getEndDate() {
        return endDate;
    }

    @JsonProperty("fineValidita")
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("firstActivationDate")
    public Date getFirstActivationDate() {
        return firstActivationDate;
    }

    @JsonProperty("dataPrevistaAttivazione")
    @JsonDeserialize(using = ContractsDateDeserializer.class)
    public void setFirstActivationDate(Date firstActivationDate) {
        this.firstActivationDate = firstActivationDate;
    }

    @JsonProperty("scrap")
    public String getScrap() {
        return scrap;
    }

    @JsonProperty("descrizionePubblica")
    public void setScrap(String scrap) {
        this.scrap = scrap;
    }

    @JsonProperty("utNumber")
    public String getUserNumber() {
        return utNumber;
    }

    @JsonProperty("utenza")
    public void setUserNumber(String userNumber) {
        this.utNumber = userNumber;
    }

    @JsonProperty("integratedProduct")
    public Boolean getIntegratedProduct() {
        return integratedProduct;
    }

    @JsonProperty("prodottoIntegrato")
    public void setIntegratedProduct(Boolean integratedProduct) {
        this.integratedProduct = integratedProduct;
    }

    @JsonProperty("additionalInfo")
    public AdditionalInfo getAdditionalInfo() {
        return additionalInfo;
    }

    @JsonProperty("additionalInfo")
    public void setAdditionalInfo(AdditionalInfo additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
