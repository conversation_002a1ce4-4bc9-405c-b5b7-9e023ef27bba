package com.optimaitalia.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;

import java.util.Date;

public class ShipmentHeader {

    private String articleType;

    private String number;

    private String status;

    private Date modificationDate;

    private String causale;

    private String trackingNr;

    @JsonProperty("articleType")
    public String getArticleType() {
        return articleType;
    }

    @JsonProperty("tipoArticolo")
    public void setArticleType(String articleType) {
        this.articleType = articleType;
    }

    @JsonProperty("number")
    public String getNumber() {
        return number;
    }

    @JsonProperty("matricola")
    public void setNumber(String number) {
        this.number = number;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("stato")
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("modificationDate")
    public Date getModificationDate() {
        return modificationDate;
    }

    @JsonProperty("dataModificaStato")
    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    public void setModificationDate(Date modificationDate) {
        this.modificationDate = modificationDate;
    }

    @JsonProperty("causal")
    public String getCausale() {
        return causale;
    }

    @JsonProperty("causale")
    public void setCausale(String causale) {
        this.causale = causale;
    }

    public String getTrackingNr() {
        return trackingNr;
    }

    public void setTrackingNr(String trackingNr) {
        this.trackingNr = trackingNr;
    }
}
// {
//         "idDettaglio": 1000,
//         "matricola": null,
//         "tipoArticolo": "Sim_optima",
//         "stato": "SPEDIZIONE CREATA",
//         "trackingNr": null,
//         "dataModificaStato": "13/06/2017",
//         "riftipo": "OPR_ID",
//         "rifvalore": "706797",
//         "rifSAPnumero": null,
//         "causale": null,
//         "infoaggiuntive": "{\"msisdn\":null,\"msisdnMnp\":null,\"offertaCommerciale\":\"\"}",
//         "riconoscimento": null,
//         "msisdn": null
//         }