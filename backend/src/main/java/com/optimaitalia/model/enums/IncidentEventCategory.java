package com.optimaitalia.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum IncidentEventCategory {

    CHANGE_EMAIL("Variazione E-mail"),
    CHANGE_TELEPHONE_NUMBER("Variazione Numero di telefono"),
    CHANGE_BILLING_ADDRESS("Variazione Indirizzo di fatturazione"),
    CHANGE_OFFICE_ADDRESS("Variazione Sede legale"),
    CHANGE_SOCIAL_NAME("Variazione Ragione Sociale"),
    CHANGE_INVOICE_SHIPPING_METHOD("Variazione Modalità di spedizione fattura"),
    INSERT_NEW_EE("Inserimento autolettura EE"),
    INSERT_NEW_GAS("Inserimento autolettura Gas"),
    RIMODULAZIONE_OFFERTA("Rimodulazione Offerta"),
    ATTIVAZIONE_NUOVI_PRODOTTI("Attivazione Nuovi Prodotti"),
    RECAPITI_CONSULENTE("Recapiti Consulente"),
    DASH_BUTTON("Dash Button"),
    PEC_VARIATION("Variazione PEC"),
    CHANGE_RECIPIENT_CODE("Variazione codice destinatario"),
    PAGAMENTO_SALDO_NEGATIVO("Pagamento Saldo Negativo"),
    TAGLIA("Taglia"),
    DIZATTIVAZIONE("Disattivazione"),
    PROMOMESI3("Promo3Mesi"),
    MODIFICA_SCANDEZA_FATTURA ("Modifica scadenza fattura"),
    POST_ATTIVAZIONE ("Post Attivazione"),
    VISIT_AGENT("Visita Agente"),
    CONTRACT_COPY("Invio Copia Contratto"),
    ALLEGO_PAGAMENTO("Incassi da fax"),
    CROSS_SELLING("Senza Caratteristica"),
    REDEEM_ENERGY_VOUCHER("Energy Card"),
    REDEEM_MOBILE_VOUCHER("Mobile Card");

    private final String incidentEventCategory;

    IncidentEventCategory(String incidentEventCategory) {
        this.incidentEventCategory = incidentEventCategory;
    }

    @JsonValue
    public String getValue() {
        return incidentEventCategory;
    }


    @Override
    public String toString() {
        return "IncidentEventCategory{" +
                "incidentEventCategory='" + incidentEventCategory + '\'' +
                '}';
    }
}
