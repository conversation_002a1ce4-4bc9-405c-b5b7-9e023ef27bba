package com.optimaitalia.model.enums;


public enum ServiceType {

    ENERGIA(4), GAS(8), CHANGE_USER_DATA(6);

    private final Integer serviceType;

    ServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public Integer getValue() {
        return this.serviceType;
    }

    @Override
    public String toString() {
        return String.valueOf(serviceType);
    }
}
