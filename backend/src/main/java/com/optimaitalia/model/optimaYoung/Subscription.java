package com.optimaitalia.model.optimaYoung;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Subscription {
    private String subscriptionId;
    private Long msisdnid;
    private Boolean isAvailableOptimaYoung;
    private List<Coupon> coupons;

    @JsonProperty("subscriptionId")
    public String getSubscriptionId() {
        return subscriptionId;
    }

    @JsonProperty("SubscriptionId")
    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    @JsonProperty("msisdnid")
    public Long getMsisdnid() {
        return msisdnid;
    }

    @JsonProperty("Msisdn")
    public void setMsisdnid(Long msisdnid) {
        this.msisdnid = msisdnid;
    }

    @JsonProperty("isAvailableOptimaYoung")
    public Boolean getIsAvailableOptimaYoung() {
        return isAvailableOptimaYoung;
    }

    @JsonProperty("IsEligibileOptimaYoung")
    public void setIsAvailableOptimaYoung(Boolean isAvailableOptimaYoung) {
        this.isAvailableOptimaYoung = isAvailableOptimaYoung;
    }

    @JsonProperty("coupons")
    public List<Coupon> getCoupons() {
        return coupons;
    }

    @JsonProperty("Coupons")
    public void setCoupons(List<Coupon> coupons) {
        this.coupons = coupons;
    }
}
