package com.optimaitalia.model.optimaYoung;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Coupon {

    private String couponCode;

    private String couponType;

    private String receivingDate;

    private String expiryDate;

    private String awardDate;

    private Integer numTarget;

    private Integer numUses;

    private Integer numComplete;

    @JsonProperty("couponCode")
    public String getCouponCode() {
        return couponCode;
    }

    @JsonProperty("CodiceCoupon")
    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @JsonProperty("couponType")
    public String getCouponType() {
        return couponType;
    }

    @JsonProperty("TipoCoupon")
    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    @JsonProperty("receivingDate")
    public String getReceivingDate() {
        return receivingDate;
    }

    @JsonProperty("DataErogazione")
    public void setReceivingDate(String receivingDate) {
        this.receivingDate = receivingDate;
    }

    @JsonProperty("expiryDate")
    public String getExpiryDate() {
        return expiryDate;
    }

    @JsonProperty("DataScadenza")
    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    @JsonProperty("awardDate")
    public String getAwardDate() {
        return awardDate;
    }

    @JsonProperty("DataPremio")
    public void setAwardDate(String awardDate) {
        this.awardDate = awardDate;
    }

    @JsonProperty("numTarget")
    public Integer getNumTarget() {
        return numTarget;
    }

    @JsonProperty("NumObiettivo")
    public void setNumTarget(Integer numTarget) {
        this.numTarget = numTarget;
    }

    @JsonProperty("numUses")
    public Integer getNumUses() {
        return numUses;
    }

    @JsonProperty("NumUtilizzioni")
    public void setNumUses(Integer numUses) {
        this.numUses = numUses;
    }

    @JsonProperty("numComplete")
    public Integer getNumComplete() {
        return numComplete;
    }

    @JsonProperty("NumCompleti")
    public void setNumComplete(Integer numComplete) {
        this.numComplete = numComplete;
    }
}
