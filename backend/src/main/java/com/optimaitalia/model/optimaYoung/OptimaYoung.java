package com.optimaitalia.model.optimaYoung;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class OptimaYoung {
    private Long clientId;
    private List<Subscription> subscriptions;

    @JsonProperty("clientId")
    public Long getClientId() {
        return clientId;
    }

    @JsonProperty("IdCliente")
    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    @JsonProperty("subscriptions")
    public List<Subscription> getSubscriptions() {
        return subscriptions;
    }

    @JsonProperty("Subscriptions")
    public void setSubscriptions(List<Subscription> subscriptions) {
        this.subscriptions = subscriptions;
    }
}
