package com.optimaitalia.model.prospectUser.emailToSupport;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ProspectUserEmailToSupport {
    private List<Destination> destinations;
    private List<Destination> ccs;
    private String ccns;
    private String bodyText;
    private String bodyContentType;
    private String objectText;
    private Boolean isPec;
    private List<Attachment> attachmentsEmailStream;
}
