package com.optimaitalia.model.prospectUser;

import lombok.Getter;
import lombok.Setter;

import java.sql.Date;

@Getter
@Setter
public class ProspectUserContract {

    private Integer idContratto;
    private Integer idGruppo;
    private Date dataArrivoFax;
    private Date dataImportazione;
    private Date dataStipula;
    private Date dataInvioAttivazione;
    private Integer idCliente;
    private String nome;
    private String cognome;
    private String email;
    private Integer servizio;
    private String utenza;
    private String icona;
    private Integer categorieCCN;
    private String invioEmail;
    private String documentiDaRecuperare;
    private String azioneQCC;
    private Integer motivazioneQCCKO;
    private Integer attivazioneParziale;
    private String serviziCestinati;
    private Boolean flagContrattoCestinato;
}
