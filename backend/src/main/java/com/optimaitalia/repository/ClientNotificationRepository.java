package com.optimaitalia.repository;

import com.optimaitalia.model.db.ClientNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ClientNotificationRepository extends JpaRepository<ClientNotification, Long> {

    @Query(value = "SELECT n from ClientNotification n WHERE n.clientId = ?1 ORDER BY n.insertDate DESC")
    List<ClientNotification> getAllClientNotifications(Long clientId);

    @Query(value = "SELECT n from ClientNotification n WHERE n.clientId IN ?1 ORDER BY n.insertDate DESC")
    List<ClientNotification> getAllClientNotificationsForUserList(List<Long> clientId);
}
