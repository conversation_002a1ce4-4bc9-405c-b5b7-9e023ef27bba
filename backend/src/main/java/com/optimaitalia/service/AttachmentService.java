package com.optimaitalia.service;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;


public interface AttachmentService {
    ResponseEntity sendIncidentWithAttachment(MultipartFile file, String incidentId, String message);

    ResponseEntity sendIncidentWithoutAttachment(String incidentId, String message);

    ResponseEntity sendIncidentWithMultiAttachment(MultipartFile[] files, String incidentId, String message);
}
