package com.optimaitalia.service;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.charts.ListaConsumiDettagliOre;
import com.optimaitalia.model.wrappers.services.EnergyDetailsByHoursRequest;
import com.optimaitalia.model.wrappers.services.InfoPod2G;
import com.optimaitalia.model.wrappers.services.PodDetail;
import com.optimaitalia.model.wrappers.services.PodDetailsRequest;

import java.io.IOException;
import java.util.List;
import java.util.Map;


public interface ElectricityService {

    List<PodDetail> findPodDetails(PodDetailsRequest podDetailsRequest) throws ValidateException;

    List<InfoPod2G> getPod2GInfo(String[] podDetailsRequest);

    List<Map> energyPointAdjustments(String clientId, Integer punto);

    List<ListaConsumiDettagliOre> energyDetailsByHours(EnergyDetailsByHoursRequest detailsByHoursRequest);

    byte[] getFile(EnergyDetailsByHoursRequest detailsByHoursRequest) throws IOException;
}
