package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.exception.NotificationNotFoundException;
import com.optimaitalia.model.db.ClientNotification;
import com.optimaitalia.repository.ClientNotificationRepository;
import com.optimaitalia.service.ClientNotificationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
@Transactional
public class ClientNotificationServiceImpl implements ClientNotificationService {

    private static final Logger logger = LogManager.getLogger(ClientNotificationServiceImpl.class);

    private final ClientNotificationRepository clientNotificationRepository;

    @Autowired
    public ClientNotificationServiceImpl(ClientNotificationRepository clientNotificationRepository) {
        this.clientNotificationRepository = clientNotificationRepository;
    }

    @Override
    public List<ClientNotification> getAllClientNotifications(Long clientId) {
        logger.info("Getting the List<ClientNotification> from ClientNotificationService");
        return clientNotificationRepository.getAllClientNotifications(clientId);
    }

    @Override
    public List<ClientNotification> getAllClientNotificationsForUserList(List<Long> clientId) {
        logger.info("Getting the List<ClientNotification> from ClientNotificationService for ClientList");
        return clientNotificationRepository.getAllClientNotificationsForUserList(clientId);
    }

    @Override
    public ClientNotification getClientNotificationById(Long clientId, Long id) {
        logger.info("Getting the ClientNotification with id: " + id);
        return clientNotificationRepository.findById(id)
                .orElseThrow(() -> new NotificationNotFoundException("Notification by id [" + id + "] was not found"));
    }

    @Override
    public ClientNotification createOrUpdateNotification(ClientNotification clientNotification) {
        logger.info("CreateOrUpdate ClientNotification for user with id: " + clientNotification.getClientId());
        return clientNotificationRepository.save(clientNotification);
    }

    @Override
    public void deleteNotificationById(Long id) {
        logger.info("Delete ClientNotification with id: " + id);
        clientNotificationRepository.deleteById(id);
    }
}
