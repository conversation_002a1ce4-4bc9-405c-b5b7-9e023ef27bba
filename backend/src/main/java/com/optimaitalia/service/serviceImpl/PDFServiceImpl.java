package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.invoice.Invoice;
import com.optimaitalia.model.wrappers.offer.TransparencyOpzioni;
import com.optimaitalia.service.InformationService;
import com.optimaitalia.service.PDFService;
import com.optimaitalia.service.SegnalazioneService;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PDFServiceImpl implements PDFService {

    private static final Logger logger = LogManager.getLogger(PDFServiceImpl.class);

    private final RestTemplate restTemplate;

    private final InformationService informationService;

    private final SecurityService securityService;

    private final ObjectMapper objectMapper;

    private SegnalazioneService segnalazioniService;

    private @Value("${restdata.urls.getPDF}")
    String urlGetPDF;

    private @Value("${restdata.urls.getFatturaNc}")
    String urlFatturaNc;

    @Value("${restdata.urls.contract-transparency-pdf-data}")
    private String contractTransparencyPdfDataUrl;


    @Autowired
    public PDFServiceImpl(RestTemplate restTemplate, InformationService informationService,
                          SecurityService securityService, ObjectMapper objectMapper, SegnalazioneService segnalazioniService) {
        this.restTemplate = restTemplate;
        this.informationService = informationService;
        this.securityService = securityService;
        this.objectMapper = objectMapper;
        this.segnalazioniService = segnalazioniService;
    }

    @Override
    public ResponseEntity downloadTrafficoVoceFile(Long invoiceId, String clientId) {
        logger.info("Initializing the download traffico voce pdf file for user with id {}. Obtaining invoices details.", clientId);
        List<Invoice> invoices = informationService.getInvoicesData(clientId);
        logger.info("Invoices details have been obtained. Obtaining file.");
        Optional<Invoice> chosenInvoice = invoices.stream()
                .filter(invoice -> invoice.getId().equals(invoiceId)).findFirst();
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> response = restTemplate
                .exchange(chosenInvoice.map(i -> urlGetPDF + i.getSpUriTrafficoVoce()).get(), HttpMethod.GET, entity, byte[].class);
        if (response != null && response.getBody() != null) {
            logger.info("File has been obtained.");
            HttpHeaders responseHeaders = new HttpHeaders();
            headers.setContentDisposition(response.getHeaders().getContentDisposition());
            return ResponseEntity.ok().headers(responseHeaders).contentType(MediaType.parseMediaType("application/pdf")).body(response.getBody());
        }
        logger.info("No file found for current invoice {}", invoiceId);
        return new ResponseEntity(HttpStatus.NOT_FOUND);
    }

    public ResponseEntity getFileFromResource(String name) {

        byte[] content;
        try (InputStream cpResource = PDFService.class.getResourceAsStream("/pdf/" + name)) {
            content = IOUtils.toByteArray(cpResource);
        } catch (IOException e) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity
                .ok()
                .contentLength(content.length)
                .contentType(MediaType.parseMediaType("application/pdf"))
                .body(content);
    }

    @Override
    public ResponseEntity getFile(Long invoiceId, String clientId) {
        logger.info("Initializing the download invoice pdf file for user with id {}. Obtaining invoices details.", clientId);
        int size;
        List<Invoice> invoices = informationService.getInvoicesData(clientId);
        logger.info("Invoices details have been obtained. Obtaining file.");
        Optional<Invoice> chosenInvoice = invoices.stream()
                .filter(invoice -> invoice.getId().equals(invoiceId)).findFirst();
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> response = restTemplate
                .exchange(chosenInvoice.map(i -> urlFatturaNc + i.getDownloadUrl()).get(), HttpMethod.GET, entity, byte[].class);
        byte[] content = response.getBody();
        if (content != null) {
            logger.info("File has been obtained.");
            size = content.length;

            headers = new HttpHeaders();
            headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.add("Pragma", "no-cache");
            headers.add("Expires", "0");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, response.getHeaders().getContentDisposition().getType());
            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(size)
                    .contentType(MediaType.parseMediaType("application/pdf"))
                    .body(content);
        }
        logger.info("No file found for current invoice {}", invoiceId);
        return new ResponseEntity(HttpStatus.NOT_FOUND);
    }


    @Override
    public ResponseEntity downloadTariffTransparencyPDF(String clientId, Long contractId) {
        logger.info("Initializing the download tariff transparency pdf file for user with id {}.Obtaining pdf file.", clientId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
        List<Object> criteriaList = new ArrayList<>();
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("Cliente", clientId);
        criteriaList.add(criteria);
        multiValueMap.put("Criteria", criteriaList);
        HttpEntity<MultiValueMap> httpEntity = new HttpEntity<>(multiValueMap, httpHeaders);
        Map body = restTemplate.postForEntity(this.contractTransparencyPdfDataUrl, httpEntity, Map.class).getBody();
        List<TransparencyOpzioni> opzioni = objectMapper.convertValue(body.get("Opzioni"), objectMapper.getTypeFactory()
                .constructCollectionType(List.class, TransparencyOpzioni.class));
        if (opzioni != null && !opzioni.isEmpty()) {
            TransparencyOpzioni transparencyOpzioni;
            if (contractId != null && opzioni.size() > 1) {
                List<TransparencyOpzioni> collect = opzioni.stream().filter(i -> i.getIdContratto() != null
                        && i.getIdContratto().equals(contractId)).collect(Collectors.toList());
                transparencyOpzioni = !collect.isEmpty() ? collect.get(0) : null;
            } else {
                transparencyOpzioni = opzioni.get(0);
            }

            if (transparencyOpzioni != null && transparencyOpzioni.getUriSp() != null) {
                logger.info("File has been obtained.");
                HttpHeaders headers = new HttpHeaders();
                headers.set("urlPdf", this.urlGetPDF + transparencyOpzioni.getUriSp());
                return ResponseEntity.ok().body(headers);
            }
        }
        logger.info("No file found for current contract {}", contractId);
        return new ResponseEntity(HttpStatus.NOT_FOUND);
    }

    @Override
    public ResponseEntity downloadSegnalazionePDF(String clientId, String fileName) {
        try {
            segnalazioniService.getSignals(clientId);
            logger.info("File has been obtained.");
            HttpHeaders pdfHeaders = new HttpHeaders();
            pdfHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
            HttpEntity<String> entity = new HttpEntity<>(pdfHeaders);
            ResponseEntity<byte[]> pdfResponse = restTemplate.exchange(this.urlGetPDF + fileName,
                    HttpMethod.GET, entity, byte[].class);
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_PDF).body(pdfResponse.getBody());
        } catch (JsonProcessingException e) {
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<?> downloadContractsPDF(String fileName) {
        HttpHeaders pdfHeaders = new HttpHeaders();
        pdfHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(pdfHeaders);
        ResponseEntity<byte[]> pdfResponse = restTemplate.exchange(this.urlGetPDF + fileName,
                HttpMethod.GET, entity, byte[].class);
        if (pdfResponse.getBody() != null) {
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_PDF).body(pdfResponse.getBody());
        } else {
            return new ResponseEntity(HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<?> downloadYearlyReportExcelFile(String clientId, String year) {
        HttpHeaders excelHeaders = new HttpHeaders();
        excelHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(excelHeaders);
        String endOfURL = UriComponentsBuilder
                .fromPath("RendicontazionePubblicaAmministrazione/RendicontazionePubblicaAmministrazione_{clientId}_{year}.xlsx")
                .buildAndExpand(clientId, year)
                .toUriString();
        ResponseEntity<byte[]> excelResponse = restTemplate.exchange(this.urlGetPDF + endOfURL,
                HttpMethod.GET, entity, byte[].class);
        if (excelResponse.getStatusCode() == HttpStatus.OK) {
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_OCTET_STREAM).body(excelResponse.getBody());
        } else if (excelResponse.getStatusCode() == HttpStatus.NOT_FOUND) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } else {
            return new ResponseEntity<>(excelResponse.getStatusCode());
        }
    }

    @Override
    public ResponseEntity<?> downloadInvoicePDF(String downloadUrl) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<byte[]> pdfResponse = restTemplate.exchange(this.urlFatturaNc + downloadUrl,
                HttpMethod.GET, entity, byte[].class);
        if (pdfResponse.getBody() != null) {
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_PDF).body(pdfResponse.getBody());
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<?> downloadCondominiYearlyReportExcelFile(String fiscalCode, String year) {
        HttpHeaders excelHeaders = new HttpHeaders();
        excelHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
        HttpEntity<String> entity = new HttpEntity<>(excelHeaders);
        String endOfURL = UriComponentsBuilder
                .fromPath("RendicontazioneCondomini/RendicontazioneCondomini_{fiscalCode}_{year}.xlsx")
                .buildAndExpand(fiscalCode, year)
                .toUriString();
        ResponseEntity<byte[]> excelResponse = restTemplate.exchange(this.urlGetPDF + endOfURL,
                HttpMethod.GET, entity, byte[].class);
        if (excelResponse.getStatusCode() == HttpStatus.OK) {
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_OCTET_STREAM).body(excelResponse.getBody());
        } else if (excelResponse.getStatusCode() == HttpStatus.NOT_FOUND) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } else {
            return new ResponseEntity<>(excelResponse.getStatusCode());
        }
    }
}
