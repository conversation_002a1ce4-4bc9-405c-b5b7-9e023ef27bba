package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.businessAgent.BusinessAgentRequest;
import com.optimaitalia.service.BusinessAgentService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Service
public class BusinessAgentServiceImp implements BusinessAgentService {

    private final SecurityService securityService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${restdata.urls.business-agent}")
    private String businessAgentLink;

    public BusinessAgentServiceImp(RestTemplate restTemplate, SecurityService securityService, ObjectMapper objectMapper) {
        this.securityService = securityService;
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public List getInformationAboutAgent(BusinessAgentRequest request) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<BusinessAgentRequest> httpEntity = new HttpEntity<>(request, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.businessAgentLink, HttpMethod.POST, httpEntity, Map.class);
        return objectMapper.convertValue(exchange.getBody().get("response"), List.class);
    }
}
