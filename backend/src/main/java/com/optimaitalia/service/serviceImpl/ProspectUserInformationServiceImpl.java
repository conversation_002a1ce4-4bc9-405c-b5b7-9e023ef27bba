package com.optimaitalia.service.serviceImpl;

import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.prospect.ForgotPasswordRequest;
import com.optima.security.model.prospect.ForgotPasswordResponse;
import com.optima.security.model.prospect.ProspectUserEntity;
import com.optima.security.processors.TokenService;
import com.optima.security.repository.ProspectUserRepository;
import com.optimaitalia.model.prospectUser.ProspectUserContractsResponse;
import com.optimaitalia.model.prospectUser.emailToSupport.ProspectUserEmailToSupport;
import com.optimaitalia.service.ProspectUserInformationService;
import com.optimaitalia.service.RestorePasswordEmailService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class ProspectUserInformationServiceImpl implements ProspectUserInformationService {

    @Value("${restdata.urls.contracts-by-cf}")
    private String prospectContractsUrl;
    @Value("${restdata.urls.prospect-email-support}")
    private String prospectSupportUrl;
    @Value("${restdata.urls.prospect-file-upload}")
    private String prospectUploadFileUrl;
    private final RestTemplate restTemplate;
    TokenService tokenService;
    private final ProspectUserRepository userRepository;
    private final RestorePasswordEmailService restorePasswordEmailService;
    private static final Logger logger = LogManager.getLogger(ProspectUserInformationServiceImpl.class);

    public ProspectUserInformationServiceImpl(RestTemplate restTemplate, ProspectUserRepository userRepository,
                                              @Qualifier("ResetPswTokenServiceImpl") TokenService tokenService,
                                              RestorePasswordEmailService restorePasswordEmailService) {
        this.restTemplate = restTemplate;
        this.userRepository = userRepository;
        this.tokenService = tokenService;
        this.restorePasswordEmailService = restorePasswordEmailService;
    }

    @Override
    public ProspectUserContractsResponse getContractsByCodiceFiscale(String codiceFiscale) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("@CodiceFiscale", codiceFiscale);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, httpHeaders);
        logger.info("Looking for contracts by Codice Fiscale");
        return this.restTemplate.exchange(this.prospectContractsUrl,
                HttpMethod.POST, httpEntity, ProspectUserContractsResponse.class).getBody();
    }

    @Override
    public Integer sendEmailToSupport(ProspectUserEmailToSupport email) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(email, httpHeaders);
        logger.info("Sending an email to support");
        return (Integer) Objects.requireNonNull(this.restTemplate.exchange(this.prospectSupportUrl, HttpMethod.POST, httpEntity, Map.class).getBody()).get("transmissionCode");
    }

    @Override
    public ResponseEntity<?> uploadFile(MultipartFile[] files, String contractId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        for (MultipartFile file : files) {
            String uploadUrl = UriComponentsBuilder.fromHttpUrl(this.prospectUploadFileUrl)
                    .buildAndExpand(Objects.requireNonNull(file.getOriginalFilename()).replace(" ", ""), contractId)
                    .toUriString();
            Map<String, Object> request = new HashMap<>();
            try {
                request.put("file", Base64.getEncoder().encodeToString(file.getBytes()));
            } catch (IOException e) {
                logger.error("Something went wrong with reading file - {}", file.getOriginalFilename());
            }
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            logger.info("Sending file to contract - {}", contractId);
            ResponseEntity<String> exchange = this.restTemplate.exchange(uploadUrl, HttpMethod.POST, entity, String.class);
            if (!exchange.toString().contains("caricato correttamente")) {
                logger.error("Something went wrong with sending files to contract - {}", contractId);
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Override
    public ForgotPasswordResponse restorePassword(ForgotPasswordRequest request) {
        logger.info("Searching for a user by email");
        ProspectUserEntity prospectUser = userRepository.findByEmail(request.getEmail());
        ForgotPasswordResponse forgotPasswordResponse = new ForgotPasswordResponse();
        if (prospectUser != null) {
            try {
                String token = tokenService.buildToken(prospectUser);
                userRepository.insertToken(prospectUser.getId(), token);
                forgotPasswordResponse.setStatus(200);
                logger.info("Token saved successfully");
                restorePasswordEmailService.sendEmailMessageToUser(prospectUser.getEmail(), token, true);
            } catch (JwtTokenException e) {
                forgotPasswordResponse.setStatus(500);
                forgotPasswordResponse.setMessage("Error when creating a token");
                logger.error("Error when creating a token", e);
            }
        } else {
            logger.info("The prospect user with email {} does not exist", request.getEmail());
            forgotPasswordResponse.setStatus(404);
            forgotPasswordResponse.setMessage("This email does not exist");
        }
        return forgotPasswordResponse;
    }
}
