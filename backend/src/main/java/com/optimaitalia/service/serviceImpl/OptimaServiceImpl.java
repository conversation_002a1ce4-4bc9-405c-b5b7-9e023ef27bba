package com.optimaitalia.service.serviceImpl;

import com.optima.security.model.UserEntity;
import com.optima.security.model.changeCustomerEmail.ChangeEmailRequest;
import com.optima.security.model.changeCustomerEmail.ChangeEmailResponse;
import com.optima.security.model.newUserService.RequestNewUserService;
import com.optima.security.model.newUserService.ResponseNewUserService;
import com.optima.security.model.updateUserDeleted.UpdateDeletedFieldBatchResponse;
import com.optima.security.model.updateUserDeleted.UpdateDeletedFieldRequest;
import com.optima.security.model.updateUserDeleted.UpdateDeletedFieldResponse;
import com.optima.security.repository.UserRepository;
import com.optima.security.repository.dao.UserDao;
import com.optima.security.util.OptimaSHAPasswd;
import com.optima.security.util.PasswordGenerator;
import com.optimaitalia.model.optimaResponseRequest.IsUserResponse;
import com.optimaitalia.service.OptimaService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class OptimaServiceImpl implements OptimaService {

    private final UserDao userDao;
    private final UserRepository userRepository;
    private final PasswordGenerator passwordGenerator;
    private final OptimaSHAPasswd optimaSHAPasswd;
    private static final Logger logger = LogManager.getLogger(OptimaServiceImpl.class);

    @Autowired
    public OptimaServiceImpl(UserDao userDao, UserRepository userRepository, PasswordGenerator passwordGenerator, OptimaSHAPasswd optimaSHAPasswd) {
        this.userDao = userDao;
        this.userRepository = userRepository;
        this.passwordGenerator = passwordGenerator;
        this.optimaSHAPasswd = optimaSHAPasswd;
    }

    @Override
    public IsUserResponse isUserExist(String userName) {
        UserEntity user = userDao.findByUserName(userName);
        return (user != null) ? new IsUserResponse(true, "Lo username e' disponibile!", true) :
                new IsUserResponse(false, "Lo username non e' disponibile!", true);
    }

    @Override
    public ResponseNewUserService addNewUser(RequestNewUserService user) {
        UserEntity userEntity = new UserEntity(user);
        String pwd = passwordGenerator.generate();
        userEntity.setPassword(optimaSHAPasswd.code(pwd));
        ResponseNewUserService responseNewUserService;
        if (userEntity.getUsername()!= null && userDao.findByUserName(userEntity.getUsername()) == null) {
            userRepository.save(userEntity);
            logger.info("A new user saved successfully");
            responseNewUserService = new ResponseNewUserService("UTENTE INSERITO CORRETTAMENTE", userEntity.getUsername(), pwd, true);
        } else {
            logger.error("Error - a new user didn't save");
            responseNewUserService = new ResponseNewUserService("ERRORE INSERIMENTO UTENTE", userEntity.getUsername(), "", false);
        }

        return responseNewUserService;
    }

    @Override
    public ChangeEmailResponse changeEmail(ChangeEmailRequest changeEmailRequest) {
        if (userDao.findByUserName(changeEmailRequest.getUsername()) != null) {
            userDao.updateEmail(changeEmailRequest.getUsername(), changeEmailRequest.getEmail());
            logger.info("Email has been changed");
            return new ChangeEmailResponse("Ok", changeEmailRequest.getUsername(), "Email has been changed", changeEmailRequest.getEmail());
        } else {
            logger.info("Not found a customer with this username");
            return new ChangeEmailResponse("Ko", changeEmailRequest.getUsername(), "Not found a customer with this username", "-");
        }
    }

    @Override
    public UpdateDeletedFieldBatchResponse updateDeletedFieldBatch(List<UpdateDeletedFieldRequest> updateDeletedFieldRequests) {
        List<UpdateDeletedFieldResponse> responses = new ArrayList<>();
        for (UpdateDeletedFieldRequest request : updateDeletedFieldRequests) {
            UpdateDeletedFieldResponse response = updateDeletedField(request);
            responses.add(response);
        }
        return new UpdateDeletedFieldBatchResponse(responses);
    }

    private UpdateDeletedFieldResponse updateDeletedField(UpdateDeletedFieldRequest updateDeletedFieldRequest) {
        if (userDao.findByUserName(updateDeletedFieldRequest.getUsername()) != null) {
            userDao.updateDeleted(updateDeletedFieldRequest.getUsername(), updateDeletedFieldRequest.getDeleted());
            logger.info("Deleted status has been updated for user: {}", updateDeletedFieldRequest.getUsername());
            return new UpdateDeletedFieldResponse("Ok", updateDeletedFieldRequest.getUsername(),
                    "Deleted status has been updated", updateDeletedFieldRequest.getDeleted());
        } else {
            logger.info("Not found a customer with this username: {}", updateDeletedFieldRequest.getUsername());
            return new UpdateDeletedFieldResponse("Ko", updateDeletedFieldRequest.getUsername(),
                    "Not found a customer with this username", null);
        }
    }
}
