package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.offer.*;
import com.optimaitalia.model.wrappers.offer.request.ChangePromoMeseRequest;
import com.optimaitalia.model.wrappers.offer.request.CheckTariffRequest;
import com.optimaitalia.model.wrappers.user.response.ChangePersonalDataResponse;
import com.optimaitalia.service.OffersService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OffersServiceImpl implements OffersService {

    private static final Logger logger = LogManager.getLogger(OffersServiceImpl.class);

    private final ResourceLoader resourceLoader;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    private final RestTemplate restTemplate;

    private final OvalValidator ovalValidator;

    @Value("${restdata.urls.initial-bonus-progress}")
    private String initialBonusProgressUrl;

    @Value("${restdata.urls.сlientiBundle}")
    private String clientBundleUrl;

    @Value("${restdata.urls.check-tariff}")
    private String checkTariffUrl;

    @Value("${restdata.urls.meseOff}")
    private String promoMeseOffUrl;

    @Value("${restdata.urls.change-promo-mese}")
    private String changePromoMeseUrl;

    @Value("${mese.codicePromo}")
    private String codicePromo;

    public OffersServiceImpl(ResourceLoader resourceLoader, ObjectMapper objectMapper, SecurityService securityService,
                             RestTemplate restTemplate, OvalValidator ovalValidator) {
        this.resourceLoader = resourceLoader;
        this.objectMapper = objectMapper;
        this.securityService = securityService;
        this.restTemplate = restTemplate;
        this.ovalValidator = ovalValidator;
    }


    @Override
    public InitialBonusProgress loadInitialBonusProgress(String clientId, String billingId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> request = new HashMap<>();
        request.put("IdCliente", clientId);
        request.put("IdFatt", billingId);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);
        Map body = restTemplate.exchange(initialBonusProgressUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        if (body != null) {
            return objectMapper.convertValue(body.get("Response"), InitialBonusProgress.class);
        }
        return new InitialBonusProgress();
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> governanceOfferClientBundleDetails(String clientId, String billingId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> request = new HashMap<>();
        request.put("Cliente", clientId);
        request.put("IdFatt", billingId);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);
        return restTemplate.exchange(clientBundleUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
    }

    @Override
    public List<ClientBundle> governanceOfferClientBundle(String clientId, String billingId) {
        Map<String, Object> bundleDetails = governanceOfferClientBundleDetails(clientId, billingId);
        if (bundleDetails != null && bundleDetails.get("ListClientiBundle") != null) {
            return objectMapper.convertValue(bundleDetails.get("ListClientiBundle"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ClientBundle.class));
        }
        return new ArrayList<>();
    }

    @Override
    public List<ClientBundleDetails> getClientBundleDetails(String clientId, String billingId) {
        return this.governanceOfferClientBundle(clientId, billingId).stream().flatMap(i -> i.getClientBundleDetails().stream())
                .filter(i -> i.getPdr() != null || i.getPod() != null).collect(Collectors.toList());
//        return this.governanceOfferClientBundle(clientId, billingId).stream().flatMap(i -> i.getClientBundleDetails().stream())
//                .filter(i -> i.getFinval() != null && (i.getPdr() != null || i.getPod() != null)).collect(Collectors.toList());
    }

    @Override
    public ResponseEntity checkYourTariff(CheckTariffRequest checkTariffRequest) throws ValidateException {
        ovalValidator.validate(checkTariffRequest);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("servizio", checkTariffRequest.getService().name().toLowerCase());
        map.add("tipologia", checkTariffRequest.getType().name().toLowerCase());
        if (checkTariffRequest.getService() != com.optimaitalia.model.enums.Service.VOCE) {
            map.add("codice", checkTariffRequest.getTariffCode());
        }
        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_HTML)
                .body(restTemplate.postForObject(this.checkTariffUrl, new HttpEntity<>(map, headers), String.class));
    }


    @Override
    public List<PromoMeseOffWrapper> promoMeseOff(String clientId) {
        logger.info("Obtaining list of promo mese off for client with id {}", clientId);
        if (clientId != null) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> request = new HashMap<>();
            request.put("idCliente", clientId);
            request.put("codicePromo", codicePromo);
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);

            List promoMeseList = restTemplate.exchange(promoMeseOffUrl, HttpMethod.POST, httpEntity, List.class).getBody();
            logger.debug("Promo mese off has been obtained.");
            if (promoMeseList != null && promoMeseList.size() > 0 && promoMeseList.get(0) != null) {

                List<PromoMeseOffWrapper> convertedPromos = objectMapper.convertValue(promoMeseList,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, PromoMeseOffWrapper.class));
                return convertedPromos;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public ChangePersonalDataResponse changePromoMeseOff(ChangePromoMeseRequest changePromoMeseRequest) throws JsonProcessingException {
        logger.info("Received request for change promo meses {}", changePromoMeseRequest);
        if (changePromoMeseRequest != null) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<?> httpEntity = new HttpEntity<>(objectMapper.writeValueAsString(changePromoMeseRequest), httpHeaders);

            return restTemplate.exchange(changePromoMeseUrl, HttpMethod.POST, httpEntity, ChangePersonalDataResponse.class).getBody();
        }
        return new ChangePersonalDataResponse();
    }
}
