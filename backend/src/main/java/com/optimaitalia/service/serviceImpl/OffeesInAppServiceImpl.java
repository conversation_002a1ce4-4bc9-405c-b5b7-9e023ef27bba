package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.offer5g.Offer5GRequest;
import com.optimaitalia.model.offer5g.Offer5GResponse;
import com.optimaitalia.service.OffeesInAppService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class OffeesInAppServiceImpl implements OffeesInAppService {

    private static final Logger logger = LogManager.getLogger(OffeesInAppServiceImpl.class);

    @Value("${restdata.urls.app-offer-5G}")
    private String offer5GUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final SecurityService securityService;

    @Override
    public Offer5GResponse getOffer5GData(String clientId, String subscriptionId) {
        try {
            Offer5GRequest request = new Offer5GRequest(Long.parseLong(clientId), Long.parseLong(subscriptionId));

            HttpEntity<Offer5GRequest> httpEntity = new HttpEntity<>(request, createHeaders());

            logger.info("Fetching 5G offer data for client id: {} and subscription id: {}", clientId, subscriptionId);

            ResponseEntity<Map> exchange = restTemplate.exchange(offer5GUrl, HttpMethod.POST, httpEntity, Map.class);

            return objectMapper.convertValue(exchange.getBody().get("response"), Offer5GResponse.class);
        } catch (Exception e) {
            logger.error("Error fetching 5G offer data for client id: {} and subscription id: {}", clientId, subscriptionId, e);
            throw new RuntimeException("Failed to fetch 5G offer data", e);
        }
    }

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String token = securityService.getToken().getAccessToken();
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }
}
