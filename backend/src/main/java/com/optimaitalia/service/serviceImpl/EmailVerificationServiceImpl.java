package com.optimaitalia.service.serviceImpl;

import com.optima.security.model.UserEntity;
import com.optima.security.repository.dao.UserDao;
import com.optimaitalia.service.EmailVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EmailVerificationServiceImpl implements EmailVerificationService {
    @Autowired
    private UserDao userDao;


    @Override
    public List<UserEntity> emailAndUsernameExists(String email) {
        return userDao.findAllUsersByEmail(email);
    }
}
