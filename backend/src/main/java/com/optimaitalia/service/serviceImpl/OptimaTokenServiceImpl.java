package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.service.OptimaTokenService;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Date;

@Service
public class OptimaTokenServiceImpl implements OptimaTokenService {

    @Value("${optima.security.token.wso2.secret}")
    private String wso2TokenSecret;

    @Value("${optima.security.token.wso2.expiration-time:#{60*30*1000}}")
    private Long tokenExpirationTime;

    @Override
    public String generateWso2Token(String clientId, String ipAddress)  {
        return Jwts.builder()
                .claim("system", "selfcare")
                .claim("user", clientId)
                .claim("ip", ipAddress)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + tokenExpirationTime))
                .signWith(SignatureAlgorithm.HS512, wso2TokenSecret.getBytes(StandardCharsets.UTF_8))
                .compact();
    }
}