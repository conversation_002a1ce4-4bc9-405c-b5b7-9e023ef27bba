package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.gas.*;
import com.optimaitalia.service.GasService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class GasServiceImpl implements GasService {

    private static final Logger logger = LogManager.getLogger(GasServiceImpl.class);

    private final RestTemplate sslRestTemplate;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    private final OvalValidator ovalValidator;

    @Value("${restdata.urls.gas-pod-details}")
    private String podDetailsUrl;

    @Value("${restdata.urls.gas-pdr-additional-data}")
    private String pdrAdditionalDataUrl;

    @Value("${restdata.urls.gas-point-adjustments}")
    private String gasPointAdjustmentsUrl;

    public GasServiceImpl(RestTemplate sslRestTemplate, ObjectMapper objectMapper, SecurityService securityService, OvalValidator ovalValidator) {
        this.sslRestTemplate = sslRestTemplate;
        this.objectMapper = objectMapper;
        this.securityService = securityService;
        this.ovalValidator = ovalValidator;
    }


    @Override
    public List<PodDetail> findPodDetails(List<PodDetailsRequest> podDetails) {
        logger.info("Obtaining pdr details for request: {}", podDetails);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("crmGetInfoPdrByPdrRequestDTO", podDetails);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
        Map response = sslRestTemplate.exchange(this.podDetailsUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        logger.info("Pod details has been obtained.");
        if (response != null && response.get("response") != null) {
            return objectMapper.convertValue(((Map) response.get("response")).get("infoPdr"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, PodDetail.class));
        }
        return new ArrayList<>();
    }

    @Override
    public List<PodDetail> findPodDetails(PodDetailsRequest podDetails) throws ValidateException {
        ovalValidator.validate(podDetails);
        logger.info("Obtaining pdr details for user with id {}, and pdr {}", podDetails.getCliente(), podDetails.getPdr());
        List<PodDetailsRequest> details = new ArrayList<>();
        details.add(podDetails);
        return this.findPodDetails(details);
    }

    @Override
    public List<PdrAdditionalData> getPdrAdditionalData(PdrAdditionalDataRequest request) throws ValidateException {
        ovalValidator.validate(request);
        logger.info("Obtaining additional pdr data for request user {}", request);
        List<PdrAdditionalDataRequest> requestList = new ArrayList<>();
        requestList.add(request);
        return this.getPdrAdditionalData(requestList);
    }

    @Override
    public List<PdrAdditionalData> getPdrAdditionalData(List<PdrAdditionalDataRequest> additionalDataRequests) {
        logger.info("Obtaining list of additional pdr data for {}", additionalDataRequests);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("crmGetAltriDatiByPdrIdClientRequest", additionalDataRequests);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
        Map response = sslRestTemplate.exchange(this.pdrAdditionalDataUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        logger.info("List of additional pdr details has been obtained.");
        if (response != null && response.get("response") != null) {
            return objectMapper.convertValue(((Map) response.get("response")).get("altriDatiPdrResp"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, PdrAdditionalData.class));
        }
        return new ArrayList<>();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<GasPointAdjustment> gasPointAdjustments(String clientId, String pdr) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        Map<String, String> criteria = new HashMap<>();
        criteria.put("IdCliente", clientId);
        criteria.put("Pdr", pdr);
        body.put("Criteria", Collections.singletonList(criteria));
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
        Map response = sslRestTemplate.exchange(this.gasPointAdjustmentsUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        List<GasPointAdjustment> list = objectMapper.convertValue(response.get("ListaConguagli"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, GasPointAdjustment.class));
        return list.stream().sorted(Comparator.comparing(GasPointAdjustment::getDataLettura)).collect(Collectors.toList());
    }


}
