package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.service.AttachmentService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.Multipart;
import javax.mail.internet.MimeMultipart;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

@Service
public class AttachmentServiceImpl implements AttachmentService {

    private static final Logger logger = LogManager.getLogger(AttachmentServiceImpl.class);

    @Value("${restdata.urls.reportingAttachment}")
    private String attachmentUrl;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    private final RestTemplate restTemplate;

    public AttachmentServiceImpl(ObjectMapper objectMapper, SecurityService securityService,
                                 RestTemplate restTemplate) {
        this.objectMapper = objectMapper;
        this.securityService = securityService;
        this.restTemplate = restTemplate;
    }

    @Override
    public ResponseEntity sendIncidentWithAttachment(MultipartFile file, String incidentId, String message) {
        return sendIncident(file, incidentId, message);
    }

    @Override
    public ResponseEntity sendIncidentWithoutAttachment(String incidentId, String message) {
        return sendIncident(null, incidentId, message);
    }

    @Override
    public ResponseEntity sendIncidentWithMultiAttachment(MultipartFile[] files, String incidentId, String message) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> request = new HashMap<>();
        request.put("IdCaso", incidentId);

        request.put("Nota", adaptItalianSpecificSymbols(message));
        request.put("IsProprietario", "A292B8E4-58C6-DF11-9810-00259003B18C");
        if (files.length != 0) {
            Arrays.stream(files).forEach(file -> {
                try {
                    request.put("Titolo", "Aggiunta allegato per il caso " + incidentId);
                    request.put("FileBytes", file.getBytes());
                    request.put("FileName", file.getOriginalFilename());
                    HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);
                    logger.info("Request: "+ httpEntity.toString());
                    restTemplate.exchange(attachmentUrl, HttpMethod.POST, httpEntity, Map.class);
                    logger.info("Attach file with name: "+ file.getOriginalFilename());
                } catch (IOException e) {
                    logger.error("Error while trying to send email. Error: {0}", e);
                }
            });
        } else {
            request.put("Titolo", "Inserita nota da cliente da selfcare ");
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);
            restTemplate.exchange(attachmentUrl, HttpMethod.POST, httpEntity, Map.class);
        }
        logger.info("Incident has been sent. There was "+ files.length+ " attachment");
        return ResponseEntity.ok().build();
    }

    private ResponseEntity sendIncident(MultipartFile file, String incidentId, String message) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> request = new HashMap<>();
        request.put("IdCaso", incidentId);

        request.put("Nota", adaptItalianSpecificSymbols(message));
        request.put("IsProprietario", "A292B8E4-58C6-DF11-9810-00259003B18C");
        if (file != null && !file.isEmpty()) {
            try {
                request.put("Titolo", "Aggiunta allegato per il caso " + incidentId);
                request.put("FileBytes", file.getBytes());
                request.put("FileName", file.getOriginalFilename());
            } catch (IOException e) {
                logger.error("Error while trying to send email. Error: {0}", e);
            }
        } else {
            request.put("Titolo", "Inserita nota da cliente da selfcare ");
        }
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(request, httpHeaders);
        restTemplate.exchange(attachmentUrl, HttpMethod.POST, httpEntity, Map.class);
        logger.info("Email has been sent.");
        return ResponseEntity.ok().build();
    }

    private String adaptItalianSpecificSymbols(String message) {
        return message
                .replaceAll("à", "a'").replaceAll("À", "A'")
                .replaceAll("è", "e'").replaceAll("È", "E'")
                .replaceAll("ò", "o'").replaceAll("Ò", "O'")
                .replaceAll("ù", "u'").replaceAll("Ù", "U'");
    }
}
