package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.model.wrappers.sales.ClientSalesInf;
import com.optimaitalia.service.SalesInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class SalesInfoImpl implements SalesInfo {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${restdata.urls.salesMail}")
    private String sanderEmail;

    public SalesInfoImpl(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public List<String> getSalesMail(String id) {
        ResponseEntity<String> exchange = restTemplate.getForEntity(this.sanderEmail, String.class, id);
        String body = exchange.getBody();
        if (body != null) {
            try {
                String test = body.substring(1, body.length() - 1).replace("\\", "").replace("\\", "");
                List<ClientSalesInf> cl = Arrays.asList(objectMapper.readValue(test, ClientSalesInf[].class));
                List<String> temp = new ArrayList<>();
                cl.forEach(item ->
                        item.getoListSales().stream().filter(s -> s.getSalesTipe()
                                .toLowerCase().equals("Nuovo modello".toLowerCase()))
                                .forEach(element -> {
                                    temp.add(element.getEmail());
                                }));
                return temp;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}
