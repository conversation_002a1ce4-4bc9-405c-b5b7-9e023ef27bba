package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.enums.AccountType;
import com.optimaitalia.service.ScioltoService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class ScioltoServiceImpl implements ScioltoService {

    private static final Logger logger = LogManager.getLogger(ScioltoServiceImpl.class);

    private final SecurityService securityService;
    private final RestTemplate restTemplate;

    public ScioltoServiceImpl(SecurityService securityService, RestTemplate restTemplate) {
        this.securityService = securityService;
        this.restTemplate = restTemplate;
    }

    @Value("${restdata.urls.sciolto}")
    private String scioltoUrl;

    @Value("${restdata.urls.generalSciolito}")
    private String generalSciolitoUrl;

    @Override
    public ResponseEntity getScioltoInformation(String offer, AccountType cluster) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(httpHeaders);
        Integer clusterValue = cluster.equals(AccountType.BUSINESS) ? 1 : 2;
        Map response = restTemplate.exchange(scioltoUrl, HttpMethod.GET, httpEntity, Map.class, clusterValue, offer).getBody();
        logger.info("Sciolto information has been obtained.");
        return new ResponseEntity(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity getScioltoGeneralInformation(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(httpHeaders);
        Map response = restTemplate.exchange(generalSciolitoUrl, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
        logger.info("Sciolto general information has been obtained.");
        return new ResponseEntity(response, HttpStatus.OK);
    }
}
