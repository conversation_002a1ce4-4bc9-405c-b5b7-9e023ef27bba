package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.voucherCard.*;
import com.optimaitalia.service.VoucherCardService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class VoucherCardServiceImpl implements VoucherCardService {

    @Value("${restdata.urls.voucher-card-status}")
    private String voucherCardUrlStatus;

    @Value("${restdata.urls.voucher-card-redeem}")
    private String voucherCardUrlRedeem;

    @Value("${restdata.urls.voucher-card-top-up-sim}")
    private String voucherCardUrlTopUpSim;

    @Value("${restdata.urls.voucher-card-user-information}")
    private String voucherCardUrlUserInformation;

    @Value("${restdata.urls.voucher-card-contracts}")
    private String voucherCardUrlContracts;

    @Value("${restdata.urls.voucher-card-sim-balance}")
    private String voucherCardUrlSimBalance;

    private final RestTemplate restTemplate;
    private final SecurityService securityService;

    public VoucherCardServiceImpl(RestTemplate restTemplate, SecurityService securityService) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
    }

    @Override
    public VoucherCardResponse checkVoucherCard(String voucherCard) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);
        return restTemplate.exchange(voucherCardUrlStatus + voucherCard, HttpMethod.GET, httpEntity, VoucherCardResponse.class).getBody();
    }

    @Override
    public VoucherCardResponse redeemVoucherCard(RedeemVoucherRequest voucherRequest) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(voucherRequest, httpHeaders);
        return restTemplate.exchange(voucherCardUrlRedeem, HttpMethod.POST, httpEntity, VoucherCardResponse.class).getBody();
    }

    @Override
    public ResponseEntity<?> redeemVoucherCardTopUpSim(TopUpSimByVoucher topUpSimByVoucher) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> httpEntity = new HttpEntity<>(topUpSimByVoucher, httpHeaders);
        ResponseEntity<?> response = restTemplate.exchange(voucherCardUrlTopUpSim, HttpMethod.POST, httpEntity, Map.class);
        return response.getStatusCode() == HttpStatus.OK ? new ResponseEntity<>(response, HttpStatus.OK)
                : new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @Override
    public UsedVouchersInformation getInformationAboutUserCards(String typeCard, Long clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("@IdCliente", clientId);
        body.put("@IdTipoCard", typeCard);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, httpHeaders);
        return restTemplate.exchange(voucherCardUrlUserInformation, HttpMethod.GET, httpEntity, UsedVouchersInformation.class, clientId, typeCard).getBody();
    }

    @Override
    public ResponseEntity<?> getContractsInformation(Long clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("@idCliente", clientId);
        body.put("@cancellato", 0);
        body.put("@importato", 1);
        body.put("@idContratto", null);
        body.put("@ragioneSociale", null);
        body.put("@codiceFiscale", null);
        body.put("@telefono", null);
        body.put("@partitaIva", null);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, httpHeaders);
        return restTemplate.exchange(voucherCardUrlContracts, HttpMethod.POST, httpEntity, Map.class);
    }

    @Override
    public VoucherSimBalanceInformation getSimBalanceInformation(String clientId, String utenza) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("@IdCliente", clientId);
        body.put("@SubscriptionId", "");
        body.put("@Utenza", utenza);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, httpHeaders);
        return restTemplate.exchange(voucherCardUrlSimBalance, HttpMethod.POST, httpEntity, VoucherSimBalanceInformation.class).getBody();
    }
}
