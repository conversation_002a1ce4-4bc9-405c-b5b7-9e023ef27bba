package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.optimaYoung.OptimaYoung;
import com.optimaitalia.service.OptimaYoungService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class OptimaYoungServiceImpl implements OptimaYoungService {

    private static final Logger logger = LogManager.getLogger(OptimaYoungServiceImpl.class);

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    @Value("${restdata.urls.porta-un-amico}")
    private String portaUnAmicoUrl;

    @Override
    public OptimaYoung getInviteFriendsInfo(String clientId) {
        HttpEntity<Void> httpEntity = new HttpEntity<>(createHeaders());
        String url = portaUnAmicoUrl + clientId;
        logger.info("Fetching invite friends info for client id: {}", clientId);
        ResponseEntity<Map> exchange = restTemplate.exchange(url, HttpMethod.GET, httpEntity, Map.class);
        return objectMapper.convertValue(exchange.getBody().get("response"), OptimaYoung.class);
    }

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String token = securityService.getToken().getAccessToken();
        headers.set("Authorization", "Bearer " + token);
        return headers;
    }
}
