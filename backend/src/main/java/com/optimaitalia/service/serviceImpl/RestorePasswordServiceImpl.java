package com.optimaitalia.service.serviceImpl;

import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.UserEntity;
import com.optima.security.processors.TokenService;
import com.optima.security.repository.dao.UserDao;
import com.optima.security.util.OptimaSHAPasswd;
import com.optimaitalia.model.forgotPassword.ForgotPasswordFormRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordResponse;
import com.optimaitalia.service.EmailVerificationService;
import com.optimaitalia.service.RestorePasswordEmailService;
import com.optimaitalia.service.RestorePasswordService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class RestorePasswordServiceImpl implements RestorePasswordService {
    @Autowired
    @Qualifier("ResetPswTokenServiceImpl")
    TokenService tokenService;
    @Autowired
    OptimaSHAPasswd optimaSHAPasswd;
    @Autowired
    UserDao userDao;
    private static final Logger logger = LogManager.getLogger(RestorePasswordServiceImpl.class);
    private final EmailVerificationService emailVerificationService;
    private final RestorePasswordEmailService restorePasswordEmailService;

    public RestorePasswordServiceImpl(EmailVerificationService emailVerificationService, RestorePasswordEmailService restorePasswordEmailService) {
        this.emailVerificationService = emailVerificationService;
        this.restorePasswordEmailService = restorePasswordEmailService;

    }

    @Override
    public ForgotPasswordResponse restorePassword(ForgotPasswordRequest request) {
        ForgotPasswordResponse forgotPasswordResponse = new ForgotPasswordResponse();
        List<UserEntity> user = emailVerificationService.emailAndUsernameExists(request.getEmail());
        if (user != null && !user.isEmpty()) {
            // Check if all user accounts are deleted
            boolean allDeleted = true;
            for (UserEntity userEntity : user) {
                if (Boolean.FALSE.equals(userEntity.getDeleted())) {
                    allDeleted = false;
                    break;
                }
            }
            if (allDeleted) {
                forgotPasswordResponse.setStatus(403);
                forgotPasswordResponse.setMessage("deleted_account");
                logger.info("Password reset attempted for deleted account with email: {}", request.getEmail());
                return forgotPasswordResponse;
            }

            try {
                // Find the first non-deleted user to build the token
                UserEntity firstNonDeletedUser = user.stream()
                    .filter(u -> Boolean.FALSE.equals(u.getDeleted()))
                    .findFirst()
                    .orElse(user.get(0));
                String token = tokenService.buildToken(firstNonDeletedUser);

                // Only update token for non-deleted accounts
                for (UserEntity userEntity : user) {
                    if (Boolean.FALSE.equals(userEntity.getDeleted())) {
                        this.updatePswToken(userEntity, token);
                    }
                }
                forgotPasswordResponse = restorePasswordEmailService.sendEmailMessageToUser(request.getEmail(), token, false);

            } catch (JwtTokenException e) {
                e.printStackTrace();
            }

        } else {
            forgotPasswordResponse.setStatus(404);
            forgotPasswordResponse.setMessage("Questa email non esiste");

        }
        return forgotPasswordResponse;
    }

    @Override
    public void updatePswToken(UserEntity user, String token) {
        userDao.insertToken(token, user.getId());
    }

    @Override
    public List<UserEntity> findByToken(String token) {
        return userDao.findByToken(token);
    }

    @Transactional
    @Override
    public ForgotPasswordResponse resetPassword(ForgotPasswordFormRequest fpf, String token) {
        ForgotPasswordResponse forgotPasswordResponse = new ForgotPasswordResponse();
        List<UserEntity> user = userDao.findByToken(token);
        if (user.isEmpty()) {
            return createErrorResponse(404, "User not found", null);
        }
        for (UserEntity userEntity : user) {
            if (fpf.getPasswords().getPassword1().equals(fpf.getPasswords().getPassword2())) {
                if (optimaSHAPasswd.compare(userEntity.getOldPassword(), fpf.getPasswords().getPassword2())) {
                    return createErrorResponse(409, "New password must be different from the old password", userEntity.getUsername());
                }
                try {
                    String psw = optimaSHAPasswd.code(fpf.getPasswords().getPassword2());
                    userDao.updateUser(userEntity.getUsername(), psw, token, userEntity.getPassword());
                    userDao.updateToken(userEntity.getUsername(), token);
                    forgotPasswordResponse.setStatus(200);
                    forgotPasswordResponse.setMessage("Success");
                    logger.info("Success password saving for user - {}", userEntity.getUsername());
                } catch (Exception e) {
                    logger.error("Error while saving password for user: " + userEntity.getUsername(), e);
                    return createErrorResponse(400, "Something went wrong", userEntity.getUsername());
                }
            } else {
                return createErrorResponse(400, "Passwords don't match", null);
            }
        }
        return forgotPasswordResponse;
    }

    private ForgotPasswordResponse createErrorResponse(Integer status, String message, String username) {
        ForgotPasswordResponse response = new ForgotPasswordResponse();
        response.setStatus(status);
        response.setMessage(message);
        logger.error(message + " for user: " + username);
        return response;
    }
}
