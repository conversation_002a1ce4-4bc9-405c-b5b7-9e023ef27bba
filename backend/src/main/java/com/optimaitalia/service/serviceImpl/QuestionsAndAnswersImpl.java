package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.questionsAndAnswers.FormAnswer;
import com.optimaitalia.model.wrappers.questionsAndAnswers.FormAnswerResponse;
import com.optimaitalia.service.QuestionsAndAnswers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
public class QuestionsAndAnswersImpl implements QuestionsAndAnswers {

    private final RestTemplate restTemplate;

    private final SecurityService securityService;

    @Value("${restdata.urls.formAnswers}")
    private String formsAnswersUrl;

    public QuestionsAndAnswersImpl(RestTemplate restTemplate, SecurityService securityService) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
    }

    @Override
    public List<FormAnswer> getFormsAnswers() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json;charset=utf-8");
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<FormAnswer> entity = new HttpEntity<>(headers);
        return restTemplate.postForObject(formsAnswersUrl, entity, FormAnswerResponse.class).getSearchResponse();
    }
}
