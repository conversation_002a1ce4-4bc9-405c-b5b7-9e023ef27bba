package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.model.forgotPassword.ForgotPasswordResponse;
import com.optimaitalia.service.RestorePasswordEmailService;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

//import javax.mail.Message;

@Service
public class RestorePasswordEmailServiceImpl implements RestorePasswordEmailService {
    private final Session session;
    private static final Logger logger = LogManager.getLogger(RestorePasswordEmailServiceImpl.class);

    @Value("${optima.mail.service.support.email}")
    private String servicesSupportEmail;

    @Value("${optima.mail.service.sender.email}")
    private String senderEmail;
    @Value("${optima.domain}")
    private String domain;
    @Autowired
    private SpringTemplateEngine templateEngine;

    public RestorePasswordEmailServiceImpl(Session session) {
        this.session = session;
    }


    @Override
    public ForgotPasswordResponse sendEmailMessageToUser(String email, String token, Boolean isProspectUser) {
        try {
            logger.info("generazione messaggio invio cambio passwd");
            MimeMessage message = new MimeMessage(session);
            MimeMessageHelper helper = new MimeMessageHelper(message,
                    MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                    StandardCharsets.UTF_8.name());

            Context context = new Context();
            Map<String, Object> model = new HashMap<>();
            if (isProspectUser) {
                model.put("link", domain + "/api/prospect/resetPassword/" + token);
            } else {
                model.put("link", domain + "/api/resetPassword/" + token); //CHANGE DOMAIN PROPERTY IN EMAIL.PROPERTIES
            }
            model.put("textLink", "Clicca qui");
            String templateMailBodyImageKey = "imageResourceName";
            String templateMailBodyImageVal = "templates/optima_new_main_logo.png";
            model.put(templateMailBodyImageKey, templateMailBodyImageVal);

            model.forEach(context::setVariable);

            String html = templateEngine.process("email-template", context);

            helper.setTo(InternetAddress.parse(email));
            helper.setText(html, true);
            helper.setSubject("Modifica password Area Clienti Optima");
            helper.setFrom(new InternetAddress(servicesSupportEmail));

            InputStream imageIs = this.getClass().getClassLoader().getResourceAsStream("templates/optima_new_main_logo.png");
            byte[] imageByteArray = IOUtils.toByteArray(imageIs);

            final InputStreamSource imageSource = new ByteArrayResource(imageByteArray);
            helper.addInline(templateMailBodyImageVal, imageSource, "image/png");
            Transport.send(message);
        } catch (MessagingException | IOException e) {
            logger.error(e.getMessage());
            return new ForgotPasswordResponse(503, "ERROR");
        }catch (Exception e){
            logger.error(e.getMessage());
            return new ForgotPasswordResponse(503, e.getMessage());
        }
        return new ForgotPasswordResponse(200, "OK");
    }
}
