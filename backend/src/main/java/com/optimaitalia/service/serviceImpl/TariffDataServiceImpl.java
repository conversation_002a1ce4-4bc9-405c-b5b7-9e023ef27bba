package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.model.db.TariffData;
import com.optimaitalia.repository.TariffDataRepository;
import com.optimaitalia.service.TariffDataService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Service
public class TariffDataServiceImpl implements TariffDataService {

    private static final Logger logger = LogManager.getLogger(TariffDataServiceImpl.class);

    private final TariffDataRepository repository;

    public TariffDataServiceImpl(TariffDataRepository repository) {
        this.repository = repository;
    }

    @Override
    public TariffData getTariffByCountry(String country) {
        try {
            country = decodeSpaces(country);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        logger.info("Obtaining tariff list for country {}", country);
        TariffData data = new TariffData();
        if (country != null) {
            data = repository.getTariffByCountry(country);
            if (data == null) {
                return repository.getTariffByCountry("Resto del mondo");
            }
            return data;
        }
        return data;
    }

    @Override
    public TariffData getTariffRoamingByCountry(String country) {
        try {
            country = decodeSpaces(country);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        logger.info("Obtaining roaming tariff list for country {}", country);
        TariffData data = new TariffData();
        if (country != null) {
            data = repository.getTariffRoamingByCountry(country);
            if (data == null) {
                return repository.getTariffRoamingByCountry("Resto del mondo");
            }
            return data;
        }
        return data;
    }

    private String decodeSpaces(String inputString) throws UnsupportedEncodingException {
        return URLDecoder.decode(inputString, "UTF-8");
    }

}
