package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.service.SecurityService;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.mobile.*;
import com.optimaitalia.model.wrappers.mobile.conracts.CheckContractResponse;
import com.optimaitalia.model.wrappers.mobile.conracts.ContractRecord;
import com.optimaitalia.model.wrappers.mobile.costoRinnovoOffertaCheck.prodPurchPromo.ProdPurchPromo;
import com.optimaitalia.model.wrappers.mobile.products.*;
import com.optimaitalia.model.wrappers.mobile.subscriptions.SubscriptionDetailsAggregation;
import com.optimaitalia.service.InformationService;
import com.optimaitalia.service.MobileService;
import com.optimaitalia.utils.jsonReader.JsonCustomReader;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriComponentsBuilder;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class MobileServiceImpl implements MobileService {

    private static final Logger logger = LogManager.getLogger(MobileServiceImpl.class);

    @Value("${mobile.service.url.sim-per-client}")
    private String contractAPIUrl;

    @Value("${mobile.service.url.product}")
    private String productsAPIUrl;

    @Value("${mobile.service.url.product-offer}")
    private String productsOffersAPIUrl;

    @Value("${mobile.service.url.sim-details}")
    private String simDetailsApiURL;

    @Value("${mobile.service.url.sim-balance}")
    private String simBalanceApiURL;

    @Value("${mobile.service.url.product-activations}")
    private String productActivationApiURL;

    @Value("${mobile.service.url.traffic-details}")
    private String trafficDetailsUrl;

    @Value("${mobile.service.url.change-options}")
    private String changeOptionUrl;

    /*@Value("${mobile.service.url.modify-balance}")
    private String modifyBalanceUrl;*/

    @Value("${mobile.service.url.change-tariff}")
    private String changeTariffPlanUrl;

    @Value("${mobile.service.url.product-activations-external}")
    private String productActivationsServiceUrl;

    @Value("${mobile.properties.ricarica-sim.url.success}")
    private String ricaricaSuccessUrl;

    @Value("${mobile.properties.ricarica-sim.url.default}")
    private String ricaricaDefaultUrl;

    @Value("${mobile.service.url.contrattiMobile}")
    private String contrattiMobile;

    @Value("${mobile.service.url.prodPurchPromo}")
    private String prodPurchPromo;

    @Value("${mobile.service.url.ricarica-sisal}")
    private String topUpWithVoucherCodeUrl;

    @Value("${mobile.service.url.mobile-operators}")
    private String mobileOperatorsUrl;

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    private final OvalValidator ovalValidator;

    private final SecurityService securityService;
    private final InformationService informationService;

    public MobileServiceImpl(@Qualifier("sslRestTemplate") RestTemplate restTemplate,
                             ObjectMapper objectMapper, OvalValidator ovalValidator, SecurityService securityService,
                             InformationService informationService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.ovalValidator = ovalValidator;
        this.securityService = securityService;
        this.informationService = informationService;
    }

    @Override
    public List<ContractRecord> findMobileContracts(Long clientId, Long msisdnId) {
        logger.info("'Obtaining mobile contracts for user with id {} and msisdnId {}.", clientId, msisdnId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.contractAPIUrl);
        if (clientId != null) {
            builder.queryParam("customerId", clientId);
        }
        if (msisdnId != null) {
            builder.queryParam("msisdnId", msisdnId);
        }
        HttpEntity httpEntity = new HttpEntity(headers);
        ResponseEntity<Map> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, Map.class);
        logger.info("List of contracts has been obtained.");
        // change

        return objectMapper.convertValue(response.getBody().get("records"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, ContractRecord.class));
    }

    @Override
    public String topUpSimWithVoucherCode(TopUpSimWithVoucherCodeRequest request){
        logger.info("Sending request for sim top up with voucher code");
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("idSubscription", request.getIdSubscription());
        body.put("idVoucher", request.getIdVoucher());
        body.put("idCliente", request.getIdCliente());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
        Map response = restTemplate.exchange(this.topUpWithVoucherCodeUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        logger.info("Pod details has been obtained.");
        if (response != null && response.get("esito") != null) {
            return response.get("esito").toString();
        }
        return null;
    }

    @Override
    public List<MobileOperator> getMobileOperators(Long clientId) {
        logger.info("Obtaining list mobile operators", clientId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.mobileOperatorsUrl);
        if (clientId != null) {
            builder.queryParam("Cliente", clientId);
        }
        HttpEntity httpEntity = new HttpEntity(headers);
        ResponseEntity<Map> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, Map.class);
        logger.info("List of mobile operators has been obtained.");
        return objectMapper.convertValue(response.getBody().get("operatori"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, MobileOperator.class));
//        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
//        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.dilazioneClienteUrl, HttpMethod.GET, httpEntity,
//                Map.class, clientId);
    }

    @Override
    public List<ProductRecord> findProductRecords(ProductRecordsRequest productRecordsRequest, String clientId) {
        if (productRecordsRequest != null) {
            logger.info("Obtaining product records for subscription id {}", productRecordsRequest.getSubscriptionId());
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.productsAPIUrl);
            builder.queryParam("idTipoProdotto", productRecordsRequest.getProductTypeId());
            HttpHeaders headers = new HttpHeaders();
            Integer cluster = informationService.getUserData(clientId).getCluster().getValue().equals("BUSINESS") ? 1 : 2;
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            if (productRecordsRequest.getProductTypeId() == 2) {
                builder.queryParam("compatibleId", productRecordsRequest.getTariffPlanId());
                builder.queryParam("idCluster", cluster);
                builder.queryParam("subscriptionId", productRecordsRequest.getSubscriptionId());
            }
            if (productRecordsRequest.getPurchaseChannelId() != null) {
                builder.queryParam("idCanaleVendita", productRecordsRequest.getPurchaseChannelId());
            }
            if (productRecordsRequest.getProductTypeId() == 3) {
                builder.queryParam("subscriptionId", productRecordsRequest.getSubscriptionId());
            }
            builder.queryParam("data", new SimpleDateFormat("yyyy/MM/dd'T'HH:mm").format(Calendar.getInstance().getTime()));
            HttpEntity httpEntity = new HttpEntity(headers);
            ResponseEntity<Map> response = restTemplate.exchange(builder.build().toUri(), HttpMethod.GET, httpEntity, Map.class);
            logger.info("Product records have been obtained.");
            return objectMapper.convertValue(response.getBody().get("records"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ProductRecord.class));
        }
        return new ArrayList<>();
    }

    @Override
    public List<Product> findProductOffersRecords(ProductRecordsRequest productRecordsRequest) {
        if (productRecordsRequest != null) {
            logger.info("Obtaining product records for subscription id {}", productRecordsRequest.getSubscriptionId());
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.productsOffersAPIUrl);
            builder.queryParam("tipoProdotto", productRecordsRequest.getProductTypeId());
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            if (productRecordsRequest.getProductTypeId() == 2) {
                builder.queryParam("idProdottoCompatibile", productRecordsRequest.getTariffPlanId());
                builder.queryParam("idCluster", 2);
                builder.queryParam("subscriptionId", productRecordsRequest.getSubscriptionId());
            }
            if (productRecordsRequest.getProductTypeId() == 3) {
                builder.queryParam("subscriptionId", productRecordsRequest.getSubscriptionId());
            }
            if (productRecordsRequest.getPurchaseChannelId() != null) {
                builder.queryParam("idCanaleVendita", productRecordsRequest.getPurchaseChannelId());
            }
            builder.queryParam("data", new SimpleDateFormat("yyyy/MM/dd'T'HH:mm").format(Calendar.getInstance().getTime()));
            HttpEntity httpEntity = new HttpEntity(headers);
            ResponseEntity<Map> response = restTemplate.exchange(builder.build().toUri(), HttpMethod.GET, httpEntity, Map.class);
            logger.info("Product records have been obtained.");
            return objectMapper.convertValue(response.getBody().get("list"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Product.class));
        }
        return new ArrayList<>();
    }

    @Override
    public List<SubscriptionDetailsAggregation> findSubscriptionDetailsAggregation(Long msisdn) {
        logger.info("Searching subscription detail aggregation for msisdn {}", msisdn);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.simDetailsApiURL).queryParam("msisdn", msisdn);
        HttpEntity httpEntity = new HttpEntity(headers);
        ResponseEntity<Map> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, Map.class);
        logger.info("Subscription detail aggregation has been found.");
        return objectMapper.convertValue(response.getBody().get("aggregations"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, SubscriptionDetailsAggregation.class));
    }

    public String getSimBalance(Long msisdn) {
        logger.info("Getting balance for msisdn {}", msisdn);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.simBalanceApiURL).queryParam("msisdn", msisdn);
        HttpEntity httpEntity = new HttpEntity(headers);
        ResponseEntity<Map> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, Map.class);
        logger.info("Sim balance has been obtained");
        List<SimBalance> records = objectMapper.convertValue(response.getBody().get("records"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, SimBalance.class));
            return records.get(0).getBalance();
    }

    @Override
    public List<ProductActivationRecord> findProductActivationRecord(Long productActivationId) {
        logger.info("Obtaining product activation records with id {}", productActivationId);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.productActivationApiURL).queryParam("id", productActivationId);
        HttpEntity httpEntity = new HttpEntity(headers);
        ResponseEntity<Map> response = restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, Map.class);
        logger.info("Product activations have been obtained.");
        return objectMapper.convertValue(response.getBody().get("records"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, ProductActivationRecord.class));

    }

    @Override
    public ProductDescription findProductDescription(Long productId) {
        JsonCustomReader reader = new JsonCustomReader<List<ProductDescription>>(objectMapper);
        try {
            ObjectMapper mapper = new ObjectMapper();
            for (Object description : (List<?>) reader.getStringFromFile("/mobile_product_configuration.json")) {
                ProductDescription productoinDescription = new ProductDescription();
                productoinDescription = mapper.convertValue(description,ProductDescription.class);
                //if (description instanceof ProductDescription) {
                    //productoinDescription = (ProductDescription) description;
                    if (productoinDescription.getProductId().equals(productId)) {
                        return productoinDescription;
                    }
                //}
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<TrafficDetail> findTrafficDetails(TrafficDetailsRequest trafficDetailsRequest) throws ValidateException {
        ovalValidator.validate(trafficDetailsRequest);
        logger.info("Obtaining traffic details for msisdn {}", trafficDetailsRequest.getMsisdnId());
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

        MultiValueMap<String, String> uriParams = new LinkedMultiValueMap<>();
        uriParams.add("msisdn", trafficDetailsRequest.getMsisdnId());
        uriParams.add("Start", "0");
        uriParams.add("limit", "1000");

        if (!StringUtils.isEmpty(trafficDetailsRequest.getFromDate())) {
            uriParams.add("startDate", trafficDetailsRequest.getFromDate());
        }
        if (!StringUtils.isEmpty(trafficDetailsRequest.getToDate())) {
            uriParams.add("endDate", trafficDetailsRequest.getToDate());
        }

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(this.trafficDetailsUrl);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);

        Map response = restTemplate.exchange(builder.queryParams(uriParams).build().toUri(),
                HttpMethod.GET, httpEntity, Map.class).getBody();
        logger.info("Traffic details have been obtained.");
        Map<String, Object> crmData = (Map<String, Object>) response.get("crmData");
        if (crmData == null || crmData.get("ebssCdrs") == null) return new ArrayList<>();
        Map<String, Object> ebssCdrs = (Map<String, Object>) crmData.get("ebssCdrs");
        if (ebssCdrs.get("ebssCdr") instanceof ArrayList) {
            return objectMapper.convertValue(ebssCdrs.get("ebssCdr"), objectMapper.getTypeFactory()
                    .constructCollectionType(List.class, TrafficDetail.class));
        }
        List<TrafficDetail> result = new ArrayList<>();
        result.add(objectMapper.convertValue(ebssCdrs.get("ebssCdr"), TrafficDetail.class));
        return result;
    }

    @Override
    public ActivateOptionResponse activateOption(ActivateOptionRequest activateOptionRequest) throws ValidateException {
        ovalValidator.validate(activateOptionRequest);
        List<ContractRecord> mobileContracts = this.findMobileContracts(activateOptionRequest.getCustomerId(), activateOptionRequest.getSimNumber());

        if (mobileContracts != null && !mobileContracts.isEmpty()) {
            List<Long> oldOptList = new ArrayList<>();

            List<Long> newOptList = new ArrayList<>();
            newOptList.add(activateOptionRequest.getNewOptionId());
            logger.info("Activating new option for user with id {} sim number {}",
                    activateOptionRequest.getCustomerId(), activateOptionRequest.getSimNumber());
            ActivateOptionResponse activateOptionResponse = this.changeOption(activateOptionRequest.getCustomerId(),
                    mobileContracts.get(0).getId(), activateOptionRequest.getSimNumber(), newOptList, oldOptList);
            logger.info("Option activation result: {}", activateOptionResponse);
            return activateOptionResponse;
        }
        logger.info("No contracts have been found.");
        return new ActivateOptionResponse();
    }

    @Override
    public ActivateOptionResponse changeOption(Long customerId, Long subscriptionId, Long simNumber, List<Long> newOptList, List<Long> oldOptList) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

        Map<String, Object> body = new HashMap<>();
        body.put("idCliente", customerId);
        body.put("subscriptionId", subscriptionId);
        body.put("msisdn", simNumber);
        body.put("channelId", 1);
        body.put("newOptList", newOptList);
        body.put("oldOptList", oldOptList);

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);

        return this.restTemplate.postForObject(this.changeOptionUrl, httpEntity, ActivateOptionResponse.class);
    }

    @Override
    @Deprecated
    public ModelAndView modifyBalance(ModifyBalanceRequest activateOptionRequest) throws ValidateException, PaymentException {
        ovalValidator.validate(activateOptionRequest);
        ModelAndView modelAndView = new ModelAndView();
       /* logger.info("Modifying balance of number {}", activateOptionRequest.getSimNumber());
        List<ContractRecord> mobileContracts = this.findMobileContracts(null, activateOptionRequest.getSimNumber());

        if (mobileContracts != null && !mobileContracts.isEmpty()) {
            ContractRecord contractRecord = mobileContracts.get(0);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

            Map<String, Object> body = new HashMap<>();
            body.put("idCliente", contractRecord.getCustomerId());
            body.put("subscriptionId", contractRecord.getId());
            body.put("msisdn", contractRecord.getMsisdnId());
            body.put("channelId", 1);
            body.put("amount", activateOptionRequest.getAmount());

            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
            Optional<ActivateOptionResponse> response = Optional.ofNullable(this.restTemplate.postForObject(this.modifyBalanceUrl, httpEntity, ActivateOptionResponse.class));
            if (response.isPresent() && OptimaServiceStatus.OK.getCode().equals(response.get().getCode())) {
                logger.info("Balance has been modified.");
                modelAndView.setView(new RedirectView(this.ricaricaSuccessUrl));
                return modelAndView;
            }
            String errorMessage = response.map(ActivateOptionResponse::getErrorMessage).orElse(null);
            logger.error("Error while trying to modify balance of sim number {}. Error: {}",
                    activateOptionRequest.getSimNumber(), errorMessage);
            throw new PaymentException(errorMessage);
        }
        logger.info("No records have been found.");*/
        modelAndView.setView(new RedirectView(this.ricaricaSuccessUrl));
        return modelAndView;
    }


    @Override
    public ActivateOptionResponse changeTariffPlan(ActivateOptionRequest activateOptionRequest) throws ValidateException {
        ovalValidator.validate(activateOptionRequest);
        logger.info("Changing tariff plan for number {}", activateOptionRequest.getSimNumber());
        List<ContractRecord> mobileContracts = this.findMobileContracts(activateOptionRequest.getCustomerId(), activateOptionRequest.getSimNumber());

        if (mobileContracts != null && !mobileContracts.isEmpty()) {
            ContractRecord contractRecord = mobileContracts.get(0);

            ProductActivationRecord productActivationRecord = IterableUtils.find(contractRecord.getAdditionalProducts(),
                    this.produceProductTypePredicate(2, ProductActivationRecord.class));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

            List<Long> newOptList = new ArrayList<>();
            newOptList.add(activateOptionRequest.getNewOptionId());

            Long actualMainProduct = null;
            if (productActivationRecord != null && productActivationRecord.getProduct() != null) {
                actualMainProduct = productActivationRecord.getProduct().getId();
            }
            List<Long> oldOptList = new ArrayList<>();

            if (actualMainProduct != null) {
                oldOptList.add(actualMainProduct);
            }

            Map<String, Object> body = new HashMap<>();
            body.put("idCliente", contractRecord.getCustomerId());
            body.put("subscriptionId", contractRecord.getId());
            body.put("msisdn", contractRecord.getMsisdnId());
            body.put("channelId", 1);
            body.put("oldTp", contractRecord.getMainProduct().getTariffPlanId());
            body.put("newOptList", newOptList);
            body.put("oldOptList", oldOptList);
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
            ActivateOptionResponse activateOptionResponse = this.restTemplate.postForObject(this.changeTariffPlanUrl,
                    httpEntity, ActivateOptionResponse.class);
            logger.info("Tariff plan has been changed.");
            return activateOptionResponse;
        }
        logger.info("No records have been found.");
        return new ActivateOptionResponse();
    }

    @Override
    public ProductActivationRecord getLastRecharge(Long simNumber) {
        logger.info("Obtaining last recharge  date of simNumber {}", simNumber);
        List<ContractRecord> mobileContracts = this.findMobileContracts(null, simNumber);
        if (mobileContracts != null && !mobileContracts.isEmpty()) {
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            multiValueMap.set("subscriptionId", mobileContracts.get(0).getId().toString());
            multiValueMap.set("start", "0");
            multiValueMap.set("limit", "1");
            multiValueMap.set("dir", "desc");
            multiValueMap.set("productCategories", "balance");
            multiValueMap.set("order", "purchaseOn");
            multiValueMap.set("sort", "id");
            List<ProductActivationRecord> productActivations = getProductActivations(multiValueMap);
            if (productActivations != null && !productActivations.isEmpty()) {
                logger.info("Last recharge date has been obtained");
                return productActivations.get(0);
            }
        }
        logger.info("No records have been found.");
        return new ProductActivationRecord();
    }

    @Override
    public List<ProductActivationRecord> getProductActivations(MultiValueMap<String, String> criteria) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity httpEntity = new HttpEntity(headers);
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(this.productActivationsServiceUrl);
        ResponseEntity<Map> response = restTemplate.exchange(builder.queryParams(criteria).build().toUri(), HttpMethod.GET, httpEntity, Map.class);
        return objectMapper.convertValue(response.getBody().get("records"),
                objectMapper.getTypeFactory().constructCollectionType(List.class, ProductActivationRecord.class));
    }


    private CheckContractResponse getDateForProdPurch(Long id) {
        ResponseEntity<Map> response = restTemplate.exchange(prodPurchPromo, HttpMethod.GET, null, Map.class, id);
        logger.info("Get prodPurchPromo list.");
        ProdPurchPromo prodPurchPromo = objectMapper.convertValue(response.getBody(), ProdPurchPromo.class);
        if ((prodPurchPromo.getRecords().size() > 0) && (prodPurchPromo.getRecords().get(0).getPromotion() != null) &&
                prodPurchPromo.getRecords().get(0).getPromotion().getValidityPeriod() > 1) {
            return new CheckContractResponse(prodPurchPromo.getRecords().get(0).getPromotion().getDisplayName());
        } else if ((prodPurchPromo.getRecords().size() > 0) && (prodPurchPromo.getRecords().get(0).getPromotion() != null)) {
            return new CheckContractResponse("");
        }

        return null;


    }

    @Override
    public CheckContractResponse getProdPurchPromo(Long clientId, Long prId) {
        return getDateForProdPurch(prId);
    }

    @Override
    public Map<String, String> getTariffeNazionali() {
        JsonCustomReader<HashMap<String, String>> jr = new JsonCustomReader<>(objectMapper);
        return jr.getStringFromFile("/json/tariffeNazionali.json");
    }

    @Override
    public Map<String, Object> getInternationalRates() {
        JsonCustomReader<HashMap<String, Object>> jr = new JsonCustomReader<>(objectMapper);
        return jr.getStringFromFile("/json/tarifferInternazionali.json");

    }


    private Predicate<ProductActivationRecord> produceProductTypePredicate(final int productType, final Class<ProductActivationRecord> clazz) {
        return o -> {
            boolean found = false;
            if (clazz == ProductActivationRecord.class && o.getClass() == clazz) {
                found = o.getProduct() != null && o.getProduct().getProductMapping() != null
                        && o.getProduct().getProductMapping().getIdTipoProdotto() != null
                        && o.getProduct().getProductMapping().getIdTipoProdotto().intValue() == productType;
            }
            return found;
        };
    }
}
