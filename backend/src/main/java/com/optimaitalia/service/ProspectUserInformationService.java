package com.optimaitalia.service;

import com.optima.security.model.prospect.ForgotPasswordRequest;
import com.optima.security.model.prospect.ForgotPasswordResponse;
import com.optimaitalia.model.prospectUser.ProspectUserContractsResponse;
import com.optimaitalia.model.prospectUser.emailToSupport.ProspectUserEmailToSupport;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface ProspectUserInformationService {

    ProspectUserContractsResponse getContractsByCodiceFiscale(String codiceFiscale);

    Integer sendEmailToSupport(ProspectUserEmailToSupport email);

    ResponseEntity<?> uploadFile(MultipartFile[] files, String contractId);

    ForgotPasswordResponse restorePassword(ForgotPasswordRequest request);
}
