package com.optimaitalia.service;

import com.optimaitalia.model.wrappers.voucherCard.*;
import org.springframework.http.ResponseEntity;

public interface VoucherCardService {

    VoucherCardResponse checkVoucherCard(String voucherCard);

    VoucherCardResponse redeemVoucherCard(RedeemVoucherRequest voucherRequest);

    ResponseEntity<?> redeemVoucherCardTopUpSim(TopUpSimByVoucher topUpSimByVoucher);

    UsedVouchersInformation getInformationAboutUserCards(String typeCard, Long clientId);

    ResponseEntity<?> getContractsInformation(Long clientId);

    VoucherSimBalanceInformation getSimBalanceInformation(String clientId, String utenza);
}
