package com.optimaitalia.service;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.exception.PaymentException;
import com.optimaitalia.model.wrappers.mobile.*;
import com.optimaitalia.model.wrappers.mobile.conracts.CheckContractResponse;
import com.optimaitalia.model.wrappers.mobile.conracts.ContractRecord;
import com.optimaitalia.model.wrappers.mobile.products.*;
import com.optimaitalia.model.wrappers.mobile.subscriptions.SubscriptionDetailsAggregation;
import org.springframework.util.MultiValueMap;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

public interface MobileService {

    List<ContractRecord> findMobileContracts(Long clientId, Long msisdnId);

    List<ProductRecord> findProductRecords(ProductRecordsRequest productRecordsRequest, String clientId);

    List<Product> findProductOffersRecords(ProductRecordsRequest productRecordsRequest);

    List<SubscriptionDetailsAggregation> findSubscriptionDetailsAggregation(Long msisdn);

    String getSimBalance(Long msisdn);

    List<ProductActivationRecord> findProductActivationRecord(Long productActivationId);

    ProductDescription findProductDescription(Long productId);

    List<TrafficDetail> findTrafficDetails(TrafficDetailsRequest trafficDetailsRequest) throws ValidateException;

    ActivateOptionResponse activateOption(ActivateOptionRequest activateOptionRequest) throws ValidateException;

    ActivateOptionResponse changeOption(Long customerId, Long subscriptionId, Long simNumber, List<Long> newOptList, List<Long> oldOptList);

    ModelAndView modifyBalance(ModifyBalanceRequest request) throws ValidateException, PaymentException;

    ActivateOptionResponse changeTariffPlan(ActivateOptionRequest activateOptionRequest) throws ValidateException;

    ProductActivationRecord getLastRecharge(Long simNumber);

    List<ProductActivationRecord> getProductActivations(MultiValueMap<String, String> criteria);

    CheckContractResponse getProdPurchPromo(Long id, Long prId);

    Map<String, String> getTariffeNazionali();

    Map<String, Object> getInternationalRates();

    String topUpSimWithVoucherCode(TopUpSimWithVoucherCodeRequest request);

    List<MobileOperator> getMobileOperators(Long clientId);
}
