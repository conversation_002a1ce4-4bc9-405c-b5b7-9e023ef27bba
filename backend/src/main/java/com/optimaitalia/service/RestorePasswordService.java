package com.optimaitalia.service;

import com.optima.security.model.UserEntity;
import com.optimaitalia.model.forgotPassword.ForgotPasswordFormRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordRequest;
import com.optimaitalia.model.forgotPassword.ForgotPasswordResponse;

import java.util.List;

public interface RestorePasswordService {
    ForgotPasswordResponse restorePassword(ForgotPasswordRequest request);

    void updatePswToken(UserEntity user, String token);

    List<UserEntity> findByToken(String token);

    ForgotPasswordResponse resetPassword(ForgotPasswordFormRequest fpf, String token);
}
