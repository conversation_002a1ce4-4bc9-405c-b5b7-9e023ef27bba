package com.optimaitalia.service;

import com.optima.security.model.changeCustomerEmail.ChangeEmailRequest;
import com.optima.security.model.changeCustomerEmail.ChangeEmailResponse;
import com.optima.security.model.newUserService.RequestNewUserService;
import com.optima.security.model.newUserService.ResponseNewUserService;
import com.optima.security.model.updateUserDeleted.UpdateDeletedFieldBatchResponse;
import com.optima.security.model.updateUserDeleted.UpdateDeletedFieldRequest;
import com.optimaitalia.model.optimaResponseRequest.IsUserResponse;

import java.util.List;

public interface OptimaService {
    IsUserResponse isUserExist(String userName);

    ResponseNewUserService addNewUser(RequestNewUserService user);

    ChangeEmailResponse changeEmail(ChangeEmailRequest changeEmailRequest);

    UpdateDeletedFieldBatchResponse updateDeletedFieldBatch(List<UpdateDeletedFieldRequest> updateDeletedFieldRequests);
}
