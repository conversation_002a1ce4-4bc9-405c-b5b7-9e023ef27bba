package com.optimaitalia;

import com.optimaitalia.configuration.yamlConfig.YamlPropertySourceFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

@SpringBootApplication
@PropertySource(factory = YamlPropertySourceFactory.class,
        value = { "classpath:/appconst-${spring.profiles.active}.yaml"})
@PropertySource( value = {"classpath:/properties/payment/payment-${spring.profiles.active}.properties",
        "classpath:/properties/email/email-${spring.profiles.active}.properties",
"classpath:/properties/paypal/paypal-${spring.profiles.active}.properties"})
@EnableGlobalMethodSecurity(prePostEnabled = true)
@EnableJpaRepositories("com.optimaitalia.repository")
@EntityScan("com.optimaitalia.model.db")
public class SelfCareApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(SelfCareApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(SelfCareApplication.class, args);
    }


}
