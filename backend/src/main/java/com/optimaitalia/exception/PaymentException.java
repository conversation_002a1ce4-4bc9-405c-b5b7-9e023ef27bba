package com.optimaitalia.exception;

public class PaymentException extends Throwable {

    private String message;

    public PaymentException(String message) {
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }


    @Override
    public String toString() {
        return "PaymentException{" +
                "message='" + message + '\'' +
                '}';
    }
}
