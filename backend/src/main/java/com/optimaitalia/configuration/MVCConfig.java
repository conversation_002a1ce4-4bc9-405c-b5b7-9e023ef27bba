package com.optimaitalia.configuration;

import com.optimaitalia.intercepters.RestControllerLoggerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Configuration
public class MVCConfig implements WebMvcConfigurer {

    @Bean
    protected HandlerInterceptorAdapter restControllerLoggerInterceptor() {
        return new RestControllerLoggerInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this.restControllerLoggerInterceptor()).addPathPatterns("/**");
    }
}
