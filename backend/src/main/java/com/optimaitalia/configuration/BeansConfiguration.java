package com.optimaitalia.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.configuration.interceptor.Wso2TokenInterceptor;
import com.optimaitalia.handlers.rest.template.RestTemplateResponseErrorHandlerFactory;
import com.optimaitalia.handlers.rest.template.RestTemplateResponseErrorHandlerProvider;
import com.optimaitalia.handlers.rest.template.RestTemplateResponseErrorProcessor;
import com.optimaitalia.handlers.rest.template.RestTemplateUnauthorizedResponseHandler;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Arrays;

@Configuration
public class BeansConfiguration {

    @Bean
    protected ClientHttpRequestInterceptor xApiConsumerHeaderInterceptor() {
        return (request, body, execution) -> {
            request.getHeaders().add("X-API-CONSUMER", " SELFCARE");
            return execution.execute(request, body);
        };
    }

    @Bean
    protected RestTemplate restTemplate(RestTemplateBuilder builder, Wso2TokenInterceptor wso2TokenInterceptor) {
        return builder.errorHandler(new RestTemplateResponseErrorProcessor(this
                        .restTemplateResponseErrorHandlerProvider()))
                .interceptors(Arrays.asList(this.xApiConsumerHeaderInterceptor(), wso2TokenInterceptor))
                .build();
    }

    @Bean
    protected RestTemplate sslRestTemplate(Wso2TokenInterceptor wso2TokenInterceptor) throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.setInterceptors(Arrays.asList(this.xApiConsumerHeaderInterceptor(), wso2TokenInterceptor));
        return restTemplate;
    }

    @Bean
    protected ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    protected RestTemplateResponseErrorHandlerProvider restTemplateResponseErrorHandlerProvider() {
        RestTemplateResponseErrorHandlerProvider provider = new RestTemplateResponseErrorHandlerProvider();
        provider.addHandlerFactory(HttpStatus.Series.CLIENT_ERROR, RestTemplateResponseErrorHandlerFactory.factory(builder -> {
            builder.add(HttpStatus.UNAUTHORIZED, new RestTemplateUnauthorizedResponseHandler());
        }));
        return provider;

    }
}
