package com.optimaitalia.configuration.interceptor;

import com.optimaitalia.service.OptimaTokenService;
import lombok.NonNull;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;


@Component
public class Wso2TokenInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger logger = LogManager.getLogger(Wso2TokenInterceptor.class);
    private static final String WSO2_IDENTIFIER = "wso2";
    private static final String OPTIMA_TOKEN_HEADER = "x-optima-token";
    private static final String CUSTOMER_DETAIL_API = "governance.customer.Detail-API";

    private final OptimaTokenService wso2TokenService;

    public Wso2TokenInterceptor(OptimaTokenService wso2TokenService) {
        this.wso2TokenService = wso2TokenService;
    }

    @Override
    public @NonNull ClientHttpResponse intercept(HttpRequest request, @NonNull byte[] body,
                                                 @NonNull ClientHttpRequestExecution execution) throws IOException {
        String url = request.getURI().toString();
        if (!isWso2Request(url)) {
            return execution.execute(request, body);
        }
        try {
            String clientId = getCurrentClientId();
            if (clientId == null || clientId.isEmpty()) {
                clientId = extractClientIdFromUrl(url);
            }

            String ipAddress = getClientIpAddress();
            if (clientId != null && !clientId.isEmpty()) {
                String token = wso2TokenService.generateWso2Token(clientId, ipAddress);
                request.getHeaders().add(OPTIMA_TOKEN_HEADER, token);

            } else {
                logger.warn("Could not obtain clientId for WSO2 service call to: {}", url);
            }
        } catch (Exception e) {
            logger.error("Error adding x-optima-token header for WSO2 service call to: {}", url, e);
        }

        return execution.execute(request, body);
    }

    private boolean isWso2Request(String url) {
        return StringUtils.hasText(url) && url.contains(WSO2_IDENTIFIER);
    }

    private String getCurrentClientId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() != null) {
                com.optima.security.model.JwtToken jwtToken =
                        (com.optima.security.model.JwtToken) authentication.getPrincipal();
                return jwtToken.getUid();
            }
        } catch (Exception e) {
            logger.error("Error extracting client ID from authentication context", e);
        }
        return null;
    }

    private String extractClientIdFromUrl(String url) {
        if (url.contains(CUSTOMER_DETAIL_API)) {
            return url.substring(url.lastIndexOf('/') + 1);
        }
        return null;
    }

    private String getClientIpAddress() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getRemoteAddr();
        }
        return "unknown";
    }
}
