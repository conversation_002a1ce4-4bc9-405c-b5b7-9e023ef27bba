package com.optimaitalia.utils.dateDeserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class OfferDateDeserializer extends JsonDeserializer<Date> {

    private SimpleDateFormat sdf;

    public OfferDateDeserializer() {
        sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS", Locale.ITALY);
        sdf.setTimeZone(TimeZone.getTimeZone("Europe/Rome"));
    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        try {
            return sdf.parse(jsonParser.getText());
        } catch (ParseException e) {
            return null;
        }
    }
}
