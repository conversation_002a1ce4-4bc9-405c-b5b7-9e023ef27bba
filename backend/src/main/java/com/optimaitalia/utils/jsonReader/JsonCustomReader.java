package com.optimaitalia.utils.jsonReader;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;

@Service
public class JsonCustomReader<T> {
    private final ObjectMapper objectMapper;
    private static final Logger logger = LogManager.getLogger(JsonCustomReader.class);

    public JsonCustomReader(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }


    public T getStringFromFile(String path) {
        logger.info("Reading json file " + path);
        try (InputStream inputStream = TypeReference.class.getResourceAsStream(path)) {
            return objectMapper.readValue(inputStream, new TypeReference<T>() {
            });
        } catch (IOException e) {
            logger.error("Error while trying to read json file.");
            return null;
        }
    }
}
