package com.optimaitalia.utils;

import com.optimaitalia.model.condominio.Condominio;
import com.optimaitalia.service.serviceImpl.UserDataServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CheckPreAuthorizeUtil {

    private static final Logger logger = LogManager.getLogger(CheckPreAuthorizeUtil.class);

    public final UserDataServiceImpl userDataService;

    @Autowired
    public CheckPreAuthorizeUtil(UserDataServiceImpl userDataService) {
        this.userDataService = userDataService;
    }

    /**
     * Method checks clientId from request and from authentication.principal.uid getting
     * from spring security for sottopitoCluster type, then get condominio (child) users and
     * also checks list of condominio users ids for conformity with clientId from request
     *
     * @param clientIdFromReq client id received from request
     * @param currentClientId client id received from spring security
     * @return boolean result, depends on user cluster or condominio users
     */
    public boolean checkForUserRole(String clientIdFromReq, String currentClientId) {
        List<Condominio> condominioList  = userDataService.getUserCondominioInfo(currentClientId);
        if (condominioList == null || condominioList.isEmpty()) {
            logger.warn("CondominioList for ClientId: " + currentClientId + " is empty");
            return true;
        }
        else if (clientIdFromReq.equals(currentClientId)) {
            logger.warn("Client ID validation failed");
            return true;
        }
        else {
            List<Long> condominiosIds = condominioList.stream().map(Condominio::getCustomerId).collect(Collectors.toList());
            return condominiosIds.contains(Long.parseLong(clientIdFromReq));
        }
    }
}
