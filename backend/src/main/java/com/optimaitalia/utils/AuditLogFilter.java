package com.optimaitalia.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.model.Contracts;
import com.optimaitalia.service.AuditLogService;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

@Component
public class AuditLogFilter implements Filter {

    // private static final Logger logger = LogManager.getLogger(AuditLogFilter.class);

    private final Environment environment;
    private final AuditLogService auditLogService;

    /* @Autowired
     private org.springframework.context.ApplicationContext appContext;
 */
    public AuditLogFilter(Environment environment, AuditLogService auditLogService) {
        this.environment = environment;
        this.auditLogService = auditLogService;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
  /*  // getting return type of endpoint
  String methodReturnType = null;
      try {
            RequestMappingHandlerMapping handlerMapping = (RequestMappingHandlerMapping) appContext.getBean("requestMappingHandlerMapping");
            HandlerMethod handlerMethod = (HandlerMethod) Objects.requireNonNull(handlerMapping.getHandler(httpRequest)).getHandler();
            Class<?> returnType = handlerMethod.getMethod().getReturnType();
            methodReturnType = returnType.getSimpleName();
        } catch (Exception e) {
            logger.error("Cannot get the handler method");
        }*/
        String jwtToken = httpRequest.getHeader("authorization");
        if (httpRequest.getRequestURI().contains("/upload-file") || httpRequest.getRequestURI().contains("/attachment")
                || httpRequest.getRequestURI().contains("/email/new")) {
            getAllInformationAboutRequest(jwtToken, httpRequest, null);
            chain.doFilter(request, response);
        } else {
            MultiReadRequest wrapper = new MultiReadRequest((HttpServletRequest) request);
            getAllInformationAboutRequest(jwtToken, httpRequest, wrapper);
            chain.doFilter(wrapper, response);
        }
    }

    private void getAllInformationAboutRequest(String jwtToken, HttpServletRequest httpRequest, MultiReadRequest wrapper) throws IOException {
        if (jwtToken != null) {
            String[] split_string = jwtToken.split("\\.");
            String base64EncodedBody = split_string[1];
            Base64 base64Url = new Base64(true);
            String body = new String(base64Url.decode(base64EncodedBody));
            JSONObject jsonObject = new JSONObject(body);
            String Url = environment.acceptsProfiles("preprod") ? "http://mi-areaclienti-pre.optimaitalia.com" + httpRequest.getRequestURI()
                    : "https://areaclienti.optimaitalia.com" + httpRequest.getRequestURI();
            String resourceType;
            String resourceId;
            if (httpRequest.getRequestURI().contains("/api/pdf/")) {
                String[] parts = httpRequest.getRequestURI().split("/");
                resourceId = parts[parts.length - 1];
                resourceType = "FILE_ID";
            } else if (httpRequest.getRequestURI().contains("api/contracts/pdf/")) {
                ObjectMapper objectMapper = new ObjectMapper();
                Contracts jsonMap = objectMapper.readValue(wrapper.requestBody, Contracts.class);
                resourceId = jsonMap.getSpRelativeUri();
                resourceType = "CONTRACT_ID";
            } else {
                resourceId = String.valueOf(jsonObject.get("id"));
                resourceType = "CUSTOMER_ID";
            }
            auditLogService.sendLog(httpRequest.getMethod(), String.valueOf(jsonObject.get("id")), httpRequest.getRemoteAddr(), Url, resourceId, resourceType);
        }
    }

    @Override
    public void destroy() {

    }

    static class MultiReadRequest extends HttpServletRequestWrapper {

        private final String requestBody;

        @SneakyThrows
        public MultiReadRequest(HttpServletRequest request) {
            super(request);
            requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody.getBytes());
            return new ServletInputStream() {
                @Override
                public boolean isFinished() {
                    return byteArrayInputStream.available() == 0;
                }

                @Override
                public boolean isReady() {
                    return true;
                }

                @Override
                public void setReadListener(ReadListener readListener) {

                }

                @Override
                public int read() throws IOException {
                    return byteArrayInputStream.read();
                }
            };
        }

        @Override
        @SneakyThrows
        public BufferedReader getReader() {
            return new BufferedReader(new InputStreamReader(this.getInputStream(), StandardCharsets.UTF_8));
        }
    }
}
