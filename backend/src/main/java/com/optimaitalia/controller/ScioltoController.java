package com.optimaitalia.controller;


import com.optimaitalia.model.enums.AccountType;
import com.optimaitalia.service.serviceImpl.ScioltoServiceImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController()
@RequestMapping("/api/sciolto")
public class ScioltoController {

    private final ScioltoServiceImpl scioltoService;

    public ScioltoController(ScioltoServiceImpl scioltoService) {
        this.scioltoService = scioltoService;
    }

    @GetMapping("/general/information/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity getScioltoGeneralInformation(@PathVariable String clientId) {
         return scioltoService.getScioltoGeneralInformation(clientId);
    }

    @GetMapping("/information/{offer}/{cluster}")
    public ResponseEntity getScioltoInformation(@PathVariable String offer, @PathVariable AccountType cluster) {
        return scioltoService.getScioltoInformation(offer, cluster);
    }
}
