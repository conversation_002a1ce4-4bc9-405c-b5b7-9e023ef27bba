package com.optimaitalia.controller;

import com.optimaitalia.model.optimaYoung.OptimaYoung;
import com.optimaitalia.service.OptimaYoungService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/api/optima-young")
public class OptimaYoungController {

    private final OptimaYoungService optimaYoungService;

    public OptimaYoungController(OptimaYoungService optimaYoungService) {
        this.optimaYoungService = optimaYoungService;
    }

    @GetMapping("/{clientId}")
    public OptimaYoung getInviteFriendsInfo(@PathVariable String clientId) {
        return optimaYoungService.getInviteFriendsInfo(clientId);
    }
}
