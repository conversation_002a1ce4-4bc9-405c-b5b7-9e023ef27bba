package com.optimaitalia.controller;

import com.optimaitalia.model.offer5g.Offer5GResponse;
import com.optimaitalia.service.OffeesInAppService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/api/offers-in-apps")
public class OffersInAppController {

    private final OffeesInAppService offeesInAppService;

    public OffersInAppController(OffeesInAppService offeesInAppService) {
        this.offeesInAppService = offeesInAppService;
    }

    @GetMapping("/offer-5g/{clientId}/{subscriptionId}")
    public Offer5GResponse getOffer5GData(@PathVariable String clientId, @PathVariable String subscriptionId) {
        return offeesInAppService.getOffer5GData(clientId, subscriptionId);
    }
}
