package com.optimaitalia.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.models.wrappers.email.EmailMessage;
import com.optima.common.services.EmailService;
import com.optima.security.model.JwtToken;
import com.optimaitalia.model.enums.IncidentEvent;
import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventRequest;
import com.optimaitalia.service.IncidentEventService;
import com.optimaitalia.service.serviceImpl.SupportServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;


@RestController()
@RequestMapping("/api/email")
public class EmailController {

    private static final Logger logger = LogManager.getLogger(EmailController.class);

    private final EmailService emailService;
    private final SupportServiceImpl supportService;
    private final IncidentEventService incidentEventService;

    public EmailController(EmailService emailService, SupportServiceImpl supportService, IncidentEventService incidentEventService) {
        this.emailService = emailService;
        this.supportService = supportService;
        this.incidentEventService = incidentEventService;
    }

    @PostMapping(value = "/new", consumes = {"multipart/form-data"})
    public ResponseEntity newSupportEmailMessage(
            @RequestPart("subjectHeader") String subjectHeader,
            @RequestPart("subject") String subject,
            @RequestPart("message") String message,
            @RequestPart(value = "category", required = false) String category,
            @RequestPart(value = "utNumber", required = false) String utNumber,
            @RequestPart(value = "bills", required = false) String bills,
            @RequestPart(value = "email", required = false) String email,
            @RequestPart("files") MultipartFile[] files,
            Principal principal) {
        logger.info("Sending new email with attachment from user with id: {}", ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid());
        return emailService.sendEmailWithAttachmentToSupport(files, message, subjectHeader,  subject, category, utNumber, bills, email);
    }

    @PostMapping(value = "/new")
    public ResponseEntity newSupportEmailMessage(@RequestBody EmailMessage emailMessage, Principal principal) throws JsonProcessingException {
        logger.info("Sending new email from user with id: {}", ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid());
        return emailService.sendEmailMessageToSupport(emailMessage, supportService.checkIncidentEventDifferentType(((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid()));
    }


    @PostMapping("/integrated/solution")
    public ResponseEntity sendRequestForIntegratedSolution(@RequestBody EmailMessage emailMessage, Principal principal) throws ValidateException {
        String uid = ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid();
        logger.info("Sending new Email with request for Integrated Solution from user with id: {}", uid
        );
        return emailService.sendRequestForIntegratedSolution(emailMessage, uid);
    }

    @PostMapping("/tuttoinuno/msg")
    public ResponseEntity sendTuttoInUnoSolution(@RequestBody EmailMessage emailMessage, Principal principal) throws ValidateException {
        String uid = ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid();
        logger.info("Sending new Email with request for Integrated Solution from user with id: {}", uid
        );
        return emailService.sendRequestForTuttoInUno(emailMessage, uid);
    }

    @PostMapping("/agent/bookVisit")
    public ResponseEntity sendRequestForBookVisitAgent(@RequestBody EmailMessage emailMessage, Principal principal) throws ValidateException {
        logger.info("Sending emails and open incident with request for book visit agent from user with id: {}", ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid());
        IncidentEventRequest incidentEventRequest = new IncidentEventRequest();
        incidentEventRequest.setCustomerId(Long.parseLong(((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid()));
        incidentEventRequest.setIncidentEvent(IncidentEvent.VISIT_AGENT);
        incidentEventRequest.setIncidentCategory(IncidentEventCategory.VISIT_AGENT);
        incidentEventRequest.setIncidentAnnotation(emailMessage.getMessage());
        incidentEventRequest.setServiceType("6");
        incidentEventRequest.setOrigin("200007");
        incidentEventService.customerIncidentEvent(incidentEventRequest);
        return emailService.sendRequestForBookVisitAgent(emailMessage);
    }
}
