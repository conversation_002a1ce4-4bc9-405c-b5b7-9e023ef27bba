package com.optimaitalia.controller;

import com.optimaitalia.model.db.ClientNotification;
import com.optimaitalia.service.ClientNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/notifications")
public class ClientNotificationController {

    private final ClientNotificationService clientNotificationService;

    @Autowired
    public ClientNotificationController(ClientNotificationService clientNotificationService) {
        this.clientNotificationService = clientNotificationService;
    }

    @GetMapping("/{clientId}")
    // @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<List<ClientNotification>> getAllClientNotifications(@PathVariable Long clientId) {
        List<ClientNotification> clientNotificationsList = clientNotificationService.getAllClientNotifications(clientId);
        return new ResponseEntity<>(clientNotificationsList, HttpStatus.OK);
    }

    @PostMapping("/{clientId}/list")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<List<ClientNotification>> getAllClientNotificationsForUserList(@PathVariable Long clientId,
                                                                                         @RequestBody List<Long> clientIdList) {
        List<ClientNotification> clientNotificationsList =
                clientNotificationService.getAllClientNotificationsForUserList(clientIdList);
        return new ResponseEntity<>(clientNotificationsList, HttpStatus.OK);
    }

    @GetMapping("/{clientId}/{id}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<ClientNotification> getClientNotificationById(@PathVariable Long clientId, @PathVariable("id") Long id) {
        ClientNotification clientNotification = clientNotificationService.getClientNotificationById(clientId, id);
        return new ResponseEntity<>(clientNotification, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<ClientNotification> createOrUpdateNotification(@RequestBody ClientNotification clientNotification) {
        ClientNotification updatedCreatedNotification = clientNotificationService.createOrUpdateNotification(clientNotification);
        return new ResponseEntity<>(updatedCreatedNotification, HttpStatus.CREATED);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<Void> deleteNotificationById(@PathVariable Long id) {
        clientNotificationService.deleteNotificationById(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
