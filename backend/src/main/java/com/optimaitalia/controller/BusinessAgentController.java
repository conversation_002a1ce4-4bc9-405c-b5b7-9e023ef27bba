package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.businessAgent.BusinessAgentRequest;
import com.optimaitalia.service.serviceImpl.BusinessAgentServiceImp;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(path = "/api/business-agent")
public class BusinessAgentController {

    private final BusinessAgentServiceImp businessAgentServiceImp;

    public BusinessAgentController(BusinessAgentServiceImp businessAgentServiceImp) {
        this.businessAgentServiceImp = businessAgentServiceImp;
    }

    @PostMapping("/information")
    @PreAuthorize("#request.getIdCliente()!=null&&@checkPreAuthorizeUtil.checkForUserRole(#request.getIdCliente(), authentication.principal.uid)")
    public List getInformationAboutAgent(@RequestBody BusinessAgentRequest request) {
        return businessAgentServiceImp.getInformationAboutAgent(request);
    }

}
