package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.model.JwtToken;
import com.optimaitalia.model.charts.ListaConsumiDettagliOre;
import com.optimaitalia.model.wrappers.services.EnergyDetailsByHoursRequest;
import com.optimaitalia.model.wrappers.services.InfoPod2G;
import com.optimaitalia.model.wrappers.services.PodDetail;
import com.optimaitalia.model.wrappers.services.PodDetailsRequest;
import com.optimaitalia.service.ElectricityService;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.Principal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/electricity")
public class ElectricityController {

    private final ElectricityService electricityService;

    private final OvalValidator ovalValidator;

    public ElectricityController(ElectricityService electricityService, OvalValidator ovalValidator) {
        this.electricityService = electricityService;
        this.ovalValidator = ovalValidator;

    }

    @PostMapping("/pod/details")
    public List<PodDetail> getPodDetail(@RequestBody PodDetailsRequest podDetailsRequest, Principal principal) throws ValidateException {
        ovalValidator.validate(podDetailsRequest);
        Long userId = Long.valueOf(((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid());
        if (podDetailsRequest.getPodRequests().stream().filter(i -> !i.getClientId().equals(userId)).collect(Collectors.toList()).isEmpty()) {
            return electricityService.findPodDetails(podDetailsRequest);
        }
        return null;
    }

    @PostMapping("/pod/2g-details")
    public List<InfoPod2G> getPod2GInfo(@RequestBody String[] podDetailsRequest) {
        return electricityService.getPod2GInfo(podDetailsRequest);
    }

    @GetMapping("/point/adjustments")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<Map> energyPointAdjustments(@RequestParam String clientId, @RequestParam Integer punto) {
        return electricityService.energyPointAdjustments(clientId, punto);
    }

    @PostMapping("/detailsByHours")
    @PreAuthorize("#detailsByHoursRequest.idConsumi!=null&&@checkPreAuthorizeUtil.checkForUserRole(#detailsByHoursRequest.idConsumi, authentication.principal.uid)")
    public List<ListaConsumiDettagliOre> energyDetailsByHours(@RequestBody EnergyDetailsByHoursRequest detailsByHoursRequest) {
        return electricityService.energyDetailsByHours(detailsByHoursRequest);
    }

    @PostMapping("/excel")
    @PreAuthorize("#detailsByHoursRequest.idConsumi!=null&&@checkPreAuthorizeUtil.checkForUserRole(#detailsByHoursRequest.idConsumi, authentication.principal.uid)")
    public ResponseEntity getEnergyDetailsInExcel(@RequestBody EnergyDetailsByHoursRequest detailsByHoursRequest) throws IOException {
        byte[] excelContent = electricityService.getFile(detailsByHoursRequest);
        File file = new File("Report.xlsx");
        FileUtils.writeByteArrayToFile(file, excelContent);
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + file.getName())
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .contentLength(file.length())
                .body(resource);
    }
}
