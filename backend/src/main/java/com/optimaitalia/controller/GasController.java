package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optima.security.model.JwtToken;
import com.optimaitalia.model.wrappers.gas.*;
import com.optimaitalia.service.GasService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/gas")
public class GasController {

    private final GasService gasService;

    public GasController(GasService gasService) {
        this.gasService = gasService;
    }

    @PostMapping("/pod/details")
    @PreAuthorize("#podDetails.cliente==authentication.principal.uid")
    public List<PodDetail> getPodDetail(@RequestBody PodDetailsRequest podDetails) throws ValidateException {
        return gasService.findPodDetails(podDetails);
    }

    @PostMapping("/pdr/additional/data")
    public ResponseEntity<List<PdrAdditionalData>> getPdrAdditionalData(@RequestBody List<PdrAdditionalDataRequest> request, Principal principal) {
        String clientId = ((JwtToken) ((UsernamePasswordAuthenticationToken) principal).getPrincipal()).getUid();
        if (request.size() != request.stream().filter(item -> clientId.equals(item.getCliente())).collect(Collectors.toList()).size()) {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }
        return ResponseEntity.ok(gasService.getPdrAdditionalData(request));
    }

    @GetMapping("/point/adjustments")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<GasPointAdjustment> gasPointAdjustments(@RequestParam String clientId, @RequestParam String pdr) {
        return gasService.gasPointAdjustments(clientId, pdr);
    }

}
