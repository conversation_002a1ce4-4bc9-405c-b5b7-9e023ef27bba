package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.voucherCard.*;
import com.optimaitalia.service.VoucherCardService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/api/voucher-card")
public class VoucherCardController {

    private final VoucherCardService voucherCardService;

    public VoucherCardController(VoucherCardService voucherCardService) {
        this.voucherCardService = voucherCardService;
    }

    @GetMapping
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public VoucherCardResponse checkVoucherCard(@RequestParam("voucher") String voucher, @RequestParam("clientId") Long clientId) {
        return voucherCardService.checkVoucherCard(voucher);
    }

    @PostMapping("/redeem")
    @PreAuthorize("#voucherRequest.user!=null&&@checkPreAuthorizeUtil.checkForUserRole(#voucherRequest.user, authentication.principal.uid)")
    public VoucherCardResponse redeemVoucherCard(@RequestBody RedeemVoucherRequest voucherRequest) {
        return voucherCardService.redeemVoucherCard(voucherRequest);
    }

    @PostMapping("/top-up-sim")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> redeemVoucherCardTopUpSim(@RequestBody TopUpSimByVoucher topUpSimByVoucher, @RequestParam("clientId") Long clientId) {
        return voucherCardService.redeemVoucherCardTopUpSim(topUpSimByVoucher);
    }

    @GetMapping("/information")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public UsedVouchersInformation getInformationAboutUserCards(@RequestParam("typeCard") String typeCard, @RequestParam("clientId") Long clientId) {
        return voucherCardService.getInformationAboutUserCards(typeCard, clientId);
    }

    @GetMapping("/contracts")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> getContractsInformation(@RequestParam("clientId") Long clientId) {
        return voucherCardService.getContractsInformation(clientId);
    }

    @GetMapping("/sim-balance")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public VoucherSimBalanceInformation getSimBalanceInformation(@RequestParam("simNumber") String simNumber, @RequestParam("clientId") String clientId) {
        return voucherCardService.getSimBalanceInformation(clientId, simNumber);
    }
}
