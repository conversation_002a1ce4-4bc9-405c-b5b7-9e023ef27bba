package com.optimaitalia.controller;

import com.optimaitalia.model.db.NotificaMdp;
import com.optimaitalia.service.NotificaMdpService;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/notifica")
public class NotificaMdpController {

    private final NotificaMdpService notificaMdpService;

    public NotificaMdpController(NotificaMdpService notificaMdpService) {
        this.notificaMdpService = notificaMdpService;
    }

    @GetMapping("/mdp/info")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<NotificaMdp> hasMdpNotification(@RequestParam Long clientId) {
        return notificaMdpService.getNotificationMdpInfo(clientId);
    }

    @PostMapping(path = "/mdp/new")
    public void addRecord(@RequestBody NotificaMdp notificaMdp) {
        notificaMdpService.save(notificaMdp);
    }

}
