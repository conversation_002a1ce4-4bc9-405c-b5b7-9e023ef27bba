package com.optimaitalia.controller;

import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AdminController {
    private final Environment environment;

    public AdminController(Environment environment) {
        this.environment = environment;
    }

    @GetMapping("/isAdmin")
    public ResponseEntity getadminPage() {
        if (environment.acceptsProfiles("preprod","dev")) {
            {
                return new ResponseEntity<>( HttpStatus.OK);
            }

        } else {
            return new ResponseEntity<>( HttpStatus.UNAUTHORIZED);
        }
    }
}

