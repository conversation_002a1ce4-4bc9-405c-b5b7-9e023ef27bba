package com.optimaitalia.controller;

import com.optima.security.model.OTPResponse;
import com.optima.security.model.wrappers.OtpDTO;
import com.optima.security.service.OTPService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/api/otp")
public class OTPController {

    private final OTPService otpService;

    public OTPController(OTPService otpService) {
        this.otpService = otpService;
    }

    @PostMapping("/{clientId}/ip/{ip}")
    public ResponseEntity<OTPResponse> sendOTP(@PathVariable("clientId") Long clientId, @PathVariable("ip") String ip) {
        return otpService.sendOTP(clientId, ip);
    }

    @GetMapping("/{clientId}/{otp}")
    public ResponseEntity<OtpDTO> getOTP(@PathVariable("clientId") Long clientId, @PathVariable("otp") String otp) {
        return otpService.loadByClientIdAndOtp(clientId, otp);
    }
}
