package com.optimaitalia.controller;


import com.optimaitalia.model.wrappers.questionsAndAnswers.FormAnswer;
import com.optimaitalia.service.QuestionsAndAnswers;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/questions")
public class QuestionsAndAnswersController {


    private final QuestionsAndAnswers questionsAndAnswers;

    public QuestionsAndAnswersController(QuestionsAndAnswers questionsAndAnswers) {
        this.questionsAndAnswers = questionsAndAnswers;
    }

    @PostMapping("/answers")
    public List<FormAnswer> getFormsAnswers() {
        return questionsAndAnswers.getFormsAnswers();
    }

}
