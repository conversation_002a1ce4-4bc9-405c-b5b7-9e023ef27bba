package com.optimaitalia.controller;

import com.optimaitalia.model.amazonprime.AmazonPrime;
import com.optimaitalia.service.AmazonPrimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping(path = "/api/amazon-prime")
public class AmazonPrimeController {

    private final AmazonPrimeService amazonPrimeService;

    @Autowired
    public AmazonPrimeController(AmazonPrimeService amazonPrimeService) {
        this.amazonPrimeService = amazonPrimeService;
    }

    @GetMapping(path = "/prime-data")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public AmazonPrime getAmazonPrimeData(@RequestParam("clientId") Long clientId) throws IOException {
        return amazonPrimeService.getAmazonPrimeData(clientId);
    }

    @GetMapping(path = "/unsubscribe")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public AmazonPrime unsubscribe(@RequestParam("clientId") Long clientId) {
        return amazonPrimeService.unsubscribe(clientId);
    }
}
