package com.optimaitalia.controller;

import com.optimaitalia.model.Contracts;
import com.optimaitalia.model.wrappers.voucherCard.RequestWrapper;
import com.optimaitalia.service.PDFService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/api")
public class PDFController {

    private final PDFService pdfService;

    public PDFController(PDFService pdfService) {
        this.pdfService = pdfService;
    }

    @GetMapping(path = "/pdf/{clientId}/{invoiceId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity downloadPDFFile(@PathVariable("invoiceId") Long invoiceId, @PathVariable("clientId") String clientId) {
        return pdfService.getFile(invoiceId, clientId);
    }

    @GetMapping(path = "/pdf/traffico/{clientId}/{invoiceId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity downloadTrafficoVoceFile(@PathVariable("invoiceId") Long invoiceId, @PathVariable("clientId") String clientId) {
        return pdfService.downloadTrafficoVoceFile(invoiceId, clientId);
    }

    @GetMapping(path = {"/transparency/pdf/{clientId}", "/transparency/pdf/{clientId}/{contractId}"})
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity downloadTariffTransparencyPDF(@PathVariable String clientId, @PathVariable(required = false) Long contractId) {
        return this.pdfService.downloadTariffTransparencyPDF(clientId, contractId);
    }

    @PostMapping("/contracts/pdf/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> downloadContractsPDF(@PathVariable String clientId, @RequestBody Contracts fileName) {
        return this.pdfService.downloadContractsPDF(fileName.getSpRelativeUri());
    }

    @GetMapping("/excel/report/{clientId}/{year}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> downloadYearlyReportExcelFile(@PathVariable String clientId, @PathVariable String year) {
        return this.pdfService.downloadYearlyReportExcelFile(clientId, year);
    }

    @PostMapping("/pdf/invoice/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> downloadInvoicePDF(@PathVariable String clientId, @RequestBody RequestWrapper request) {
        return this.pdfService.downloadInvoicePDF(request.getDownloadUrl());
    }

    @GetMapping("/excel/report/{fiscalCode}/{year}/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<?> downloadCondominiYearlyReportExcelFile(@PathVariable String clientId, @PathVariable String fiscalCode, @PathVariable String year) {
        return this.pdfService.downloadCondominiYearlyReportExcelFile(fiscalCode, year);
    }
}

