package com.optima.android.service.impl;

import com.optima.android.dto.CustomerFirebaseDTO;
import com.optima.android.model.CustomerFirebase;
import com.optima.android.repository.CustomerFirebaseRepository;
import com.optima.android.service.CustomerFirebaseService;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

@Service
public class CustomerFirebaseServiceImpl implements CustomerFirebaseService {

    private final CustomerFirebaseRepository customerFirebaseRepository;

    private final OvalValidator ovalValidator;

    public CustomerFirebaseServiceImpl(CustomerFirebaseRepository customerFirebaseRepository, OvalValidator ovalValidator) {
        this.customerFirebaseRepository = customerFirebaseRepository;
        this.ovalValidator = ovalValidator;
    }

    @Override
    public CustomerFirebase addCustomerFirebase(CustomerFirebase customerFirebase) throws ValidateException {
        ovalValidator.validate(customerFirebase);
        customerFirebase.setDate(new Date());
        customerFirebase.setDeleted(false);
        return customerFirebaseRepository.save(customerFirebase);
    }

    @Override
    public CustomerFirebase getLastCustomerFirebaseByCustomerId(String customerId) {
        return customerFirebaseRepository.findTop1ByCustomerIdOrderByDateDesc(customerId);
    }

    @Override
    @Transactional(propagation =  Propagation.SUPPORTS)
    public CustomerFirebaseDTO findNotDeletedByFirebaseId(String customerId) {
        return customerFirebaseRepository.findByFirebaseIdAndDeletedFalseOrDeletedIsNull(customerId)
                .map(CustomerFirebaseDTO::new).orElse(new CustomerFirebaseDTO(new CustomerFirebase()));
    }

    @Override
    public void logoutFirebase(String firebaseId) {
        Optional<CustomerFirebase> c = customerFirebaseRepository.findById(firebaseId);
        c.get().setDeleted(true);
        customerFirebaseRepository.save(c.get());
    }

}
