package com.optima.android.service;

import com.optima.android.dto.CustomerFirebaseDTO;
import com.optima.android.model.CustomerFirebase;
import com.optima.common.exceptions.ValidateException;

public interface CustomerFirebaseService {

    CustomerFirebase addCustomerFirebase(CustomerFirebase customerFirebase) throws ValidateException;

    CustomerFirebase getLastCustomerFirebaseByCustomerId(String customerId);

    CustomerFirebaseDTO findNotDeletedByFirebaseId(String firebaseId);

    void logoutFirebase(String firebaseId);

}
